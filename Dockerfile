# Production Dockerfile
FROM python:3.11-alpine

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    ENVIRONMENT=production

# Create a non-root user
RUN addgroup -S app && \
    adduser -S -G app app

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apk add --no-cache \
    build-base \
    gcc \
    postgresql-dev \
    curl \
    rust \
    cargo \
    # WeasyPrint dependencies
    pango \
    cairo \
    gdk-pixbuf \
    libffi-dev \
    musl-dev \
    fontconfig \
    ttf-dejavu \
    ttf-liberation \
    ttf-droid \
    # PDF image support
    py3-pillow \
    jpeg-dev \
    freetype-dev \
    # Other dependencies
    procps \
    # Browsers support (used in WeasyPrint HTML rendering)
    harfbuzz \
    nss

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --upgrade pip && \
    pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Make healthcheck script executable
RUN chmod +x /app/healthcheck.sh

# Change ownership of app directory to non-root user
RUN chown -R app:app /app

# Switch to non-root user
USER app

# Expose port
EXPOSE 8000

# Set health check with custom script for more reliable checks
HEALTHCHECK --interval=30s --timeout=30s --start-period=30s --retries=3 \
    CMD /app/healthcheck.sh

# Run the application with reduced workers since worker processes are dying
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000", "--workers", "2", "--proxy-headers"] 