import asyncio
import sys
import os

# Add the app directory to Python path
sys.path.append('/Users/<USER>/dev/relm/relm-intel-ai')

from app.agents.financials import FinancialServiceAgent, FinancialTaskInput
from app.services.property_service import PropertyService

async def test_simple_expenses():
    """Test expense calculations with a simple prompt to avoid policy violations."""
    
    # Use the same property as the user
    prop_id = "cf1857af-bccc-4547-aad3-047cf7c526e4"
    portfolio_id = "90db2602-2ed0-4f45-9de7-145f4052409c"
    
    # Initialize financial service agent
    financial_service = FinancialServiceAgent()
    
    print("=== Testing Expense Calculation with Simple Prompt ===")
    print(f"Property ID: {prop_id}")
    print(f"Portfolio ID: {portfolio_id}")
    
    # Test with projections enabled - simplified prompt
    result = await financial_service.agent.run(
        "Calculate the complete expense analysis for this property",
        deps=FinancialTaskInput(
            task_type="overall_expense",
            prop_id=prop_id,
            year=1,
            portfolio_id=portfolio_id,
            db=PropertyService(),
            return_projections=True,
            params={}
        )
    )
    
    print(f"\nAgent returned: {type(result.output)}")
    if hasattr(result.output, 'responses'):
        responses = result.output.responses
        print(f"Number of responses: {len(responses)}")
        
        # Check each response for additional_data
        projections_count = 0
        no_projections_count = 0
        
        print(f"\n📋 Detailed Results:")
        for i, response in enumerate(responses):
            has_projections = response.additional_data is not None
            if has_projections:
                projections_count += 1
                proj_years = list(response.additional_data.keys()) if response.additional_data else []
                print(f"  {i+1:2d}. {response.task_type:20s}: ✅ {len(proj_years)} projection years - ${response.money_value:.2f}")
            else:
                no_projections_count += 1
                print(f"  {i+1:2d}. {response.task_type:20s}: ❌ No projections - ${response.money_value:.2f}")
        
        print(f"\n📊 Summary:")
        print(f"  ✅ Responses WITH projections: {projections_count}")
        print(f"  ❌ Responses WITHOUT projections: {no_projections_count}")
        print(f"  📈 Total expense items: {len(responses)}")
        
        if projections_count == len(responses):
            print("  🎉 SUCCESS: All expenses have projection data!")
        else:
            print(f"  ⚠️  ISSUE: {no_projections_count} expenses missing projection data")
            
        # Show a sample projection to verify structure
        if responses and responses[0].additional_data:
            print(f"\n🔍 Sample projection structure for {responses[0].task_type}:")
            for year, projection in responses[0].additional_data.items():
                print(f"    {year}: ${projection.money_value:.2f}")
    
    return result

if __name__ == "__main__":
    asyncio.run(test_simple_expenses()) 