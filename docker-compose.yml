services:
  api:
    # Use built image in development
    build:
      context: .
      dockerfile: Dockerfile
    image: relm-intelligence-ai:latest
    container_name: relm-api
    ports:
      - "8000:8000"
    volumes:
      - .:/app
    env_file:
      - .env
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s
    restart: unless-stopped
    profiles: ["dev", "prod"]
    # Development command - use this for local development
    command: uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload --no-access-log

  api-prod:
    # Use pre-built image from registry in production
    image: ${DOCKER_IMAGE}
    container_name: relm-api-prod
    ports:
      - "8000:8000"
    env_file:
      - .env
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s
    restart: unless-stopped
    profiles: ["prod"]
    # Production command - optimized for performance
    command: uvicorn app.main:app --host 0.0.0.0 --port 8000 --workers 4 --proxy-headers --no-access-log

networks:
  default:
    driver: bridge

# Usage:
# Development mode:  docker-compose --profile dev up
# Production mode:   docker-compose --profile prod up api-prod 