[[source]]
url = "https://pypi.org/simple"
verify_ssl = true
name = "pypi"

[packages]
fastapi = "*"
uvicorn = {extras = ["standard"], version = "*"}
python-dotenv = "*"
pydantic = "*"
pydantic-settings = "*"
email-validator = "*"
python-multipart = "*"
websockets = "*"
starlette = "*"
sse-starlette = "*"
httpx = "*"
supabase = "*"
pyjwt = "*"
redis = "*"
asyncpg = "*"
sqlalchemy = "*"
openai = "*"
cohere = "*"
instructor = "*"
turbopuffer = "*"
pydantic-ai = "*"
langchain = ">=0.3.23"
langchain-core = ">=0.3.51"
langchain-text-splitters = ">=0.3.8"
langchain-community = ">=0.3.0"
numpy = ">=1.26.0"
pandas = ">=2.2.0"
aiohttp = ">=3.11.0"
requests = "*"
sniffio = "*"
tenacity = "*"
loguru = "*"
six = "*"
typing-extensions = "*"
typed-ast = "*"
uc-micro-py = "*"
pytest = "*"
pytest-asyncio = "*"
pytest-cov = "*"
pytest-mock = "*"
black = "*"
isort = "*"
mypy = "*"
ruff = "*"
pip-tools = "*"
sentry-sdk = "*"
prometheus-client = "*"
opentelemetry-sdk = "*"
opentelemetry-exporter-otlp = "*"
boto3 = "*"
awscli = "*"
pypdf = "*"
pypdf2 = "*"
weasyprint = "*"
tavily-python = "*"
pydantic-ai-slim = {extras = ["tavily"], version = "*"}
logfire = "*"
stripe = "*"
nomic = "*"
fredapi = "*"
scrapingbee = "*"
numpy-financial = "*"

[dev-packages]

[requires]
python_version = "3.11"
