import asyncio
import sys
import os

# Add the app directory to Python path
sys.path.append('/Users/<USER>/dev/relm/relm-intel-ai')

from app.agents.financials import FinancialServiceAgent, FinancialTaskInput
from app.services.property_service import PropertyService

async def test_agent_preserves_projections():
    """Test that the financial agent preserves additional_data when return_projections=True."""
    
    # Known property ID
    prop_id = "9af6986f-800f-4452-80bb-c314c7fb82e0"
    portfolio_id = "test_portfolio"
    
    # Initialize financial service agent
    financial_service = FinancialServiceAgent()
    
    print("=== Testing Financial Agent with return_projections=True ===")
    
    # Test with projections enabled
    result = await financial_service.agent.run(
        f"Calculate overall expenses for property {prop_id}",
        deps=FinancialTaskInput(
            task_type="overall_expense",
            portfolio_id=portfolio_id,
            prop_id=prop_id,
            year=1,
            db=PropertyService(),
            return_projections=True,
            params={}
        )
    )
    
    print(f"Agent returned: {type(result.output)}")
    if hasattr(result.output, 'responses'):
        print(f"Number of responses: {len(result.output.responses)}")
        
        # Check each response for additional_data
        projections_count = 0
        no_projections_count = 0
        
        for i, response in enumerate(result.output.responses):
            has_projections = response.additional_data is not None
            if has_projections:
                projections_count += 1
                proj_years = list(response.additional_data.keys()) if response.additional_data else []
                print(f"  {i+1}. {response.task_type}: additional_data = {len(proj_years)} years")
            else:
                no_projections_count += 1
                print(f"  {i+1}. {response.task_type}: additional_data = None ❌")
        
        print(f"\n📊 Summary:")
        print(f"  ✅ Responses WITH projections: {projections_count}")
        print(f"  ❌ Responses WITHOUT projections: {no_projections_count}")
        
        if projections_count == len(result.output.responses):
            print("  🎉 SUCCESS: All expenses have projection data!")
        else:
            print(f"  ⚠️  ISSUE: {no_projections_count} expenses missing projection data")
    
    return result

if __name__ == "__main__":
    asyncio.run(test_agent_preserves_projections()) 