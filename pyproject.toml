[build-system]
requires = ["setuptools>=42", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "relm-intelligence-ai"
version = "0.1.0"
description = "WebSocket-based chatbot API with AI service integration"
authors = [
    {name = "Relm Intelligence Team", email = "<EMAIL>"}
]
readme = "README.md"
license = {text = "MIT"}
requires-python = ">=3.10"
classifiers = [
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
]
dependencies = [
    "fastapi>=0.104.0",
    "uvicorn[standard]>=0.23.0",
    "pydantic>=2.4.0",
    "redis>=4.6.0",
    "supabase>=1.0.0",
    "openai>=1.0.0",
    "pydantic-ai>=0.1.3",
]

[project.optional-dependencies]
dev = [
    "black",
    "isort",
    "mypy",
    "ruff",
    "pytest",
    "pytest-asyncio",
    "pytest-cov",
]
production = [
    "sentry-sdk",
    "prometheus-client",
    "opentelemetry-sdk",
    "opentelemetry-exporter-otlp",
]

[tool.setuptools]
package-dir = {"" = "app"}
packages = ["api", "models", "repository", "services", "tests", "utils"]

[tool.black]
line-length = 100
target-version = ["py310", "py311"]
include = '\.pyi?$'
exclude = '''
/(
    \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | _build
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
line_length = 100
multi_line_output = 3
include_trailing_comma = true
force_grid_wrap = 0
use_parentheses = true
ensure_newline_before_comments = true

[tool.mypy]
python_version = "3.10"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
disallow_untyped_decorators = true
check_untyped_defs = true
disallow_any_generics = false
no_implicit_reexport = true
strict_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
plugins = ["pydantic.mypy"]

[[tool.mypy.overrides]]
module = "tests.*"
disallow_untyped_defs = false
disallow_incomplete_defs = false

[tool.ruff]
line-length = 100
target-version = "py310"
select = ["E", "F", "B", "I", "N", "UP", "C4", "SIM", "Q"]
ignore = ["E203"]
exclude = [
    ".git",
    ".mypy_cache",
    ".ruff_cache",
    ".venv",
    "venv",
    "__pycache__",
    "build",
    "dist",
]

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = "test_*.py"
python_functions = "test_*"
pythonpath = "."
asyncio_mode = "auto" 