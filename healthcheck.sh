#!/bin/sh
# Healthcheck script for the Relm Intelligence API

# Set default variables
MAX_RETRIES=3
RETRY_WAIT=2
HOST=${HEALTH_CHECK_HOST:-localhost}
PORT=${HEALTH_CHECK_PORT:-8000}
PATH=${HEALTH_CHECK_PATH:-/health}

# Function to check health endpoint
check_health() {
  # Use curl with a 5 second timeout to check the health endpoint
  response=$(curl -s -m 5 -o /dev/null -w "%{http_code}" http://${HOST}:${PORT}${PATH})
  
  # Check if response is 200 OK
  if [ "$response" = "200" ]; then
    echo "Health check successful"
    return 0
  else
    echo "Health check failed with status $response"
    return 1
  fi
}

# Try health check with retries
attempt=1
while [ $attempt -le $MAX_RETRIES ]; do
  echo "Health check attempt $attempt/$MAX_RETRIES..."
  
  if check_health; then
    # Success
    exit 0
  fi
  
  # Wait before retrying
  if [ $attempt -lt $MAX_RETRIES ]; then
    echo "Waiting ${RETRY_WAIT} seconds before retrying..."
    sleep $RETRY_WAIT
  fi
  
  attempt=$((attempt + 1))
done

# All retries failed
echo "Health check failed after $MAX_RETRIES attempts"
exit 1 