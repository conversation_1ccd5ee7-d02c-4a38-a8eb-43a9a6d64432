.PHONY: setup clean lint format test test-cov dev prod docker-build docker-run docker-compose deploy up down

# Development environment setup
setup:
	python -m pip install --upgrade pip
	pip install -r requirements.txt
	pip install -e .

# Clean up environment
clean:
	rm -rf .pytest_cache .coverage htmlcov .mypy_cache .ruff_cache __pycache__ app/__pycache__ \
	app/**/__pycache__ tests/__pycache__ tests/**/__pycache__ dist build *.egg-info

# Linting and code quality
lint:
	isort app tests
	black app tests
	ruff app tests
	mypy app tests

# Format code
format:
	isort app tests
	black app tests

# Run tests
test:
	pytest tests -v

# Run tests with coverage
test-cov:
	pytest tests --cov=app --cov-report=html

# Run development server
dev:
	uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

# Run production server
prod:
	uvicorn app.main:app --host 0.0.0.0 --port 8000 --workers 4

# Docker operations
docker-build:
	docker build -t relm-intelligence-ai .

docker-run:
	docker run -p 8000:8000 --env-file .env relm-intelligence-ai

# Run docker-compose
docker-compose:
	docker-compose up

# Start application using docker-compose in development mode
up:
	docker-compose --profile dev up --build

# Stop and remove containers, networks created by up
down:
	docker-compose --profile dev down

# Deploy to AWS ECS
deploy:
	aws ecr get-login-password --region $(AWS_REGION) | docker login --username AWS --password-stdin $(ECR_REGISTRY)
	# Build and push the image for linux/amd64 platform required by Fargate
	docker buildx build --platform linux/amd64 --push -t $(ECR_REGISTRY)/$(ECR_REPOSITORY):latest .

# Generate requirements.txt from pyproject.toml
requirements:
	pip-compile --output-file=requirements.txt pyproject.toml --extra=dev --extra=production

# Use pip-compile to sync packages with requirements.txt
sync:
	pip-sync requirements.txt

# Usage:
# Development mode:  docker-compose --profile dev up
# Production mode:   docker-compose --profile prod up api-prod 