# Web Frameworks
fastapi
uvicorn[standard]
starlette
python-multipart
websockets
sse-starlette

# Data Validation and Settings
pydantic
pydantic-settings
email-validator
python-dotenv

# Document Processing
pypdf
PyPDF2
weasyprint==65.1

# Database and ORM
asyncpg
sqlalchemy
supabase

# Authentication
pyjwt

# Caching
redis

# AI Services
openai
cohere
nomic
logfire
logfire[fastapi]
instructor
turbopuffer
pydantic-ai
duckduckgo-search
langchain>=0.3.23
langchain-core>=0.3.51
langchain-text-splitters>=0.3.8
langchain-community>=0.3.0

# Search
duckduckgo-search
tavily-python

# Data Processing and Analysis
numpy>=1.26.0
numpy-financial
pandas>=2.2.0
aiohttp>=3.11.0
rapidfuzz>=3.0.0
fredapi>=0.5.2

# HTTP and Networking
requests
sniffio
httpx
scrapingbee

# Utilities
tenacity
loguru
six
typing-extensions
typed-ast
uc-micro-py

# Testing
pytest
pytest-asyncio
pytest-cov
pytest-mock

# Development tools
black
isort
mypy
ruff
pip-tools

# Monitoring/Observability
sentry-sdk
prometheus-client
opentelemetry-sdk
opentelemetry-exporter-otlp

# AWS
boto3
awscli

# Payments
stripe