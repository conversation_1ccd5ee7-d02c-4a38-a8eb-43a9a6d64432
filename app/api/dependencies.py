"""
Dependency providers for API routes.
"""
from typing import Dict, Any, Optional

from fastapi.security import HTTPAuthorizationCredentials
from fastapi import Request, HTTPException, status, Security

from app.utils.logger import get_logger
from app.utils.auth import supabase_auth, oauth2_scheme, get_token_from_header

logger = get_logger(__name__)

async def get_current_user(request: Request) -> Dict[str, Any]:
    """
    Dependency to get the authenticated user.
    This can be used in individual routes if needed to access user data.
    
    Args:
        request: The request object
        
    Returns:
        Dict containing user data from JWT
        
    Raises:
        HTTPException: If authentication fails
    """
    try:
        # Get user from request state if middleware has already authenticated
        if hasattr(request.state, "user"):
            return request.state.user
            
        # Otherwise, validate the token
        return await supabase_auth(request)
    except Exception as e:
        logger.warning(f"Authentication failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Authentication required",
            headers={"WWW-Authenticate": "Bearer"}
        )

async def get_optional_user(request: Request) -> Optional[Dict[str, Any]]:
    """
    Dependency to get the authenticated user if available, but don't require auth.
    
    Args:
        request: The request object
        
    Returns:
        Dict containing user data from JWT or None if no auth
    """
    try:
        # Get user from request state if middleware has already authenticated
        if hasattr(request.state, "user"):
            return request.state.user
            
        # Otherwise, try to validate the token but don't raise if it fails
        return await supabase_auth.__call__(request, auto_error=False)
    except Exception:
        return None

def get_token_data(credentials: HTTPAuthorizationCredentials = Security(oauth2_scheme)) -> Dict[str, Any]:
    """
    Get validated token data from authorization header.
    This is a simpler version that works well with Swagger UI's Authorize button.
    
    Args:
        credentials: The bearer token credentials
        
    Returns:
        Dict containing the token payload
    """
    return get_token_from_header(credentials) 