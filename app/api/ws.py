"""
WebSocket API endpoints module.
"""
import jwt
import json
import uuid

from uuid import UUID
from jwt.exceptions import Py<PERSON><PERSON><PERSON>rror
from typing import Dict, Optional, List
from fastapi import APIRouter, WebSocket, WebSocketDisconnect, status

from app.utils.logger import get_logger
from app.utils.env_cache import get_env
from app.repository.cache import redis_cache
from app.services.chat_service import chat_service
from app.services.agent_service import agent_service
from app.models.message import Message, MessageContent, MessageRole

logger = get_logger(__name__)

router = APIRouter()

# Use cached getter
SUPABASE_JWT_SECRET = get_env("SUPABASE_JWT_SECRET")
if not SUPABASE_JWT_SECRET:
    raise ValueError("SUPABASE_JWT_SECRET environment variable is required")


class ConnectionManager:
    """
    Manager for WebSocket connections.
    """
    
    def __init__(self):
        """Initialize the connection manager."""
        self.active_connections: Dict[str, WebSocket] = {}
    
    async def connect(self, websocket: WebSocket, connection_id: str) -> None:
        """
        Connect a WebSocket client.
        
        Args:
            websocket: The WebSocket connection
            connection_id: Unique connection identifier (chat_id + user_id)
        """
        await websocket.accept()
        self.active_connections[connection_id] = websocket
        logger.info(f"Client connected: {connection_id}")
    
    async def disconnect(self, connection_id: str) -> None:
        """
        Disconnect a WebSocket client.
        
        Args:
            connection_id: Unique connection identifier
        """
        if connection_id in self.active_connections:
            del self.active_connections[connection_id]
            logger.info(f"Client disconnected: {connection_id}")
    
    async def send_message(self, connection_id: str, message: str) -> None:
        """
        Send a message to a specific client.
        
        Args:
            connection_id: Client to send the message to
            message: Message to send
        """
        if connection_id in self.active_connections:
            websocket = self.active_connections[connection_id]
            await websocket.send_text(message)


# Create a connection manager instance
manager = ConnectionManager()

async def validate_token(websocket: WebSocket, token: str) -> Optional[Dict]:
    """
    Validate JWT token for WebSocket connections.
    
    Args:
        websocket: The WebSocket connection
        token: JWT token to validate
        
    Returns:
        Dict containing user claims or None if invalid
    """
    try:
        # Decode the JWT token
        payload = jwt.decode(
            token, 
            SUPABASE_JWT_SECRET, 
            algorithms=["HS256"],
            options={"verify_signature": True, "verify_aud": False}
        )
        return payload
    except PyJWTError as e:
        logger.warning(f"Invalid WebSocket JWT token: {str(e)}")
        await websocket.close(code=status.WS_1008_POLICY_VIOLATION, reason="Invalid authentication token")
        return None

@router.websocket("/ws/{chat_id}")
async def websocket_endpoint(
    websocket: WebSocket,
    chat_id: str
):
    """
    WebSocket endpoint for real-time chat.
    
    Args:
        websocket: The WebSocket connection
        chat_id: Chat ID for message tracking
    """
    # Extract token from query parameters or headers
    token = websocket.query_params.get("token") or websocket.headers.get("authorization", "").replace("Bearer ", "")
    
    if not token:
        logger.warning("Missing authentication token")
        await websocket.close(code=status.WS_1008_POLICY_VIOLATION, reason="Missing authentication token")
        return
        
    # Validate token
    user_claims = await validate_token(websocket, token)
    if not user_claims:
        logger.warning("Token validation failed")
        # Connection already closed in validate_token if invalid
        return
    
    # Extract user_id from JWT token
    user_id = user_claims.get("sub")
    if not user_id:
        logger.warning("Invalid or missing user ID in token")
        await websocket.close(code=status.WS_1008_POLICY_VIOLATION, reason="Invalid user ID in token")
        return
    
    # Get chat data and verify user is a member of this chat
    try:
        # Use the new access control method instead of manual checking
        has_access = await chat_service.check_chat_access(chat_id, user_id)
        if not has_access:
            logger.warning(f"User {user_id} denied access to chat {chat_id}")
            await websocket.close(
                code=status.WS_1008_POLICY_VIOLATION, 
                reason="Access denied to this chat"
            )
            return

        # Get chat info including portfolio_id for context loading
        chat_data = await chat_service.get_chat(chat_id)
        if not chat_data:
            logger.warning(f"Chat not found: {chat_id}")
            await websocket.close(
                code=status.WS_1008_POLICY_VIOLATION, 
                reason="Chat not found"
            )
            return
            
        # Extract portfolio_id for later use
        portfolio_id = str(chat_data.get("portfolio_id", ""))
        if not portfolio_id:
            logger.warning(f"No portfolio_id found in chat_data: {chat_data}")
            await websocket.close(
                code=status.WS_1008_POLICY_VIOLATION, 
                reason="No portfolio associated with this chat"
            )
            return
            
        logger.info(f"Portfolio ID: {portfolio_id}")
        logger.info(f"User {user_id} granted access to chat {chat_id}")
            
    except Exception as e:
        logger.error(f"Error verifying chat access: {str(e)}")
        await websocket.close(
            code=status.WS_1008_POLICY_VIOLATION,
            reason="Error verifying chat access"
        )
        return
        
    # Create a unique connection ID using chat_id and user_id
    connection_id = f"{chat_id}:{user_id}"
    
    # Connect the WebSocket client
    await manager.connect(websocket, connection_id)
    
    # Message history for context
    message_history: List[Message] = []
    
    # Load previous messages for context
    try:
        if chat_data and "messages" in chat_data:
            # Convert DB messages to Message objects for context
            for msg_data in chat_data["messages"]:
                # Handle attachments and metadata
                image_urls = None
                metadata = None
                
                # Extract image URLs from attachments if present
                if msg_data.get("attachments") and isinstance(msg_data["attachments"], dict):
                    image_urls = msg_data["attachments"].get("image_urls")
                
                # Extract metadata if present
                if msg_data.get("metadata") and isinstance(msg_data["metadata"], dict):
                    metadata = msg_data["metadata"]
                
                msg = Message(
                    id=f"msg_{uuid.uuid4().hex[:10]}",
                    role=MessageRole.USER if msg_data["role"] == "user" else MessageRole.ASSISTANT,
                    content=MessageContent(
                        text=msg_data["message"],
                        image_urls=image_urls,
                        metadata=metadata
                    ),
                    user_id=UUID(msg_data["user_id"]),
                    chat_id=UUID(msg_data["chat_id"]),
                    timestamp=msg_data["created_at"]
                )
                message_history.append(msg)
                
            # Limit history size for performance (optional)
            if len(message_history) > 20:
                message_history = message_history[-20:]
                
            logger.info(f"Loaded {len(message_history)} messages from chat {chat_id}")
    except Exception as e:
        logger.warning(f"Error loading chat history: {str(e)}")
    
    try:
        # Main WebSocket loop
        while True:
            # Receive JSON message from the client
            data = await websocket.receive_text()
            message_data = json.loads(data)
            
            # Extract image URLs and metadata from the message
            # CHANGED: Assume image_urls are already uploaded to Supabase bucket by the frontend
            image_urls = message_data.get("image_urls", [])
            metadata = message_data.get("metadata", None)
            
            # Create a Message object from the received data
            user_message = Message(
                id=f"msg_{uuid.uuid4().hex[:10]}",
                role=MessageRole.USER,
                content=MessageContent(
                    text=message_data.get("text", ""),
                    image_urls=image_urls,  # CHANGED: Use image URLs directly from the frontend
                    metadata=metadata
                ),
                user_id=UUID(user_id),
                chat_id=UUID(chat_id)
            )
            
            # Add message to history for context
            message_history.append(user_message)
            
            await websocket.send_text(json.dumps({
                "type": "typing_indicator", 
                "status": "started"
            }))
            
            # Store message in chat_messages table
            try:
                await chat_service.save_message(str(chat_id), user_message)
                
                # Trigger background chat rename if needed (every 3rd user message)
                # This runs asynchronously and won't block the WebSocket response
                await agent_service.trigger_chat_rename_if_needed(str(chat_id))
                
                # Clear cache for this chat
                chat_cache_key = f"chat_messages:{chat_id}"
                await redis_cache.redis.delete(chat_cache_key)
                
                # Also clear user-specific portfolio chat cache
                portfolio_cache_key = f"chat_list:{portfolio_id}:{user_id}"  # Add user_id
                await redis_cache.redis.delete(portfolio_cache_key)
                
                # Also clear user-specific portfolio chats cache
                portfolio_chats_cache_key = f"portfolio_chats:{portfolio_id}:{user_id}"  # Add user_id
                await redis_cache.redis.delete(portfolio_chats_cache_key)
            except Exception as e:
                logger.error(f"Error storing message in database: {str(e)}")
            
            # Generate AI response using message history and PydanticAI agent
            # Pass portfolio_id for document retrieval
            ai_response = await agent_service.generate_response(message_history, portfolio_id=portfolio_id, websocket=websocket, message_id=user_message.id)
            
            # Add AI response to history for context
            message_history.append(ai_response)
            
            await websocket.send_text(json.dumps({
                "type": "typing_indicator", 
                "status": "stopped"
            }))
            
            # Store AI response in chat_messages table too
            try:
                ai_message = Message(
                    id=f"msg_{uuid.uuid4().hex[:10]}",
                    role=MessageRole.ASSISTANT,
                    content=MessageContent(text=ai_response.content.text),
                    user_id=UUID(user_id),  # Use the same user_id as original sender
                    chat_id=UUID(chat_id)
                )
                
                await chat_service.save_message(str(chat_id), ai_message)
                
                # Clear cache again after AI response (to ensure latest state is cached next time)
                chat_cache_key = f"chat_messages:{chat_id}"
                await redis_cache.redis.delete(chat_cache_key)
                
                # Also clear user-specific portfolio chat cache
                portfolio_cache_key = f"chat_list:{portfolio_id}:{user_id}"  # Add user_id
                await redis_cache.redis.delete(portfolio_cache_key)
                
                # Also clear user-specific portfolio chats cache
                portfolio_chats_cache_key = f"portfolio_chats:{portfolio_id}:{user_id}"  # Add user_id
                await redis_cache.redis.delete(portfolio_chats_cache_key)
            except Exception as e:
                logger.error(f"Error storing AI response in database: {str(e)}")
            
            # Send the AI response back to the client
            await websocket.send_text(json.dumps({
                "id": ai_response.id,
                "role": ai_response.role.value,
                "content": {"text": ai_response.content.text},
                "timestamp": ai_response.timestamp.isoformat()
            }))
            
    except WebSocketDisconnect:
        # Handle disconnection
        await manager.disconnect(connection_id)
    except Exception as e:
        # Handle other exceptions
        logger.error(f"WebSocket error: {str(e)}")
        await manager.disconnect(connection_id) 