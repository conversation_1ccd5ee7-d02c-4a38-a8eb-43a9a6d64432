"""
Tests for the agent service using PydanticAI.
"""
import pytest
from unittest.mock import patch, AsyncMock, MagicMock

from app.services.agent_service import AgentService, AgentDependencies
from app.models.message import Message, MessageContent, MessageRole

@pytest.fixture
def agent_service():
    """Create an agent service for testing."""
    return AgentService(model_name="gpt-4.1-mini")

@pytest.fixture
def mock_message_history():
    """Create a mock message history for testing."""
    return [
        Message(
            id="msg_1234567890",
            role=MessageRole.USER,
            content=MessageContent(text="Hello, can you tell me about my properties?"),
            user_id=None,
            chat_id=None
        )
    ]

@pytest.fixture
def mock_db_client(mocker):
    """Mock the get_db_client function."""
    mock_client = MagicMock()
    mock_client.table = MagicMock()
    mocker.patch('app.repository.db.get_db_client', return_value=mock_client)
    return mock_client

@pytest.mark.asyncio
@patch('app.services.agent_service.Agent.run')
async def test_generate_response_success(mock_run, agent_service, mock_message_history):
    """Test successful response generation."""
    # Set up mock return value
    mock_result = AsyncMock()
    mock_result.output = "I'd be happy to tell you about your properties!"
    mock_run.return_value = mock_result
    
    # Call the function
    response = await agent_service.generate_response(mock_message_history, portfolio_id="portfolio123")
    
    # Assertions
    assert response is not None
    assert response.role == MessageRole.ASSISTANT
    assert response.content.text == "I'd be happy to tell you about your properties!"
    
    # Verify the agent was called correctly
    mock_run.assert_called_once()
    args, kwargs = mock_run.call_args
    # First arg should be the user query
    assert args[0] == "Hello, can you tell me about my properties?"
    # Check that deps were correctly passed
    assert isinstance(kwargs['deps'], AgentDependencies)
    assert kwargs['deps'].portfolio_id == "portfolio123"
    assert len(kwargs['deps'].chat_history) == 1

@pytest.mark.asyncio
@patch('app.services.agent_service.Agent.run', side_effect=Exception("Test error"))
async def test_generate_response_error(mock_run, agent_service, mock_message_history):
    """Test error handling during response generation."""
    # Call the function
    response = await agent_service.generate_response(mock_message_history, portfolio_id="portfolio123")
    
    # Assertions
    assert response is not None
    assert response.role == MessageRole.ASSISTANT
    assert "I'm sorry, I'm having trouble processing your request" in response.content.text 

@pytest.mark.asyncio
@patch('app.services.agent_service.PydanticOutputParser')
async def test_generate_property_report(mock_parser, agent_service, mock_db_client):
    """Test generating a property report."""
    # Setup mock property data
    property_id = "test-property-123"
    
    # Configure mock responses for database calls
    def table_side_effect(table_name):
        mock_table_response = MagicMock()
        mock_method_response = MagicMock()
        mock_execute_response = MagicMock()
        
        if table_name == "prop":
            # Mock property data
            mock_execute_response.data = [{"id": property_id, "address_id": "test-addr-123"}]
        elif table_name == "prop_addresses":
            # Mock address data
            mock_execute_response.data = [{"id": "test-addr-123", "address": "123 Test St"}]
        else:
            # Empty data for other tables
            mock_execute_response.data = []
        
        mock_method_response.execute.return_value = mock_execute_response
        mock_table_response.select.return_value.eq.return_value = mock_method_response
        return mock_table_response
    
    mock_db_client.table.side_effect = table_side_effect
    
    # Mock the AI response
    mock_parser_instance = MagicMock()
    mock_parser_instance.parse_with_prompt.return_value = {"summary": "Test summary"}
    mock_parser.return_value = mock_parser_instance
    
    # Call the function
    result = await agent_service.generate_property_report(property_id)
    
    # Assertions
    assert result is not None
    assert "summary" in result 