import pytest
from unittest.mock import MagicMock
from uuid import uuid4
from datetime import datetime

from app.repository.db import connection_manager
from app.services.property_service import PropertyService
from app.models.database import Property
from app.services.chat_service import ChatService

# Mock the connection manager for testing
@pytest.fixture
def mock_connection_manager(mocker):
    """Mock the connection manager for testing."""
    # Create a mock client
    mock_client = MagicMock()
    mock_client.table = MagicMock()
    
    # Mock chainable methods like table(...).insert(...).execute()
    # Mock table() to return an object that has insert(), select(), update(), delete()
    mock_table_response = MagicMock()
    mock_client.table.return_value = mock_table_response

    # Mock methods on the table response object to return an object with execute()
    mock_method_response = MagicMock()
    mock_table_response.insert.return_value = mock_method_response
    mock_table_response.select.return_value = mock_method_response
    mock_table_response.update.return_value = mock_method_response
    mock_table_response.delete.return_value = mock_method_response
    mock_method_response.eq.return_value = mock_method_response # For .eq()
    mock_method_response.order.return_value = mock_method_response # For .order()

    # Mock execute() to return a mock response object with a 'data' attribute
    mock_execute_response = MagicMock()
    mock_execute_response.data = [] # Default to empty data
    mock_method_response.execute.return_value = mock_execute_response
    
    # Mock connection_manager.get_client to return our mock client
    mocker.patch.object(connection_manager, 'get_client', return_value=mock_client)
    
    mocker.patch('app.repository.db.get_db_client', return_value=mock_client)
    
    return mock_client

@pytest.fixture
def property_service_fixture(mock_connection_manager):
    """Fixture to create a PropertyService instance with a mocked client."""
    # Create service that will use the mocked connection manager
    service = PropertyService()
    return service

@pytest.fixture
def chat_service(mock_connection_manager):
    """Fixture to create a ChatService instance with a mocked client."""
    # Create service that will use the mocked connection manager
    service = ChatService()
    return service

@pytest.mark.asyncio
async def test_get_property_not_found(property_service_fixture, mock_connection_manager):
    """Test getting a property that does not exist."""
    property_id = str(uuid4())
    
    # Configure mock execute response for property select to return empty data
    mock_execute_response = MagicMock()
    mock_execute_response.data = []
    mock_connection_manager.table.return_value.select.return_value.eq.return_value.execute.return_value = mock_execute_response
    
    prop = await property_service_fixture.get_property(property_id)
    
    assert prop is None
    mock_connection_manager.table.assert_called_with("prop")
    mock_connection_manager.table.return_value.select.assert_called_once_with("*")
    mock_connection_manager.table.return_value.select.return_value.eq.assert_called_once_with("id", property_id)

@pytest.mark.asyncio
async def test_get_property_found(property_service_fixture, mock_connection_manager):
    """Test getting an existing property."""
    property_id_uuid = uuid4()
    address_id_uuid = uuid4()
    portfolio_id_uuid = uuid4()
    
    # Correct mock_prop_data based on Property model fields
    mock_prop_data = {
        "id": property_id_uuid, 
        "portfolio_id": portfolio_id_uuid, 
        "address_id": address_id_uuid, 
        "main_img_url": "http://example.com/image.jpg",
        "created_at": datetime.utcnow(), # Pydantic will handle conversion if needed
        "updated_at": datetime.utcnow(),
        "is_deleted": False,
    }

    # Configure mock execute response for property select
    mock_execute_response = MagicMock()
    # Pydantic expects the raw data types (UUID, datetime) here
    mock_execute_response.data = [mock_prop_data] 
    mock_connection_manager.table.return_value.select.return_value.eq.return_value.execute.return_value = mock_execute_response
    
    # Pass string representation of UUID
    prop = await property_service_fixture.get_property(str(property_id_uuid)) 
    
    assert prop is not None
    assert isinstance(prop, Property)
    assert prop.id == property_id_uuid # Compare UUID objects
    assert prop.address_id == address_id_uuid
    assert prop.portfolio_id == portfolio_id_uuid
    assert prop.main_img_url == "http://example.com/image.jpg"

@pytest.mark.asyncio
async def test_create_chat(chat_service, mock_connection_manager):
    """Test creating a new chat with ChatService."""
    portfolio_id = str(uuid4())
    user_id = str(uuid4())
    
    # Create the chat
    chat = await chat_service.create_chat(portfolio_id, user_id)
    
    assert "id" in chat
    assert chat["portfolio_id"] == portfolio_id
    assert chat["created_by"] == user_id
    
    # Verify Supabase calls
    assert mock_connection_manager.table.call_count == 2 # chat and chat_members
    mock_connection_manager.table.assert_any_call("chat")
    mock_connection_manager.table.assert_any_call("chat_members")

@pytest.mark.asyncio
async def test_get_chat_not_found(chat_service, mock_connection_manager):
    """Test getting a chat that does not exist."""
    chat_id = str(uuid4())
    
    # Configure mock execute response for chat select to return empty data
    mock_execute_response = MagicMock()
    mock_execute_response.data = []
    mock_connection_manager.table.return_value.select.return_value.eq.return_value.execute.return_value = mock_execute_response
    
    chat = await chat_service.get_chat(chat_id)
    
    assert chat is None
    mock_connection_manager.table.assert_called_with("chat")
    mock_connection_manager.table.return_value.select.assert_called_once_with("*")
    mock_connection_manager.table.return_value.select.return_value.eq.assert_called_once_with("id", chat_id)

@pytest.mark.asyncio
async def test_get_chat_found(chat_service, mock_connection_manager):
    """Test getting an existing chat."""
    chat_id = str(uuid4())
    user_id = str(uuid4())
    mock_chat_data = {"id": chat_id, "portfolio_id": str(uuid4()), "created_by": user_id, "is_deleted": False, "created_at": datetime.utcnow().isoformat()}
    mock_message_data = [{"id": str(uuid4()), "chat_id": chat_id, "user_id": user_id, "message": "Hello", "role": "user", "created_at": datetime.utcnow().isoformat()}]
    mock_member_data = [{"id": str(uuid4()), "user_id": user_id, "chat_id": chat_id, "role": "owner", "added_at": datetime.utcnow().isoformat()}]

    # Mock responses for the three select calls
    # Create distinct mock execute responses
    mock_chat_execute = MagicMock()
    mock_chat_execute.data = [mock_chat_data]
    
    mock_messages_execute = MagicMock()
    mock_messages_execute.data = mock_message_data
    
    mock_members_execute = MagicMock()
    mock_members_execute.data = mock_member_data
    
    # Configure the chained calls to return the correct execute mock
    # We need to mock the return value of execute based on the table it's called on.
    # This requires a more sophisticated side_effect function on table() or execute().
    def table_side_effect(table_name):
        mock_table_response = MagicMock()
        mock_method_response = MagicMock()
        
        if table_name == "chat":
            mock_method_response.execute.return_value = mock_chat_execute
        elif table_name == "chat_messages":
            mock_method_response.execute.return_value = mock_messages_execute
        elif table_name == "chat_members":
            mock_method_response.execute.return_value = mock_members_execute
        else:
            # Default empty response for other tables if needed
            default_execute = MagicMock()
            default_execute.data = []
            mock_method_response.execute.return_value = default_execute
            
        mock_table_response.select.return_value.eq.return_value.order.return_value = mock_method_response
        mock_table_response.select.return_value.eq.return_value = mock_method_response
        return mock_table_response

    mock_connection_manager.table.side_effect = table_side_effect

    chat = await chat_service.get_chat(chat_id)
    
    assert chat is not None
    assert chat["id"] == chat_id
    assert len(chat["messages"]) == 1
    assert chat["messages"][0]["message"] == "Hello"
    assert len(chat["members"]) == 1
    assert chat["members"][0]["user_id"] == user_id

# Add tests for PropertyService specific methods

@pytest.mark.asyncio
async def test_get_property_with_relations(property_service_fixture, mock_connection_manager):
    """Test getting a property with all its relations."""
    property_id = str(uuid4())
    address_id = str(uuid4())
    
    # Mock property data
    property_data = {
        "id": property_id,
        "portfolio_id": str(uuid4()),
        "address_id": address_id,
        "main_img_url": "https://example.com/image.jpg",
        "created_at": datetime.utcnow(),
        "updated_at": datetime.utcnow(),
        "is_deleted": False
    }
    
    # Mock address data
    address_data = {
        "id": address_id,
        "address": "123 Main St",
        "city": "Example City",
        "state": "CA",
        "zip": "12345",
        "created_at": datetime.utcnow(),
        "updated_at": datetime.utcnow()
    }
    
    # Configure mock responses
    def table_side_effect(table_name):
        mock_table_response = MagicMock()
        mock_method_response = MagicMock()
        mock_execute_response = MagicMock()
        
        if table_name == "prop":
            mock_execute_response.data = [property_data]
        elif table_name == "prop_addresses":
            mock_execute_response.data = [address_data]
        else:
            # Empty data for other tables
            mock_execute_response.data = []
        
        mock_method_response.execute.return_value = mock_execute_response
        mock_table_response.select.return_value.eq.return_value = mock_method_response
        mock_table_response.select.return_value = mock_method_response
        return mock_table_response
    
    mock_connection_manager.table.side_effect = table_side_effect
    
    # Call method
    result = await property_service_fixture.get_property_with_relations(property_id)
    
    # Assertions
    assert result is not None
    assert result["id"] == property_id
    assert "prop_address" in result
    assert result["prop_address"]["address"] == "123 Main St"

# TODO: Add more tests for other PropertyService methods
# TODO: Add tests for error handling (e.g., Supabase client raising exceptions)
# TODO: Refine mock verification, especially for multiple calls to the same method (like insert) 