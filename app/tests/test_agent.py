"""
Simple test script to verify LlamaIndex agent functionality.
This can be run directly to test if the agent works outside the main application.
"""
import os
import sys
import nest_asyncio
from dotenv import load_dotenv

# Apply nest_asyncio to handle nested event loops
nest_asyncio.apply()

# Load environment variables first
load_dotenv()

# Print package versions for debugging
try:
    import llama_index
    print(f"LlamaIndex version: {llama_index.__version__}")
except (ImportError, AttributeError):
    print("LlamaIndex core package not found or version not available")

try:
    # Import only necessary components for testing
    from llama_index.llms.openai import OpenAI
    from llama_index.core.tools import FunctionTool, ToolMetadata
    from llama_index.agent.openai import OpenAIAgent
    print("Successfully imported all required LlamaIndex modules")
except ImportError as e:
    print(f"Error importing LlamaIndex modules: {e}")
    sys.exit(1)

def main():
    """Test the LlamaIndex agent with a simple query."""
    
    print("Initializing test agent...")
    
    # Check OpenAI API key
    openai_key = os.getenv("OPENAI_API_KEY")
    if not openai_key:
        print("ERROR: No OpenAI API key found. Set the OPENAI_API_KEY environment variable.")
        return
    
    print(f"OpenAI API key found (length: {len(openai_key)})")
    
    try:
        # Initialize the OpenAI LLM with explicit parameters
        llm = OpenAI(
            model="gpt-4.1-mini", 
            temperature=0.7, 
            api_key=openai_key
        )
        print("OpenAI LLM initialized successfully")
        
        # Create a simple tool
        def simple_tool(query: str) -> str:
            print(f"Tool called with: {query}")
            return f"This is a response from the simple tool: {query}"
        
        tool = FunctionTool.from_defaults(
            name="simple_tool",
            description="A simple tool that echoes the input",
            fn=simple_tool,
            metadata=ToolMetadata(
                name="simple_tool",
                description="Use this tool for testing"
            )
        )
        print("Tool created successfully")
        
        # Create the agent with minimal configuration
        agent = OpenAIAgent.from_tools(
            [tool],
            llm=llm,
            verbose=True,
            system_prompt="You are a helpful assistant. Use the available tools to respond to queries."
        )
        print("Agent created successfully")
        
        # Test the agent with a query that should trigger the tool
        query = "Can you use the simple tool to process this message?"
        print(f"\nTest query: {query}")
        
        # Make a simple agent call - LlamaIndex now handles this synchronously
        response = agent.chat(query)
        print(f"\nAgent response: {response.response}")
        print("\nAgent test completed successfully!")
        
        # Test a follow-up query
        follow_up = "Thank you! Can you summarize what the tool did?"
        print(f"\nFollow-up query: {follow_up}")
        response = agent.chat(follow_up)
        print(f"\nAgent response: {response.response}")
        
    except Exception as e:
        print(f"\nError during agent test: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    # Run the function
    main() 