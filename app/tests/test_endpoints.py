"""
Tests for API endpoints.
"""
import json
import pytest
from fastapi.testclient import Test<PERSON>lient

from app.main import app


client = TestClient(app)


@pytest.mark.asyncio
async def test_create_conversation():
    """Test creating a new conversation."""
    response = client.post("/conversations")
    assert response.status_code == 201
    data = response.json()
    assert "id" in data
    assert "messages" in data
    assert len(data["messages"]) == 0


@pytest.mark.asyncio
async def test_get_conversation():
    """Test retrieving a conversation."""
    # Create a conversation first
    response = client.post("/conversations")
    assert response.status_code == 201
    conversation = response.json()
    
    # Get the conversation
    response = client.get(f"/conversations/{conversation['id']}")
    assert response.status_code == 200
    data = response.json()
    assert data["id"] == conversation["id"]


@pytest.mark.asyncio
async def test_list_conversations():
    """Test listing conversations."""
    # Create a few conversations
    for _ in range(3):
        client.post("/conversations")
    
    response = client.get("/conversations")
    assert response.status_code == 200
    data = response.json()
    assert isinstance(data, list)
    assert len(data) >= 3


@pytest.mark.asyncio
async def test_add_message():
    """Test adding a message to a conversation."""
    # Create a conversation
    response = client.post("/conversations")
    conversation_id = response.json()["id"]
    
    # Add a user message
    message_data = {
        "role": "user",
        "content": {"text": "Hello, AI!"}
    }
    response = client.post(
        f"/conversations/{conversation_id}/messages",
        json=message_data
    )
    assert response.status_code == 200
    
    # Check AI response
    data = response.json()
    assert data["role"] == "assistant"
    assert "text" in data["content"]
    
    # Get the conversation to check both messages are there
    response = client.get(f"/conversations/{conversation_id}")
    assert response.status_code == 200
    data = response.json()
    assert len(data["messages"]) == 2
    assert data["messages"][0]["role"] == "user"
    assert data["messages"][1]["role"] == "assistant"


@pytest.mark.asyncio
async def test_delete_conversation():
    """Test deleting a conversation."""
    # Create a conversation
    response = client.post("/conversations")
    conversation_id = response.json()["id"]
    
    # Delete the conversation
    response = client.delete(f"/conversations/{conversation_id}")
    assert response.status_code == 204
    
    # Try to get the deleted conversation
    response = client.get(f"/conversations/{conversation_id}")
    assert response.status_code == 404


@pytest.mark.asyncio
async def test_websocket():
    """Test WebSocket connection and messaging."""
    import uuid
    client_id = f"test-client-{uuid.uuid4().hex[:8]}"
    
    # Create a conversation first to get an ID
    response = client.post("/conversations")
    conversation_id = response.json()["id"]
    
    with client.websocket_connect(f"/ws/{client_id}/{conversation_id}") as websocket:
        # Send a message
        websocket.send_text(json.dumps({
            "text": "Hello from WebSocket test"
        }))
        
        # Receive response
        response = websocket.receive_text()
        data = json.loads(response)
        
        # Verify response format
        assert "id" in data
        assert data["role"] == "assistant"
        assert "text" in data["content"] 