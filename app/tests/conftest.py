import pytest
from unittest.mock import AsyncMock, MagicMock

@pytest.fixture(scope="function")
def mock_db_session():
    """Fixture to mock the database session."""
    session = AsyncMock()
    session.execute = AsyncMock()
    session.commit = AsyncMock()
    session.rollback = AsyncMock()
    session.scalar_one_or_none = AsyncMock()
    session.scalars = AsyncMock()
    # Mock the behavior of scalars().all() to return an empty list by default
    session.scalars.return_value.all = MagicMock(return_value=[]) 
    # Mock the behavior of execute().scalars().all()
    session.execute.return_value.scalars.return_value.all = MagicMock(return_value=[]) 
    # Mock the behavior of execute().scalar_one_or_none()
    session.execute.return_value.scalar_one_or_none = AsyncMock(return_value=None)
    return session

@pytest.fixture(scope="function")
def mock_redis_client():
    """Fixture to mock the Redis client."""
    client = AsyncMock()
    client.get = AsyncMock(return_value=None)
    client.set = AsyncMock()
    client.delete = AsyncMock()
    client.exists = AsyncMock(return_value=0)
    client.keys = AsyncMock(return_value=[])
    return client

@pytest.fixture(scope="function")
def mock_db_client(mocker):
    """Fixture to mock the database client."""
    client = MagicMock()
    client.table = MagicMock()
    
    # Set up table method to return an object with common Supabase operations
    table_response = MagicMock()
    method_response = MagicMock()
    execute_response = MagicMock()
    execute_response.data = []
    
    # Set up the chain: table().select().eq().execute()
    table_response.select.return_value = method_response
    table_response.insert.return_value = method_response
    table_response.update.return_value = method_response
    table_response.delete.return_value = method_response
    method_response.eq.return_value = method_response
    method_response.execute.return_value = execute_response
    
    client.table.return_value = table_response
    
    # Patch get_db_client to return our mock
    mocker.patch('app.repository.db.get_db_client', return_value=client)
    return client

# You might need more specific mock setups depending on your repository functions 