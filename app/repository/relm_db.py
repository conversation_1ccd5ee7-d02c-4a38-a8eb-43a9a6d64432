"""
RELM Database Repository - Connection and queries for external property database.

This module provides database connection and query functions for the external
RELM property database containing rental listings.
"""
import asyncpg
from asyncpg.pool import Pool
from typing import Optional, List

from app.utils.logger import get_logger
from app.utils.env_cache import get_env
from app.models.relm_models import RentalListing

logger = get_logger(__name__)

class RelmDbConnection:
    """Database connection manager for external RELM database."""
    
    _pool: Optional[Pool] = None
    
    @classmethod
    async def get_pool(cls) -> Pool:
        """Get or create database connection pool."""
        if cls._pool is None:
            try:
                port_str = get_env('PROPERTY_PG_PORT', '5432')
                port = int(port_str) if port_str else 5432
                
                cls._pool = await asyncpg.create_pool(
                    host=get_env('PROPERTY_PG_HOST'),
                    database=get_env('PROPERTY_PG_DBNAME'),
                    user=get_env('PROPERTY_PG_USER'),
                    password=get_env('PROPERTY_PG_PASSWORD'),
                    port=port,
                    min_size=1,
                    max_size=10
                )
                logger.info("Connected to RELM external database")
            except Exception as e:
                logger.error(f"Failed to connect to RELM database: {str(e)}")
                raise
        return cls._pool
    
    @classmethod
    async def close_pool(cls):
        """Close database connection pool."""
        if cls._pool:
            await cls._pool.close()
            cls._pool = None


class RelmDbService:
    """Service for querying the external RELM database."""
    
    @classmethod
    async def get_recent_rental_listings(
        cls, 
        postal_code: Optional[str] = None,
        city: Optional[str] = None,
        state_code: Optional[str] = None,
        limit: int = 50,
        prefer_active: bool = True
    ) -> List[RentalListing]:
        """
        Get recent rental listings with rent and sqft data.
        
        Args:
            postal_code: Filter by postal code
            city: Filter by city
            state_code: Filter by state code
            limit: Maximum number of results
            prefer_active: Prefer is_for_rent=True listings first
            
        Returns:
            List of RentalListing objects with rent and sqft data
        """
        try:
            pool = await RelmDbConnection.get_pool()
            
            # Build WHERE clause for location filtering
            where_conditions = ["list_price IS NOT NULL", "sqft IS NOT NULL", "sqft > 0"]
            params = []
            param_count = 0
            
            if postal_code:
                # Join with addresses to filter by postal code
                join_query = f"""
                FROM dev_rental_listings l
                JOIN dev_rental_addresses a ON l.address_id = a.id
                WHERE a.zipcode = ${param_count + 1}
                """
                params.append(postal_code)
                param_count += 1
            elif city and state_code:
                # Join with addresses to filter by city and state
                join_query = f"""
                FROM dev_rental_listings l
                JOIN dev_rental_addresses a ON l.address_id = a.id
                WHERE a.city = ${param_count + 1} AND a.state_code = ${param_count + 2}
                """
                params.extend([city, state_code])
                param_count += 2
            else:
                join_query = "FROM dev_rental_listings l"
            
            # Add other conditions
            for condition in where_conditions:
                if "WHERE" in join_query:
                    join_query += f" AND {condition}"
                else:
                    join_query += f" WHERE {condition}"
            
            # Order by preference: active rentals first, then by list_date desc
            if prefer_active:
                order_clause = "ORDER BY is_for_rent DESC, list_date DESC NULLS LAST"
            else:
                order_clause = "ORDER BY list_date DESC NULLS LAST"
            
            query = f"""
            SELECT l.id, l.address_id, l.mls_id, l.beds, l.full_baths, l.half_baths,
                   l.sqft, l.list_price, l.list_date, l.is_for_rent, l.price_per_sqft,
                   l.year_built, l.hoa_fee, l.description_type
            {join_query}
            {order_clause}
            LIMIT ${param_count + 1}
            """
            
            params.append(limit)
            
            async with pool.acquire() as conn:
                rows = await conn.fetch(query, *params)
                
                listings = []
                for row in rows:
                    listing_data = dict(row)
                    listing = RentalListing(**listing_data)
                    listings.append(listing)
                
                return listings
                
        except Exception as e:
            logger.error(f"Error fetching rental listings from RELM DB: {str(e)}")
            return []
    
    @classmethod
    async def get_rental_comps_by_location(
        cls,
        postal_code: Optional[str] = None,
        city: Optional[str] = None,
        state_code: Optional[str] = None,
        beds: Optional[int] = None,
        min_sqft: Optional[float] = None,
        max_sqft: Optional[float] = None,
        limit: int = 50
    ) -> List[RentalListing]:
        """
        Get rental comparables by location and property characteristics.
        
        Args:
            postal_code: Filter by postal code
            city: Filter by city  
            state_code: Filter by state code
            beds: Filter by number of bedrooms
            min_sqft: Minimum square footage
            max_sqft: Maximum square footage
            limit: Maximum number of results
            
        Returns:
            List of comparable rental listings
        """
        try:
            pool = await RelmDbConnection.get_pool()
            
            where_conditions = ["l.list_price IS NOT NULL", "l.sqft IS NOT NULL", "l.sqft > 0"]
            params = []
            param_count = 0
            
            # Location filtering
            if postal_code:
                join_query = f"""
                FROM dev_rental_listings l
                JOIN dev_rental_addresses a ON l.address_id = a.id
                WHERE a.zipcode = ${param_count + 1}
                """
                params.append(postal_code)
                param_count += 1
            elif city and state_code:
                join_query = f"""
                FROM dev_rental_listings l  
                JOIN dev_rental_addresses a ON l.address_id = a.id
                WHERE a.city = ${param_count + 1} AND a.state_code = ${param_count + 2}
                """
                params.extend([city, state_code])
                param_count += 2
            else:
                join_query = "FROM dev_rental_listings l"
            
            # Property characteristic filtering
            if beds is not None:
                where_conditions.append(f"l.beds = ${param_count + 1}")
                params.append(beds)
                param_count += 1
                
            if min_sqft is not None:
                where_conditions.append(f"l.sqft >= ${param_count + 1}")
                params.append(min_sqft)
                param_count += 1
                
            if max_sqft is not None:
                where_conditions.append(f"l.sqft <= ${param_count + 1}")
                params.append(max_sqft)
                param_count += 1
            
            # Add conditions to query
            for condition in where_conditions:
                if "WHERE" in join_query:
                    join_query += f" AND {condition}"
                else:
                    join_query += f" WHERE {condition}"
            
            query = f"""
            SELECT l.id, l.address_id, l.mls_id, l.beds, l.full_baths, l.half_baths,
                   l.sqft, l.list_price, l.list_date, l.is_for_rent, l.price_per_sqft,
                   l.year_built, l.hoa_fee, l.description_type
            {join_query}
            ORDER BY l.list_date DESC NULLS LAST
            LIMIT ${param_count + 1}
            """
            
            params.append(limit)
            
            async with pool.acquire() as conn:
                rows = await conn.fetch(query, *params)
                
                listings = []
                for row in rows:
                    listing_data = dict(row)
                    listing = RentalListing(**listing_data)
                    listings.append(listing)
                
                return listings
                
        except Exception as e:
            logger.error(f"Error fetching rental comps from RELM DB: {str(e)}")
            return []
    
    @classmethod 
    async def calculate_average_rent_per_sqft(
        cls,
        postal_code: Optional[str] = None,
        city: Optional[str] = None,
        state_code: Optional[str] = None,
        beds: Optional[int] = None
    ) -> Optional[float]:
        """
        Calculate average rent per square foot for a location and property type.
        
        Args:
            postal_code: Filter by postal code
            city: Filter by city
            state_code: Filter by state code
            beds: Filter by number of bedrooms
            
        Returns:
            Average rent per square foot or None if no data
        """
        try:
            pool = await RelmDbConnection.get_pool()
            
            where_conditions = ["l.list_price IS NOT NULL", "l.sqft IS NOT NULL", "l.sqft > 0"]
            params = []
            param_count = 0
            
            # Location filtering
            if postal_code:
                join_query = f"""
                FROM dev_rental_listings l
                JOIN dev_rental_addresses a ON l.address_id = a.id
                WHERE a.zipcode = ${param_count + 1}
                """
                params.append(postal_code)
                param_count += 1
            elif city and state_code:
                join_query = f"""
                FROM dev_rental_listings l
                JOIN dev_rental_addresses a ON l.address_id = a.id  
                WHERE a.city = ${param_count + 1} AND a.state_code = ${param_count + 2}
                """
                params.extend([city, state_code])
                param_count += 2
            else:
                join_query = "FROM dev_rental_listings l"
            
            # Bedroom filtering
            if beds is not None:
                where_conditions.append(f"l.beds = ${param_count + 1}")
                params.append(beds)
                param_count += 1
            
            # Add conditions to query
            for condition in where_conditions:
                if "WHERE" in join_query:
                    join_query += f" AND {condition}"
                else:
                    join_query += f" WHERE {condition}"
            
            query = f"""
            SELECT AVG(l.list_price / l.sqft) as avg_rent_per_sqft
            {join_query}
            """
            
            async with pool.acquire() as conn:
                result = await conn.fetchval(query, *params)
                return float(result) if result is not None else None
                
        except Exception as e:
            logger.error(f"Error calculating average rent per sqft from RELM DB: {str(e)}")
            return None 