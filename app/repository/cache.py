"""
Redis cache module for data caching.
"""
import json
import redis.asyncio as redis

from datetime import datetime
from typing import Dict, Optional, Any
from app.utils.logger import get_logger
from app.utils.env_cache import get_env

logger = get_logger(__name__)

# Redis connection settings using cached getter
REDIS_HOST = get_env("REDIS_HOST", "localhost")
redis_port_str = get_env("REDIS_PORT", '6379')
REDIS_PORT = int(redis_port_str) if redis_port_str is not None else 6379 # Handle potential None
REDIS_USERNAME = get_env("REDIS_USERNAME")
REDIS_PASSWORD = get_env("REDIS_PASSWORD")

class DateTimeEncoder(json.JSONEncoder):
    """Custom JSON encoder that handles datetime objects."""
    def default(self, obj):
        if isinstance(obj, datetime):
            return obj.isoformat()
        return super().default(obj)


class RedisCache:
    """
    Redis cache implementation for data storage.
    """
    
    def __init__(self):
        """Initialize Redis connection."""
        if REDIS_HOST and REDIS_PORT and REDIS_USERNAME and REDIS_PASSWORD:
            self.redis = redis.Redis(
                host=REDIS_HOST,
                port=REDIS_PORT,
                username=REDIS_USERNAME,
                password=REDIS_PASSWORD,
                decode_responses=True  # Decode responses to strings
            )
            logger.info(f"Connecting to Redis at {REDIS_HOST}:{REDIS_PORT}")
        else:
            # Fallback or raise an error if credentials are not set
            logger.error("Redis connection details not found in environment variables.")
            raise ValueError("Missing Redis connection details in .env")
        
        logger.info("Initialized Redis cache")

    async def set(self, key: str, data: Dict[str, Any], ttl: int = 1800) -> None:
        """
        Store data in the cache.
        
        Args:
            key: The cache key
            data: The data to store
        """
        
        try:
            # Serialize the data
            serialized_data = json.dumps(data, cls=DateTimeEncoder)
            
            # Store in Redis
            await self.redis.set(key, serialized_data, ex=ttl)
            
            logger.debug(f"Cached data: {key}")
        except Exception as e:
            logger.error(f"Error caching data {key}: {str(e)}")

    async def delete(self, key: str) -> None:
        """
        Delete data from the cache.
        """
        await self.redis.delete(key)
    
    async def get(self, key: str) -> Optional[Dict[str, Any]]:
        """
        Retrieve data from the cache.
        
        Args:
            key: The cache key
            
        Returns:
            Optional[Dict[str, Any]]: The data if found, None otherwise
        """
        
        # Get from Redis
        data = await self.redis.get(key)
        
        if not data:
            return None
        
        # Deserialize
        try:
            return json.loads(data)
        except json.JSONDecodeError as e:
            logger.error(f"Error deserializing data: {str(e)}")
            return None
    
    async def expire(self, key: str, seconds: int) -> bool:
        """
        Set an expiration time on a key.
        
        Args:
            key: The key to set expiration on
            seconds: Time to expiration in seconds
            
        Returns:
            bool: True if the timeout was set, False otherwise
        """
        return await self.redis.expire(key, seconds)
    
    async def close(self) -> None:
        """Close the Redis connection."""
        await self.redis.close()

    async def task_queue_keygen(self, stage: str, task_token: str) -> str:
        """
        Get the key for a task in the cache.
        """
        return f"task_queue:{stage}:{task_token}"
    
    async def set_task_queue(self, stage: str, token: str, status: str, ttl: int = 1800, data: Dict[str, Any] = {}):
        """
        Add a task to the cache.
        """
        key = await self.task_queue_keygen(stage, token)
        await self.set(key, {"status": status, "token": token, "data": data}, ttl)
        return {"status": status, "token": token, "data": data}

    async def get_task_queue(self, stage: str, token: str):
        """
        Get a task from the cache.
        """
        key = await self.task_queue_keygen(stage, token)
        return await self.get(key)

    async def delete_task_queue(self, stage: str, token: str):
        """
        Delete a task from the cache.
        """
        key = await self.task_queue_keygen(stage, token)
        await self.delete(key)


# Create a singleton instance
redis_cache = RedisCache() 