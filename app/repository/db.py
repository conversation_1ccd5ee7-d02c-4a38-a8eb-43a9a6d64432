"""
Supabase database connection and operations module.
"""
import asyncio
from typing import Optional, ClassVar
from contextlib import asynccontextmanager

from supabase import create_client, Client 

from app.utils.env_cache import get_env
from app.utils.logger import get_logger

logger = get_logger(__name__)

# Use cached getter for configuration
supabase_url = get_env("SUPABASE_URL")
supabase_key = get_env("SUPABASE_SERVICE_ROLE_KEY")

class SupabaseConnectionManager:
    """
    Singleton manager for Supabase database connections.
    Ensures a single shared connection instance across the application.
    """
    _instance: ClassVar[Optional['SupabaseConnectionManager']] = None
    _initialized: bool = False
    _client: Optional[Client] = None
    _lock: asyncio.Lock = asyncio.Lock()
    
    def __new__(cls):
        """Ensure singleton pattern - only one instance of the manager exists."""
        if cls._instance is None:
            cls._instance = super(SupabaseConnectionManager, cls).__new__(cls)
        return cls._instance
    
    async def initialize(self) -> None:
        """Initialize the Supabase connection if not already initialized."""
        # Use lock to ensure only one initialization happens at a time
        async with self._lock:
            if self._initialized and self._client is not None:
                return
                
            if not supabase_url or not supabase_key:
                logger.error("Supabase environment variables SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY must be set.")
                raise ValueError("Supabase URL and service role key must be provided")
    
            try:
                # Create the client
                self._client = create_client(supabase_url, supabase_key) 
                
                # Verify the client is working with a simple query
                try:
                    # Try a simple query to verify connection
                    response = self._client.from_("chat").select("count", count="exact").limit(1).execute()
                    count_val = response.count if hasattr(response, 'count') else None
                    logger.info(f"Supabase client initialized and verified, count: {count_val}")
                except Exception as e:
                    logger.warning(f"Supabase client created but verification query failed: {e}")
                    # Continue anyway since the client was created
                
                self._initialized = True
                logger.info("Successfully initialized Supabase database connection")
            except Exception as e:
                logger.error(f"Failed to initialize Supabase client: {e}")
                self._client = None
                self._initialized = False
                raise
    
    async def get_client(self) -> Client:
        """
        Get the Supabase client instance, initializing if necessary.
        
        Returns:
            Client: Initialized Supabase client
            
        Raises:
            RuntimeError: If initialization fails
        """
        if not self._initialized or self._client is None:
            await self.initialize()
            
        if self._client is None:
            raise RuntimeError("Failed to initialize Supabase client")
            
        return self._client
    
    async def verify_connection(self) -> bool:
        """
        Verify that the connection is still working.
        
        Returns:
            bool: True if the connection is working, False otherwise
        """
        if not self._initialized or self._client is None:
            return False
            
        try:
            response = self._client.from_("chat").select("count", count="exact").limit(1).execute()
            return True if response else False
        except Exception as e:
            logger.warning(f"Supabase client verification failed: {e}")
            return False
    
    async def reset_connection(self) -> None:
        """
        Reset the connection if needed.
        """
        async with self._lock:
            self._client = None
            self._initialized = False
            await self.initialize()

# Global singleton instance of the connection manager
connection_manager = SupabaseConnectionManager()

# For backwards compatibility with existing code
db: Client | None = None  # Will be set during app startup

async def init_db():
    """Initialize Supabase connection for the application."""
    global db
    # Use the connection manager to get a client
    client = await connection_manager.get_client()
    # Set the global db variable for backwards compatibility
    db = client
    return client

# ADDED: Simplified way to get a database client for all services
async def get_db_client() -> Client:
    """
    Get a database client from the connection manager.
    This is the standard way for services to get database access.
    
    Returns:
        Client: Initialized Supabase client
    """
    return await connection_manager.get_client()

@asynccontextmanager
async def get_db_connection():
    """
    Async context manager for getting a database connection.
    
    Usage:
        async with get_db_connection() as client:
            result = client.from_("table").select("*").execute()
    
    Yields:
        Client: Supabase client
    """
    client = await connection_manager.get_client()
    try:
        yield client
    except Exception:
        # If there's an error, check if connection is still valid
        if not await connection_manager.verify_connection():
            # If not valid, reset for next use
            await connection_manager.reset_connection()
        raise
