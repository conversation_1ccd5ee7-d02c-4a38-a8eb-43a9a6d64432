"""
Pydantic models for database tables.
"""
from uuid import UUID
from datetime import datetime, date
from pydantic import BaseModel, Field
from typing import Dict, List, Optional, Any

class Users(BaseModel):
    """User database model."""
    id: UUID = Field(..., description="Unique user identifier")
    full_name: Optional[str] = Field(None, description="User's full name")
    email: Optional[str] = Field(None, description="User's email address")
    stripe_customer_id: Optional[str] = Field(None, description="Stripe customer ID")

    class Config:
        table = "users"

class ChatMessage(BaseModel):
    """Chat message database model."""
    id: UUID = Field(..., description="Unique message identifier")
    chat_id: UUID = Field(..., description="Chat identifier")
    user_id: UUID = Field(..., description="User identifier")
    message: str = Field(..., description="Message content")
    role: str = Field(..., description="Message role (user/assistant)")
    created_at: datetime = Field(..., description="Message creation timestamp")
    updated_at: datetime = Field(..., description="Message update timestamp")
    is_deleted: bool = Field(..., description="Whether the message is deleted")

    class Config:
        table = "chat_messages"


class Chat(BaseModel):
    """Chat database model."""
    id: UUID = Field(..., description="Unique chat identifier")
    name: Optional[str] = Field(default="Portfolio Chat", description="Chat name")
    portfolio_id: UUID = Field(..., description="Portfolio identifier")
    created_by: UUID = Field(..., description="User who created the chat")
    created_at: datetime = Field(..., description="Chat creation timestamp")
    updated_at: Optional[datetime] = Field(None, description="Last update timestamp")
    deleted_at: Optional[datetime] = Field(None, description="Deletion timestamp")
    is_deleted: bool = Field(..., description="Whether the chat is deleted")

    class Config:
        table = "chat"


class ChatMember(BaseModel):
    """Chat member database model."""
    id: UUID = Field(..., description="Unique chat member identifier")
    user_id: UUID = Field(..., description="User identifier")
    chat_id: UUID = Field(..., description="Chat identifier")
    role: str = Field(..., description="Member role (owner/member)")
    added_at: datetime = Field(..., description="When the member was added")
    deleted_at: Optional[datetime] = Field(None, description="Deletion timestamp")
    is_deleted: bool = Field(default=False, description="Whether the member is deleted")

    class Config:
        table = "chat_members"

class DataroomGuest(BaseModel):
    """Dataroom guest database model."""
    id: int = Field(..., description="Unique dataroom guest identifier")
    owner_id: Optional[UUID] = Field(None, description="Owner user identifier")
    sender_id: Optional[UUID] = Field(None, description="Sender user identifier")
    receiver_id: Optional[UUID] = Field(None, description="Receiver user identifier")
    can_share: Optional[bool] = Field(None, description="Whether guest can share")
    can_download: Optional[bool] = Field(None, description="Whether guest can download")
    has_access: Optional[bool] = Field(None, description="Whether guest has access")
    created_at: datetime = Field(..., description="Creation timestamp")
    portfolio_id: Optional[UUID] = Field(None, description="Portfolio identifier")

    class Config:
        table = "dataroom_guests"

class DataroomEvent(BaseModel):
    """Dataroom event database model."""
    id: int = Field(..., description="Unique dataroom event identifier")
    user_id: Optional[UUID] = Field(None, description="User identifier")
    event_type: Optional[str] = Field(None, description="Type of event")
    timestamp: datetime = Field(..., description="Event timestamp")
    portfolio_id: Optional[UUID] = Field(None, description="Portfolio identifier")

    class Config:
        table = "dataroom_events"


class PropertyAddress(BaseModel):
    """Property address database model."""
    id: UUID = Field(..., description="Unique address identifier")
    address: str = Field(..., description="Street address")
    city: str = Field(..., description="City")
    state: str = Field(..., description="State")
    zip: str = Field(..., description="ZIP code")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: Optional[datetime] = Field(None, description="Last update timestamp")

    class Config:
        table = "prop_addresses"


class PropertyUnit(BaseModel):
    """Property unit database model."""
    id: Optional[UUID] = Field(None, description="Unique unit identifier")
    prop_id: Optional[UUID] = Field(None, description="Property identifier")
    unit: str = Field(..., description="Unit identifier (e.g., 'A', '101')")
    beds: Optional[float] = Field(None, description="Number of bedrooms")
    baths: Optional[float] = Field(None, description="Number of bathrooms")
    hoa_fee: Optional[float] = Field(None, description="HOA fee")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: Optional[datetime] = Field(None, description="Last update timestamp")
    sqft: Optional[float] = Field(None, description="Square footage")
    feature_tags: Optional[List[str]] = Field(None, description="Feature tags")
    amenities_tags: Optional[List[str]] = Field(None, description="Amenities tags")
    img_urls: Optional[List[str]] = Field(None, description="Image URLs")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")
    rent: Optional[float] = Field(None, description="Rent amount")
    price: Optional[float] = Field(None, description="Price amount")
    availability_status: Optional[str] = Field(None, description="Availability status")

    class Config:
        table = "prop_units"


class PropertyMarketData(BaseModel):
    """Property market data database model."""
    id: UUID = Field(..., description="Unique market data identifier")
    prop_id: UUID = Field(..., description="Property identifier")
    unit: str = Field(..., description="Unit identifier")
    beds: Optional[float] = Field(None, description="Number of bedrooms")
    baths: Optional[float] = Field(None, description="Number of bathrooms")
    sqft: Optional[float] = Field(None, description="Square footage")
    data: Optional[Dict[str, Any]] = Field(None, description="Market data as JSON")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: Optional[datetime] = Field(None, description="Last update timestamp")
    unit_id: Optional[UUID] = Field(None, description="Unit identifier")

    class Config:
        table = "prop_market_data"


class Property(BaseModel):
    """Property database model."""
    id: UUID = Field(..., description="Unique property identifier")
    portfolio_id: UUID = Field(..., description="Portfolio identifier")
    address_id: UUID = Field(..., description="Address identifier")
    main_img_url: Optional[str] = Field(None, description="Main property image URL")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: Optional[datetime] = Field(None, description="Last update timestamp")
    is_deleted: bool = Field(False, description="Whether the property is deleted")
    
    # Relationships (not stored in DB, but for convenience)
    address: Optional[PropertyAddress] = Field(None, description="Property address")
    units: Optional[List[PropertyUnit]] = Field(None, description="Property units")
    market_data: Optional[List[PropertyMarketData]] = Field(None, description="Property market data")
    env_risk: Optional["PropertyEnvironmentalRisk"] = Field(None, description="Environmental risk data")
    solar_potential: Optional["PropertySolarPotential"] = Field(None, description="Solar potential data")
    air_quality: Optional["PropertyAirQuality"] = Field(None, description="Air quality data")
    pois: Optional["PointsOfInterest"] = Field(None, description="Points of interest data")
    financials: Optional["PropertyFinancials"] = Field(None, description="Property financials")

    class Config:
        table = "prop"

    def to_dict(self) -> Dict[str, Any]:
        """Convert the Property to a dictionary."""
        return {
            "id": str(self.id),
            "portfolio_id": str(self.portfolio_id),
            "address_id": str(self.address_id),
            "main_img_url": self.main_img_url,
            "created_at": self.created_at.isoformat(),
            "is_deleted": self.is_deleted,

        }

class PropertyDemographics(BaseModel):
    """Property demographics database model."""
    id: UUID = Field(..., description="Unique identifier")
    prop_id: UUID = Field(..., description="Property identifier")
    created_at: datetime = Field(..., description="Creation timestamp")
    modified_at: datetime = Field(..., description="Last modification timestamp")
    
    vintage: Optional[int] = Field(None, description="Vintage year of demographic data")
    population_2020_count: Optional[int] = Field(None, description="Population count from 2020 census")
    population_median_age: Optional[float] = Field(None, description="Median age of population")
    median_household_income: Optional[float] = Field(None, description="Median household income")
    average_household_income: Optional[float] = Field(None, description="Average household income")
    crime_total_risk: Optional[float] = Field(None, description="Total crime risk score")

    class Config:
        table = "prop_demographics"


class PropertyTaxHistory(BaseModel):
    """Property tax history database model."""
    id: UUID = Field(..., description="Unique identifier")
    prop_id: UUID = Field(..., description="Property identifier")
    created_at: datetime = Field(..., description="Creation timestamp")
    modified_at: datetime = Field(..., description="Last modification timestamp")
    
    assessed_tax_year: Optional[int] = Field(None, description="Year of tax assessment")
    assessed_value_land: Optional[float] = Field(None, description="Assessed value of land")
    assessed_value_total: Optional[float] = Field(None, description="Total assessed value")
    tax_bill_amount: Optional[float] = Field(None, description="Tax bill amount")

    class Config:
        table = "prop_tax_history"


class PropertyDetails(BaseModel):
    """Property details database model."""
    id: UUID = Field(..., description="Unique identifier")
    prop_id: UUID = Field(..., description="Property identifier")
    created_at: datetime = Field(..., description="Creation timestamp")
    modified_at: datetime = Field(..., description="Last modification timestamp")
    
    display_address: Optional[str] = Field(None, description="Display address")
    street_address: Optional[str] = Field(None, description="Street address")
    city: Optional[str] = Field(None, description="City")
    state: Optional[str] = Field(None, description="State")
    zip: Optional[str] = Field(None, description="ZIP code")
    bed_count: Optional[int] = Field(None, description="Number of bedrooms")
    bath_count: Optional[float] = Field(None, description="Number of bathrooms")
    gross_sq_ft: Optional[int] = Field(None, description="Gross square footage")
    lot_size_acre: Optional[float] = Field(None, description="Lot size in acres")
    year_built: Optional[int] = Field(None, description="Year property was built")
    property_use_code_mapped: Optional[str] = Field(None, description="Mapped property use code")
    hvacc_cooling_code: Optional[str] = Field(None, description="HVAC cooling code")
    hvacc_heating_code: Optional[str] = Field(None, description="HVAC heating code")
    parking_garage_code: Optional[str] = Field(None, description="Parking garage code")
    parking_space_count: Optional[int] = Field(None, description="Number of parking spaces")
    basement_sq_ft: Optional[int] = Field(None, description="Basement square footage")
    basement_finished_sq_ft: Optional[int] = Field(None, description="Finished basement square footage")
    basement_unfinished_sq_ft: Optional[int] = Field(None, description="Unfinished basement square footage")
    flooring_material_code: Optional[str] = Field(None, description="Flooring material code")
    has_laundry_room: Optional[bool] = Field(None, description="Whether property has laundry room")
    fireplace_count: Optional[int] = Field(None, description="Number of fireplaces")
    structure_style_code: Optional[str] = Field(None, description="Structure style code")
    roof_material_code: Optional[str] = Field(None, description="Roof material code")
    driveway_material_code: Optional[str] = Field(None, description="Driveway material code")
    construction_code: Optional[str] = Field(None, description="Construction code")
    roof_construction_code: Optional[str] = Field(None, description="Roof construction code")
    exterior_code: Optional[str] = Field(None, description="Exterior code")
    sewer_usage_code: Optional[str] = Field(None, description="Sewer usage code")
    water_source_code: Optional[str] = Field(None, description="Water source code")
    last_sale_date: Optional[date] = Field(None, description="Date of last sale")
    last_sale_amount: Optional[float] = Field(None, description="Amount of last sale")
    longitude: Optional[float] = Field(None, description="Longitude coordinate")
    latitude: Optional[float] = Field(None, description="Latitude coordinate")

    class Config:
        table = "prop_details"


class ZipDemographics(BaseModel):
    """Zip code demographics database model."""
    id: UUID = Field(..., description="Unique identifier")
    zipcode: str = Field(..., description="ZIP code")
    city: str = Field(..., description="City")
    snapshot_date: date = Field(..., description="Date of demographic snapshot")
    population: Optional[int] = Field(None, description="Population")
    median_age: Optional[float] = Field(None, description="Median age")
    median_hh_income: Optional[float] = Field(None, description="Median household income")
    avg_hh_income: Optional[float] = Field(None, description="Average household income")
    crime_risk: Optional[int] = Field(None, description="Crime risk score")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")

    class Config:
        table = "zip_demographics"

class Portfolios(BaseModel):
    """Portfolio database model."""
    id: UUID = Field(..., description="Unique portfolio identifier")
    name: str = Field(..., description="Portfolio name")
    workspace_id: UUID = Field(..., description="Workspace identifier")
    created_at: Optional[datetime] = Field(None, description="Creation timestamp")
    updated_at: Optional[datetime] = Field(None, description="Last update timestamp")
    is_deleted: Optional[bool] = Field(False, description="Whether the portfolio is deleted")

    class Config:
        table = "portfolios"

class PortfolioFinancials(BaseModel):
    """
    Portfolio financials database model.

    This model matches the database schema for the 'portfolio_financials' table.
    All fields are typed to match the database column types and names.
    """
    id: UUID = Field(..., description="Unique identifier")
    prop_id: UUID = Field(..., description="Portfolio identifier")
    year: int = Field(..., description="Year")
    rental_income: Optional[float] = Field(None, description="Rental income")
    other_income: Optional[float] = Field(None, description="Other income")
    vacancy_loss: Optional[float] = Field(None, description="Vacancy loss")
    property_tax: Optional[float] = Field(None, description="Property tax")
    insurance: Optional[float] = Field(None, description="Insurance")
    maintenance: Optional[float] = Field(None, description="Maintenance")
    management_fee: Optional[float] = Field(None, description="Management fee")
    utilities: Optional[float] = Field(None, description="Utilities")
    capital_expenditures: Optional[float] = Field(None, description="Capital expenditures")
    mortgage_payment: Optional[float] = Field(None, description="Mortgage payment")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")
    effective_gross_income: Optional[float] = Field(None, description="Effective gross income")
    total_professional_fees: Optional[float] = Field(None, description="Total professional fees")
    total_utilities: Optional[float] = Field(None, description="Total utilities")
    total_reserves: Optional[float] = Field(None, description="Total reserves")
    annual_debt_service: Optional[float] = Field(None, description="Annual debt service")
    dscr: Optional[float] = Field(None, description="Debt service coverage ratio")
    cash_flow_before_taxes: Optional[float] = Field(None, description="Cash flow before taxes")
    cash_flow_after_taxes: Optional[float] = Field(None, description="Cash flow after taxes")
    cumulative_cash_flow: Optional[float] = Field(None, description="Cumulative cash flow")
    gross_rent_multiplier: Optional[float] = Field(None, description="Gross rent multiplier")
    equity_multiple: Optional[float] = Field(None, description="Equity multiple")
    total_acquisition_cost: Optional[float] = Field(None, description="Total acquisition cost")
    aggregated_noi: Optional[float] = Field(None, description="Aggregated net operating income")
    blended_cap_rate: Optional[float] = Field(None, description="Blended capitalization rate")
    portfolio_irr: Optional[float] = Field(None, description="Portfolio internal rate of return")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Metadata")
    net_operating_income: Optional[str] = Field(None, description="Net operating income (text)")
    cap_rate: Optional[str] = Field(None, description="Capitalization rate (text)")
    total_operating_expenses: Optional[str] = Field(None, description="Total operating expenses (text)")

    class Config:
        table = "portfolio_financials"


class PropertyFinancials(BaseModel):
    """
    Property financials database model.
    
    This model matches the database schema for the 'prop_financials' table.
    All fields are typed to match the database column types and names.
    """
    id: UUID = Field(..., description="Unique identifier")
    prop_id: UUID = Field(..., description="Property identifier")
    year: int = Field(..., description="Year")
    
    # Income Projections
    rental_income: Optional[float] = Field(None, description="Gross Scheduled Income")
    long_term_rental: Optional[float] = Field(None, description="Long Term Rental")
    short_term_rental: Optional[float] = Field(None, description="Short Term Rental")
    other_income: Optional[float] = Field(None, description="Other Income")
    vacancy_loss: Optional[float] = Field(None, description="Vacancy Loss")
    credit_loss: Optional[float] = Field(None, description="Credit Loss")
    effective_gross_income: Optional[float] = Field(None, description="Effective Gross Income")
    
    # Expense Projections
    property_tax: Optional[float] = Field(None, description="Property Taxes")
    insurance: Optional[float] = Field(None, description="Insurance")
    repairs: Optional[float] = Field(None, description="Repairs")
    maintenance: Optional[float] = Field(None, description="Maintenance")
    professional_fees: Optional[float] = Field(None, description="Professional Fees")
    management_fees: Optional[float] = Field(None, description="Management")
    leasing_fees: Optional[float] = Field(None, description="Leasing")
    legal_fees: Optional[float] = Field(None, description="Legal")
    accounting_fees: Optional[float] = Field(None, description="Accounting")
    engineering_fees: Optional[float] = Field(None, description="Engineering/Inspections")
    marketing_fees: Optional[float] = Field(None, description="Marketing/Advertising")
    consulting_fees: Optional[float] = Field(None, description="Consulting")
    utilities: Optional[float] = Field(None, description="Utilities")
    services: Optional[float] = Field(None, description="Services")
    reserves: Optional[float] = Field(None, description="Reserves for Replacements")
    total_operating_expenses: Optional[float] = Field(None, description="Total Operating Expenses")
    
    # Net Operating Income
    net_operating_income: Optional[float] = Field(None, description="Net Operating Income (NOI)")
    
    # Financing & Debt Service
    annual_debt_service: Optional[float] = Field(None, description="Annual Debt Service (P&I)")
    dscr: Optional[float] = Field(None, description="DSCR (NOI ÷ Debt Service)")
    
    # Cash Flow Analysis
    cash_flow_before_taxes: Optional[float] = Field(None, description="Cash Flow Before Taxes")
    cash_flow_after_taxes: Optional[float] = Field(None, description="Cash Flow After Taxes")
    cumulative_cash_flow: Optional[float] = Field(None, description="Cumulative Cash Flow")
    
    # Valuation Metrics
    cap_rate: Optional[float] = Field(None, description="Cap Rate (%)")
    gross_rent_multiplier: Optional[float] = Field(None, description="GRM (×)")
    equity_multiple: Optional[float] = Field(None, description="Equity Multiple (×)")
    cash_on_cash_return: Optional[float] = Field(None, description="Cash-on-Cash Return (%)")
    property_value: Optional[float] = Field(None, description="Property Value")

    # Portfolio Summary & Roll-Up
    total_acquisition_cost: Optional[float] = Field(None, description="Total Acquisition Cost")
    aggregated_noi: Optional[float] = Field(None, description="Aggregated NOI")
    blended_cap_rate: Optional[float] = Field(None, description="Blended Cap Rate (%)")
    portfolio_irr: Optional[float] = Field(None, description="Portfolio IRR (%)")
    
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Metadata")

    class Config:
        table = "prop_financials"


class PropertyEnvironmentalRisk(BaseModel):
    """Property environmental risk database model."""
    id: UUID = Field(..., description="Unique identifier")
    prop_id: UUID = Field(..., description="Property identifier")
    data: Dict[str, Any] = Field(..., description="Environmental risk data as JSON")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")

    class Config:
        table = "prop_env_risk"


class PropertySolarPotential(BaseModel):
    """Property solar potential database model."""
    id: UUID = Field(..., description="Unique identifier")
    prop_id: UUID = Field(..., description="Property identifier")
    data: Dict[str, Any] = Field(..., description="Solar potential data as JSON")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")

    class Config:
        table = "prop_solar_potential"


class PropertyAirQuality(BaseModel):
    """Property air quality database model."""
    id: UUID = Field(..., description="Unique identifier")
    prop_id: UUID = Field(..., description="Property identifier")
    data: Dict[str, Any] = Field(..., description="Air quality data as JSON")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")

    class Config:
        table = "prop_air_quality"


class PointsOfInterest(BaseModel):
    """Points of interest database model."""
    id: UUID = Field(..., description="Unique identifier")
    prop_id: UUID = Field(..., description="Property identifier")
    data: Dict[str, Any] = Field(..., description="POI data as JSON")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")
    type: str = Field(..., description="POI type")

    class Config:
        table = "pois"


class Document(BaseModel):
    """Document database model."""
    id: UUID = Field(..., description="Unique document identifier")
    portfolio_id: UUID = Field(..., description="Portfolio identifier")
    property_id: Optional[UUID] = Field(None, description="Property identifier (optional)")
    user_id: UUID = Field(..., description="User who uploaded the document")
    type: Optional[str] = Field(None, description="Document type (e.g., 'lease', 'tax')")
    sub_type: Optional[str] = Field(None, description="Document sub-type")
    is_unit: Optional[bool] = Field(None, description="Whether document is related to a specific unit")
    unit_number: Optional[str] = Field(None, description="Unit number if applicable")
    filename: str = Field(..., description="Original filename")
    supabase_url: str = Field(..., description="Document URL in Supabase storage")
    processed: bool = Field(False, description="Whether document is processed for RAG")
    uploaded_at: datetime = Field(..., description="Upload timestamp")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")
    is_deleted: bool = Field(False, description="Whether the document is deleted")
    original_filename: Optional[str] = Field(None, description="Original filename before processing")
    is_hidden: bool = Field(False, description="Whether the document is hidden to guests")

    class Config:
        table = "documents"

class SlidesData(BaseModel):
    """Slides data database model."""
    id: UUID = Field(..., description="Unique identifier")
    portfolio_id: UUID = Field(..., description="Portfolio identifier")
    pdf_supabase_url: Optional[str] = Field(None, description="PDF URL in Supabase storage")
    pdf_supabase_path: Optional[str] = Field(None, description="PDF path in Supabase storage")
    pdf_name: Optional[str] = Field(None, description="PDF filename")
    pdf_url: Optional[str] = Field(None, description="Public PDF URL")
    raw_html: Optional[str] = Field(None, description="Raw HTML content")
    created_at: datetime = Field(..., description="Creation timestamp")

    class Config:
        table = "slides_data"

class Workspaces(BaseModel):
    """Workspace database model."""
    id: UUID = Field(..., description="Unique workspace identifier")
    name: str = Field(..., description="Workspace name")
    owner_id: UUID = Field(..., description="Owner user identifier")
    created_at: Optional[datetime] = Field(None, description="Creation timestamp")
    updated_at: Optional[datetime] = Field(None, description="Last update timestamp")
    is_personal: Optional[bool] = Field(None, description="Whether this is a personal workspace")
    is_deleted: bool = Field(False, description="Whether the workspace is deleted")
    billing_admin_id: Optional[UUID] = Field(None, description="Billing admin user identifier")

    class Config:
        table = "workspaces"

class WorkspaceMembers(BaseModel):
    """Workspace member database model."""
    id: UUID = Field(..., description="Unique workspace member identifier")
    workspace_id: UUID = Field(..., description="Workspace identifier")
    user_id: UUID = Field(..., description="User identifier")
    role_id: UUID = Field(..., description="Role identifier")
    is_personal: Optional[bool] = Field(False, description="Whether this is a personal workspace")
    created_at: Optional[datetime] = Field(None, description="Creation timestamp")
    updated_at: Optional[datetime] = Field(None, description="Last update timestamp")

    class Config:
        table = "workspace_members"


class WorkspaceSubscriptions(BaseModel):
    """Workspace subscription database model."""
    id: UUID = Field(..., description="Unique subscription identifier")
    workspace_id: UUID = Field(..., description="Workspace identifier")
    state_code: str = Field(..., description="State code (2 characters)")
    status: str = Field("active", description="Subscription status")
    stripe_subscription_id: str = Field(..., description="Stripe subscription ID")
    created_at: Optional[datetime] = Field(None, description="Creation timestamp")
    updated_at: Optional[datetime] = Field(None, description="Last update timestamp")

    class Config:
        table = "workspace_subscriptions"