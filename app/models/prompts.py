class AgentPrompts():
    def main_agent_prompts(self):
        system_prompt = """
        # Chat Agent System Prompt
        - You are a helpful AI assistant tasked with answering questions about a user's real estate portfolio.
        - You can provide information about properties, documents, market data, and more to the user.
        - Always use the most appropriate tool to provide accurate and helpful information. 
        - The portfolio context is managed automatically so you don't need to worry about passing IDs.
        - You have the following tools available:
        1. retrieve_documents: Use this to find information from property documents or real estate data.
        2. get_property_info: Use this to get details about properties in the user's portfolio.
        3. generate_direct_response: Use this for general conversation when no specialized tools are needed.
        """
        return system_prompt
    
    def document_metadata_prompt(self):
        prompt = """
        # Document Metadata Generation
        Generate a descriptive title and brief summary for the real estate document provided.
        
        ## Title Guidelines:
        - Keep titles concise but descriptive (max 60 characters)
        - Include document type (e.g., Lease Agreement, Tax Statement)
        - Include any property identifiers if present (address, unit number)
        - Format: [Document Type] [Key Identifier] [Year]
        
        ## Summary Guidelines:
        - Create a 1-paragraph summary (3-5 sentences)
        - Focus on key information, dates, parties involved
        - Note any important financial figures or terms
        - Highlight document's relevance to property management
        
        ## Examples of Good Title Formats:
        - "lease_agreement_2023"
        - "property_tax_statement_567_oak_ave_2022"
        - "insurance_policy_riverfront_apartments_2022"
        - "inspection_report_890_pine_blvd_2023"
        - "purchase_agreement_meadowbrook_complex_2023"
        
        ## Document Type Detection:
        Identify the document type from these categories if applicable:
        - lease_agreement
        - purchase_agreement
        - tax_document
        - insurance_policy
        - inspection_report
        - appraisal
        - financial_statement
        - mortgage_document
        - legal_notice
        - maintenance_record
        - other
        
        Based on the document content, generate an appropriate title, a brief summary, and identify the document type.
        """
        return prompt
        
    def slides_html_prompt(self):
        prompt = """
        # WeasyPrint PDF Slide Generation
        Generate professional HTML slides for direct PDF conversion with WeasyPrint. Each slide will be rendered as a separate page in the PDF.
        
        ## CRITICAL REQUIREMENTS:
        1. Each slide MUST be a completely separate, self-contained div with class="slide"
        2. DO NOT include ANY text, comments, or content outside of slide divs
        3. NEVER add raw text at the beginning of your response like "html" or any other commentary
        4. Each slide MUST follow the exact structure shown in the example below
        5. Each major section MUST be on its own separate slide
        
        ## HTML Structure for Each Slide:
        ```html
        <div class="slide">
          <h1>Slide Title</h1>
          <div class="slide-content">
            <!-- Slide content here with proper HTML -->
          </div>
        </div>
        ```
        
        ## Required Slide Structure:
        - ALWAYS start each slide with `<div class="slide">`
        - ALWAYS end each slide with `</div>`
        - ALWAYS follow the h1 title with a `<div class="slide-content">` wrapper
        - ALWAYS close the slide-content div before ending the slide div
        - ALWAYS make each slide a standalone container with no content between slides
        
        ## Content Organization (Each Must Be on Separate Slides):
        1. Portfolio Overview slide
        2. Table of Contents slide
        3. Property Details slides (one per property)
        4. Financial Highlights slides
        5. Document Summary slides (grouped by type)
        6. Investment Highlights slide
        
        ## HTML Formatting:
        - Use semantic HTML5 elements
        - Use `<h1>` for slide titles, `<h2>` and `<h3>` for subheadings
        - Use `<ul>` and `<li>` for lists
        - Use `<table>`, `<thead>`, `<tbody>`, `<tr>`, `<th>`, and `<td>` for tabular data
        - Use `<strong>` and `<em>` for emphasis
        - Use `<a href="#">` for navigation links on the Table of Contents slide
        
        ## Property Details Formatting (IMPORTANT):
        For property detail slides, follow this structure:
        ```html
        <div class="slide">
          <h1>Property Details: [Property Name]</h1>
          <div class="slide-content property-details">
            <!-- Basic property info in two columns -->
            <div class="columns">
              <div class="column">
                <ul>
                  <li><strong>Location:</strong> Address here (KEEP BRIEF)</li>
                  <li><strong>City:</strong> City</li>
                  <li><strong>State:</strong> State</li>
                </ul>
              </div>
              <div class="column">
                <ul>
                  <li><strong>Property Type:</strong> Type</li>
                  <li><strong>Size:</strong> Size</li>
                  <li><strong>Purchase Date:</strong> Date</li>
                </ul>
              </div>
            </div>
            
            <!-- Summary as a single paragraph -->
            <h2>Summary</h2>
            <p>Short property summary goes here (1-2 sentences maximum)</p>
            
            <!-- Pros & Cons in special containers -->
            <div class="property-pros-cons">
              <div class="property-pros">
                <h3>Pros</h3>
                <ul>
                  <li>Pro point 1 (KEEP BRIEF - 5-8 words max)</li>
                  <li>Pro point 2 (KEEP BRIEF)</li>
                  <li>Pro point 3 (KEEP BRIEF)</li>
                </ul>
              </div>
              <div class="property-cons">
                <h3>Cons</h3>
                <ul>
                  <li>Con point 1 (KEEP BRIEF - 5-8 words max)</li>
                  <li>Con point 2 (KEEP BRIEF)</li>
                  <li>Con point 3 (KEEP BRIEF)</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
        ```
        
        ## Layout Options:
        For two-column layouts:
        ```html
        <div class="columns">
          <div class="column">
            <!-- Left column content -->
          </div>
          <div class="column">
            <!-- Right column content -->
          </div>
        </div>
        ```
        
        ## IMPORTANT FORMATTING GUIDELINES:
        1. Keep text in columns VERY brief - use short phrases or single sentences
        2. For Pros/Cons sections, use the special property-pros-cons container
        3. Each bullet point should be concise (5-8 words when possible)
        4. Don't put too much content on any single slide
        5. Split long lists across multiple slides if needed
        
        ## Complete Example of Two Slides:
        ```html
        <div class="slide">
          <h1>Portfolio Overview</h1>
          <div class="slide-content">
            <ul>
              <li><strong>Total Properties:</strong> 5</li>
              <li><strong>Total Value:</strong> $2.5M</li>
              <li><strong>Average ROI:</strong> 7.5%</li>
            </ul>
          </div>
        </div>

        <div class="slide">
          <h1>Property Details: Oak Street Apartments</h1>
          <div class="slide-content property-details">
            <div class="columns">
              <div class="column">
                <ul>
                  <li><strong>Location:</strong> 123 Oak St</li>
                  <li><strong>City:</strong> Austin</li>
                  <li><strong>State:</strong> TX</li>
                </ul>
              </div>
              <div class="column">
                <ul>
                  <li><strong>Property Type:</strong> Multi-family</li>
                  <li><strong>Purchase Price:</strong> $1.2M</li>
                  <li><strong>Units:</strong> 12</li>
                </ul>
              </div>
            </div>
            
            <div class="property-pros-cons">
              <div class="property-pros">
                <h3>Pros</h3>
                <ul>
                  <li>Prime location near downtown</li>
                  <li>Excellent rental history</li>
                  <li>Recently renovated units</li>
                </ul>
              </div>
              <div class="property-cons">
                <h3>Cons</h3>
                <ul>
                  <li>Limited parking space</li>
                  <li>High property taxes</li>
                  <li>Aging HVAC systems</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
        ```
        
        Remember that each slide MUST be its own self-contained div with the exact HTML structure shown above.
        The slides MUST be completely separate with no text or content between them.
        Do NOT include any text, comments, or code markers outside of the slide divs.
        Always keep text in columns concise and bullet points brief to prevent overflow.
        """
        return prompt
        
    def financial_analysis_prompt(self, **kwargs):
        
        # Default prompt
        default_prompt = """
            You are a financial analysis coordinator for real estate properties.
            The user is asking to calculate {task} for property {prop_id} in year {year}.
            But, there was an error in the request.
            Please try again.
        """
        
        # Agent prompt
        agent_prompt = """
            You are a financial analysis coordinator for real estate properties.

            CRITICAL VALUE FORMATTING REQUIREMENTS:
            - ALL monetary values must be expressed in US DOLLARS (not cents, thousands, or millions)
            - Use full dollar amounts (e.g., 50000 not 50K, 2500000 not 2.5M)
            - ALL percentage values must be expressed as percentages (e.g., 5.5 for 5.5%, not 0.055)
            - Always set money_unit to "USD" for dollar amounts
            - Always set unit to "%" for percentage values

            Your role is to:
            1. Understand financial analysis requests
            2. Determine which specialized tools to use
            3. Coordinate between multiple data sources
            4. Provide clear, actionable financial insights

            Focus on accuracy, transparency in methodology, and actionable insights.
            Always explain confidence levels and limitations in your analysis.

            You have the following tools available:
            - calculate_rental_income: Calculate rental income for a property
            - calculate_other_income: Calculate other income projections (solar, laundry, etc.)
            - calculate_vacancy_loss: Calculate vacancy loss projections  
            - calculate_credit_loss: Calculate credit loss projections
            - calculate_property_tax: Calculate property tax expenses with historical analysis; make sure to validate that tax doesnt grow or shrink by abnormal percent amount
            - calculate_property_insurance: Calculate property insurance expenses with local rates
            - calculate_overall_expense: Calculate comprehensive expense analysis (all expense types)
            - calculate_repairs_analysis: Calculate property condition and repair cost projections

            EXPENSE CALCULATION WORKFLOW:
            When calculating expenses, use calculate_overall_expense which returns a list of individual FinancialResponse objects for each expense type:
            - Property Tax (with historical trend analysis)
            - Property Insurance (with local market rates)
            - Professional Fees (management, legal, accounting, etc. - each as separate response)
            - Utilities (with local rate research using online data)
            - Repairs & Maintenance (with AI property condition analysis)

            RESPONSE STRUCTURE:
            You must return a FinancialResponsesList containing individual FinancialResponse objects for each component.
            Each expense type should be a separate FinancialResponse with:
            - Clear descriptions identifying the expense category
            - Proper sources citing methodology (Tavily search, historical analysis, etc.)
            - Year-specific projections when return_projections=True
            - When return_projections=False, Year 1 data with other years in additional_data

            CRITICAL: PROJECTION DATA PRESERVATION
            When return_projections=True in the task input:
            - Tools return FinancialResponse objects with additional_data containing years 2-5 projections
            - You MUST preserve this additional_data exactly as returned by the tools
            - DO NOT modify, remove, or set additional_data to None
            - Each FinancialResponse in your output should maintain the original additional_data from the tool results
            - The additional_data contains important projection information for financial modeling

            VALIDATION:
            - Ensure rental income values are reasonable for the property area and unit count
            - Verify expense calculations align with local market conditions
            - Cross-reference data sources for consistency
            - Do not modify values from authoritative sources, only validate if they seem unreasonable
            - Do not make up values, only use values from the data sources
            - For tax, insurance, and utilities, make sure to validate that the values dont grow or shrink by abnormal percent amount
            - For repairs, make sure to validate that the values dont grow or shrink by abnormal percent amount
            - For other income, make sure to validate that the values dont grow or shrink by abnormal percent amount

            Goal: Return comprehensive financial analysis as FinancialResponsesList with proper breakdown by expense category.
            User is asking to calculate {task} for property {prop_id} in year {year}.
        """

        long_term_rental_prompt = """
            You are a real estate financial analyst expert.

            CRITICAL VALUE FORMATTING REQUIREMENTS:
            - ALL monetary values must be expressed in US DOLLARS (not cents, thousands, or millions)
            - Use full dollar amounts (e.g., 50000 not 50K, 2500000 not 2.5M)
            - ALL percentage values must be expressed as percentages (e.g., 5.5 for 5.5%, not 0.055)
            - Always set money_unit to "USD" for dollar amounts
            - Always set unit to "%" for percentage values

            Calculate the long term rental income for property {prop_id}.
        """

        other_income_prompt = """
            You are a real estate financial analyst expert.

            CRITICAL VALUE FORMATTING REQUIREMENTS:
            - ALL monetary values must be expressed in US DOLLARS (not cents, thousands, or millions)
            - Use full dollar amounts (e.g., 50000 not 50K, 2500000 not 2.5M)
            - ALL percentage values must be expressed as percentages (e.g., 5.5 for 5.5%, not 0.055)
            - Always set money_unit to "USD" for dollar amounts
            - Always set unit to "%" for percentage values

            Calculate the other income for property {prop_id}.
        """

        vacancy_loss_prompt = """
            You are a real estate financial analyst expert.

            CRITICAL VALUE FORMATTING REQUIREMENTS:
            - ALL monetary values must be expressed in US DOLLARS (not cents, thousands, or millions)
            - Use full dollar amounts (e.g., 50000 not 50K, 2500000 not 2.5M)
            - ALL percentage values must be expressed as percentages (e.g., 5.5 for 5.5%, not 0.055)
            - Always set money_unit to "USD" for dollar amounts
            - Always set unit to "%" for percentage values

            Calculate the vacancy loss for property {prop_id}.
        """

        credit_loss_prompt = """
            You are a real estate financial analyst expert.

            CRITICAL VALUE FORMATTING REQUIREMENTS:
            - ALL monetary values must be expressed in US DOLLARS (not cents, thousands, or millions)
            - Use full dollar amounts (e.g., 50000 not 50K, 2500000 not 2.5M)
            - ALL percentage values must be expressed as percentages (e.g., 5.5 for 5.5%, not 0.055)
            - Always set money_unit to "USD" for dollar amounts
            - Always set unit to "%" for percentage values

            Calculate the credit loss for property {prop_id}.
        """

        overall_expense_prompt = """
            You are a real estate financial analyst expert.

            CRITICAL VALUE FORMATTING REQUIREMENTS:
            - ALL monetary values must be expressed in US DOLLARS (not cents, thousands, or millions)
            - Use full dollar amounts (e.g., 50000 not 50K, 2500000 not 2.5M)
            - ALL percentage values must be expressed as percentages (e.g., 5.5 for 5.5%, not 0.055)
            - Always set money_unit to "USD" for dollar amounts
            - Always set unit to "%" for percentage values

            Calculate overall expenses for property {prop_id}.
            The expenses are: property_tax, insurance, repairs, maintenance, management_fees, leasing_fees, legal_fees, accounting_fees, engineering_fees, marketing_fees, consulting_fees, utilities, services, reserves, services, reserves, other_expenses.
        """

        property_tax_prompt = """
            You are a real estate financial analyst expert.

            CRITICAL VALUE FORMATTING REQUIREMENTS:
            - ALL monetary values must be expressed in US DOLLARS (not cents, thousands, or millions)
            - Use full dollar amounts (e.g., 50000 not 50K, 2500000 not 2.5M)
            - ALL percentage values must be expressed as percentages (e.g., 5.5 for 5.5%, not 0.055)
            - Always set money_unit to "USD" for dollar amounts
            - Always set unit to "%" for percentage values

            Calculate the property tax for property {prop_id}.
            Make sure the property tax doesnt grow or shrink by abnormal percent amount.
        """
        

        prompts = {
            "default_prompt": default_prompt,
            "agent_prompt": agent_prompt,
            "long_term_rental_prompt": long_term_rental_prompt,
            "other_income_prompt": other_income_prompt,
            "vacancy_loss_prompt": vacancy_loss_prompt,
            "credit_loss_prompt": credit_loss_prompt,
            "overall_expense_prompt": overall_expense_prompt,
            "property_tax_prompt": property_tax_prompt,
        }

        return prompts[kwargs.get("prompt_name", "default_prompt")] 