"""
RELM Database Models - External property rental listings database.

This module contains models for the external RELM database that stores
rental listings and property data.
"""
from uuid import UUID
from datetime import datetime
from typing import Optional, List, Dict, Any

from pydantic import BaseModel, Field
from app.utils.logger import get_logger

logger = get_logger(__name__)

class RentalListing(BaseModel):
    """
    Property rental listing model for external database.

    This model represents rental listings with property details,
    location information, and listing specifics.
    """
    
    # ID fields
    id: Optional[UUID] = Field(None, description="Unique listing identifier")
    address_id: Optional[int] = Field(None, description="Address identifier")
    mls_id: Optional[str] = Field(None, description="MLS identifier")
    data_id: Optional[UUID] = Field(None, description="Data identifier")
    build_id: Optional[UUID] = Field(None, description="Build identifier")
    
    # Location fields
    hoods: Optional[List[str]] = Field(None, description="Neighborhoods")
    
    # Property details
    beds: Optional[int] = Field(None, description="Number of bedrooms")
    full_baths: Optional[int] = Field(None, description="Number of full bathrooms")
    half_baths: Optional[int] = Field(None, description="Number of half bathrooms")
    sqft: Optional[float] = Field(None, description="Square footage")
    year_built: Optional[int] = Field(None, description="Year built")
    lot_sqft: Optional[float] = Field(None, description="Lot square footage")
    stories: Optional[int] = Field(None, description="Number of stories")
    hoa_fee: Optional[float] = Field(None, description="HOA fee")
    garage: Optional[int] = Field(None, description="Number of garage spaces")
    sold_price: Optional[float] = Field(None, description="Sold price")
    description_type: Optional[str] = Field(None, description="Description type")
    description_text: Optional[str] = Field(None, description="Description text")
    llm_summary: Optional[str] = Field(None, description="LLM generated summary")
    llm_keywords: Optional[Dict[str, Any]] = Field(None, description="LLM keywords")
    
    # Listing details
    list_price: Optional[float] = Field(None, description="List price (rent amount)")
    list_price_max: Optional[float] = Field(None, description="Maximum list price")
    list_price_min: Optional[float] = Field(None, description="Minimum list price")
    list_date: Optional[datetime] = Field(None, description="List date")
    pending_date: Optional[datetime] = Field(None, description="Pending date")
    last_sold_price: Optional[float] = Field(None, description="Last sold price")
    last_sold_date: Optional[datetime] = Field(None, description="Last sold date")
    price_per_sqft: Optional[float] = Field(None, description="Price per square foot")
    tax_record: Optional[Dict[str, Any]] = Field(None, description="Tax record data")
    
    # Flags
    is_contingent: Optional[bool] = Field(None, description="Is contingent")
    is_pending: Optional[bool] = Field(None, description="Is pending")
    is_for_rent: Optional[bool] = Field(None, description="Is for rent")
    is_vector_started: Optional[bool] = Field(False, description="Vector processing started")
    is_vector_complete: Optional[bool] = Field(False, description="Vector processing complete")
    
    # Agent and broker details
    advertiser_id: Optional[UUID] = Field(None, description="Advertiser identifier")
    source_id: Optional[UUID] = Field(None, description="Source identifier")
    source_listing_id: Optional[str] = Field(None, description="Source listing identifier")
    
    # Additional information
    nearby_schools: Optional[Dict[str, Any]] = Field(None, description="Nearby schools data")
    tags: Optional[Dict[str, Any]] = Field(None, description="Tags data")
    tags_list: Optional[List[str]] = Field(None, description="Tags list")
    slug: Optional[str] = Field(None, description="URL slug")

    class Config:
        table = "dev_rental_listings"

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary representation."""
        return self.model_dump(exclude_none=True)


class RentalAddress(BaseModel):
    """
    Rental address model for external database.
    
    Addresses master list. Unique US addresses.
    This table is used to store all addresses that are being used in the system.
    """
    
    id: Optional[int] = Field(None, description="Unique address identifier")
    building_id: Optional[int] = Field(None, description="Building identifier")
    street_number: Optional[str] = Field(None, description="Street number")
    street_name: Optional[str] = Field(None, description="Street name")
    street_direction: Optional[str] = Field(None, description="Street direction")
    street_suffix: Optional[str] = Field(None, description="Street suffix")
    unit: Optional[str] = Field(None, description="Unit number")
    city: Optional[str] = Field(None, description="City")
    state: Optional[str] = Field(None, description="State code (2 characters)")
    zipcode: Optional[str] = Field(None, description="ZIP code")
    county: Optional[str] = Field(None, description="County")
    borough: Optional[str] = Field(None, description="Borough")
    latitude: Optional[float] = Field(None, description="Latitude")
    longitude: Optional[float] = Field(None, description="Longitude")
    country_code_iso3611: Optional[str] = Field(None, description="Country code ISO 3611")
    
    class Config:
        table = "dev_rental_addresses"
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert the RentalAddress to a dictionary."""
        return self.model_dump(exclude_none=True)
    
    def to_addr1_string(self) -> str:
        """Generate formatted address line 1 string."""
        def capitalize_component(component: Optional[str]) -> str:
            if not component:
                return ''
            if any(char.isdigit() for char in component):
                return component.lower()
            return component.title()
        
        components = [
            capitalize_component(self.street_number.strip()) if self.street_number else '',
            capitalize_component(self.street_direction.strip()) if self.street_direction else '',
            capitalize_component(self.street_name.strip()) if self.street_name else '',
            capitalize_component(self.street_suffix.strip()) if self.street_suffix else ''
        ]
        return ' '.join(filter(None, components))
    
    def to_address(self) -> Optional[Dict[str, Any]]:
        """Generate complete address dictionary with formatted components."""
        try:
            components = [
                self.street_number.strip().title() if self.street_number else '',
                self.street_direction.strip().title() if self.street_direction else '',
                self.street_name.strip().title() if self.street_name else '',
                self.street_suffix.strip().title() if self.street_suffix else '',
                self.unit.strip().title() if self.unit else '',
            ]
            addr_1 = ' '.join(filter(None, components))
            
            def normalize(s: Optional[str]) -> str:
                return ' '.join(s.lower().split()) if s else ''

            borough_norm = normalize(self.borough)
            city_norm = normalize(self.city)
            
            if borough_norm and city_norm:
                if borough_norm == city_norm or borough_norm in city_norm or city_norm in borough_norm:
                    addr_2 = f"{self.borough.title() if self.borough else ''}, {self.state}, {self.zipcode}"
                else:
                    addr_2 = f"{self.borough.title() if self.borough else ''}, {self.city.title() if self.city else ''}, {self.state}, {self.zipcode}"
            else:
                addr_2 = f"{self.city.title() if self.city else ''}, {self.state}, {self.zipcode}"

            return {
                "addr_1": addr_1,
                "addr_2": addr_2,
                "unit": self.unit.title() if self.unit else '',
                "lat": self.latitude,
                "lng": self.longitude,
                "building_id": self.building_id,
                "address_details": {
                    "street_number": self.street_number,
                    "street_name": self.street_name,
                    "street_direction": self.street_direction,
                    "street_suffix": self.street_suffix,
                    "unit": self.unit,
                    "borough": self.borough,
                    "city": self.city,
                    "state": self.state,
                    "zipcode": self.zipcode
                }
            }
        except Exception as e:
            logger.error(f"Error generating address: {str(e)}, address: {self.to_dict()}")
            return None
    
    def to_address_string(self) -> str:
        """Generate complete address as a single string."""
        components = [
            self.street_number.title() if self.street_number else '',
            self.street_name.title() if self.street_name else '',
            self.street_direction.title() if self.street_direction else '',
            self.street_suffix.title() if self.street_suffix else '',
            self.unit.title() if self.unit else '',
            self.city.title() if self.city else '',
            self.state.title() if self.state else '',
            self.zipcode.title() if self.zipcode else ''
        ]
        return ' '.join(filter(None, components))