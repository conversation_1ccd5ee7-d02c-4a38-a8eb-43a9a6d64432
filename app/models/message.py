"""
Pydantic models for chat messages and related data structures.
"""
from enum import Enum
from uuid import UUID

from datetime import datetime
from typing import Dict, List, Optional, Union, Any
from pydantic import BaseModel, Field, ConfigDict

class MessageRole(str, Enum):
    """User roles in a conversation."""
    USER = "user"
    ASSISTANT = "assistant"
    SYSTEM = "system"


class MessageContent(BaseModel):
    """Content of a chat message."""
    text: str = Field(..., description="The text content of the message")
    image_urls: Optional[List[str]] = Field(None, description="List of image URLs attached to the message")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata for the message")
    
    
class Message(BaseModel):
    """
    A single message in a conversation between a user and the AI assistant.
    """
    id: Optional[str] = Field(None, description="Unique message identifier")
    role: MessageRole = Field(..., description="Role of the message sender")
    content: MessageContent = Field(..., description="Content of the message")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="Message creation timestamp")
    
    # Additional fields for database integration
    user_id: Optional[UUID] = Field(None, description="User identifier for database storage")
    chat_id: Optional[UUID] = Field(None, description="Chat identifier for database storage")

    # Updated model config for JSON serialization
    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "id": "msg_123456789",
                "role": "user",
                "content": {
                    "text": "Hello, how can you help me today?",
                    "image_urls": ["https://example.com/image1.jpg"],
                    "metadata": {"source": "web_client"}
                },
                "timestamp": "2023-09-15T10:30:00",
                "user_id": "550e8400-e29b-41d4-a716-************",
                "chat_id": "550e8400-e29b-41d4-a716-************"
            }
        },
        # Ensure datetime is serialized correctly
        json_encoders={
            datetime: lambda dt: dt.isoformat() if dt else None
        }
    )
    
    def model_dump(self, *args, **kwargs) -> Dict[str, Any]:
        """
        Override model_dump method to ensure datetime is serialized correctly.
        This works with newer versions of Pydantic.
        """
        result = super().model_dump(*args, **kwargs)
        # Convert datetime to string
        if "timestamp" in result and isinstance(result["timestamp"], datetime):
            result["timestamp"] = result["timestamp"].isoformat()
        return result
    
    def dict(self, *args, **kwargs) -> Dict[str, Any]:
        """
        Override dict method to ensure datetime is serialized correctly.
        This works with older versions of Pydantic.
        """
        try:
            # Try the new method first
            return self.model_dump(*args, **kwargs)
        except AttributeError:
            # Fall back to the old method
            result = super().dict(*args, **kwargs)
            # Convert datetime to string
            if "timestamp" in result and isinstance(result["timestamp"], datetime):
                result["timestamp"] = result["timestamp"].isoformat()
            return result


class Chat(BaseModel):
    """A chat consisting of multiple messages."""
    id: Optional[str] = Field(None, description="Unique chat identifier")
    messages: List[Message] = Field(default_factory=list, description="List of messages in the chat")
    metadata: Dict[str, Union[str, int, float, bool]] = Field(
        default_factory=dict,
        description="Additional metadata for the chat"
    )
    created_at: datetime = Field(default_factory=datetime.utcnow, description="Chat creation timestamp")
    updated_at: Optional[datetime] = Field(None, description="Chat last update timestamp")
    
    # Updated model config for JSON serialization
    model_config = ConfigDict(
        # Ensure datetime is serialized correctly
        json_encoders={
            datetime: lambda dt: dt.isoformat() if dt else None
        }
    )
    
    def model_dump(self, *args, **kwargs) -> Dict[str, Any]:
        """
        Override model_dump method to ensure datetime is serialized correctly.
        This works with newer versions of Pydantic.
        """
        result = super().model_dump(*args, **kwargs)
        # Convert datetime to string
        if "created_at" in result and isinstance(result["created_at"], datetime):
            result["created_at"] = result["created_at"].isoformat()
        if "updated_at" in result and isinstance(result["updated_at"], datetime):
            result["updated_at"] = result["updated_at"].isoformat()
        return result
    
    def dict(self, *args, **kwargs) -> Dict[str, Any]:
        """
        Override dict method to ensure datetime is serialized correctly.
        This works with older versions of Pydantic.
        """
        try:
            # Try the new method first
            return self.model_dump(*args, **kwargs)
        except AttributeError:
            # Fall back to the old method
            result = super().dict(*args, **kwargs)
            # Convert datetime to string
            if "created_at" in result and isinstance(result["created_at"], datetime):
                result["created_at"] = result["created_at"].isoformat()
            if "updated_at" in result and isinstance(result["updated_at"], datetime):
                result["updated_at"] = result["updated_at"].isoformat()
            return result 