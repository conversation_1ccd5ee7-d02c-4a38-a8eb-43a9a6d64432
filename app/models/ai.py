"""
Pydantic models for AI response structures.
"""
from pydantic import BaseModel, Field
from dataclasses import dataclass, field
from typing import Dict, List, Optional, Any

from app.services.property_service import PropertyService

class PropertyProsCons(BaseModel):
    """Pros and cons of a property."""
    pros: List[str] = Field(..., description="List of property pros/advantages")
    cons: List[str] = Field(..., description="List of property cons/disadvantages")


class PropertyFinancialProspects(BaseModel):
    """Financial prospects for a property."""
    cash_flow_potential: str = Field(..., description="Description of cash flow potential")
    appreciation_outlook: str = Field(..., description="Outlook on property appreciation")
    investment_grade: str = Field(..., description="Overall investment grade or rating")
    risk_factors: List[str] = Field(..., description="List of financial risk factors")
    opportunity_areas: List[str] = Field(..., description="Areas for financial improvement")


class PropertyAISummary(BaseModel):
    """AI-generated property summary response."""
    summary: str = Field(..., description="General summary of the property")
    pros_cons: PropertyProsCons = Field(..., description="Property pros and cons analysis")
    poi_summary: str = Field(..., description="Summary of points of interest near the property")
    financial_prospects: PropertyFinancialProspects = Field(..., description="Financial prospects analysis")
    additional_insights: Optional[Dict[str, Any]] = Field(None, description="Any additional AI insights")


class DocumentAIMetadata(BaseModel):
    """AI-generated document metadata."""
    title: str = Field(..., description="Generated title for the document")
    summary: str = Field(..., description="Brief summary of the document content")
    document_type: Optional[str] = Field(None, description="Detected document type (lease, tax, etc.)")

class SlidesResponse(BaseModel):
    """Response model for generated PDF slides."""
    success: bool = Field(..., description="Whether the slides generation was successful")
    pdf_url: str = Field(..., description="URL to access the generated PDF")
    pdf_name: str = Field(..., description="Name of the generated PDF file")
    document_count: int = Field(..., description="Number of documents processed")
    property_count: int = Field(..., description="Number of properties included")
    slides_data_id: str = Field(..., description="ID of the entry in the slides_data table (can be integer or string/UUID)")
    portfolio_id: str = Field(..., description="ID of the portfolio these slides are for") 

@dataclass
class FinancialTaskInput:
    task_type: str
    portfolio_id: str
    prop_id: str 
    year: int
    db: PropertyService
    params: Dict[str, Any] = field(default_factory=dict)

class Source(BaseModel):
    source: str
    description: str
    url: Optional[str] = Field(None, description="URL of the source")

class FinancialResponse(BaseModel):
    """Financial response model."""
    task_type: Optional[str] = Field(None, description="Echo back the task type")
    portfolio_id: Optional[str] = Field(None, description="Portfolio ID")
    prop_id: Optional[str] = Field(None, description="Property ID")
    year: Optional[int] = Field(None, description="Year of the calculation: 1, 2, 3, etc.")
    sources: Optional[List[Source]] = Field(None, description="Sources of the result")
    value: Optional[float] = Field(None, description="Value of the result, always return as a percentage not a fraction or a decimal (e.g., 5.5 for 5.5%, not 0.055)")
    unit: Optional[str] = Field(None, description="Unit of the result: $, %, count, etc.")
    money_value: Optional[float] = Field(None, description="Money value of the result in US dollars (not cents, thousands, or millions). Always express as full dollar amount (e.g., 50000 not 50K)")
    money_unit: Optional[str] = Field(None, description="Money unit of the result: always use 'USD' for US dollar amounts")
    description: Optional[str] = Field(None, description="Short description of the result. Format to proper English sentence, with full, complete words.")
    additional_data: Optional[Dict[str, 'FinancialResponse']] = Field(None, description="Additional data for the result")
    def to_dict(self) -> Dict[str, Any]:
        """Convert the FinancialResponse to a dictionary."""
        return self.model_dump(exclude_none=True)

class FinancialResponsesList(BaseModel):
    """List of FinancialResponse objects."""
    responses: List[FinancialResponse] = Field(..., description="List of financial responses")
    def to_dict(self) -> Dict[str, Any]:
        """Convert the FinancialResponsesList to a dictionary."""
        return self.model_dump(exclude_none=True)
