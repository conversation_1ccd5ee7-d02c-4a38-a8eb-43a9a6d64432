"""
Pydantic models for AI response structures.
"""
from pydantic import BaseModel, Field
from typing import Dict, List, Optional, Any


class PropertyProsCons(BaseModel):
    """Pros and cons of a property."""
    pros: List[str] = Field(..., description="List of property pros/advantages")
    cons: List[str] = Field(..., description="List of property cons/disadvantages")


class PropertyFinancialProspects(BaseModel):
    """Financial prospects for a property."""
    cash_flow_potential: str = Field(..., description="Description of cash flow potential. Around 100 words.")
    appreciation_outlook: str = Field(..., description="Outlook on property appreciation. Around 100 words.")
    investment_grade: str = Field(..., description="Overall investment grade or rating.")
    risk_factors: List[str] = Field(..., description="List of financial risk factors.")
    opportunity_areas: List[str] = Field(..., description="Areas for financial improvement")


class PropertyAISummary(BaseModel):
    """AI-generated property summary response."""
    summary: str = Field(..., description="General summary of the property. Around 200 words.")
    pros_cons: PropertyProsCons = Field(..., description="Property pros and cons analysis")
    poi_summary: str = Field(..., description="Summary of points of interest near the property. Around 100 words.")
    financial_prospects: PropertyFinancialProspects = Field(..., description="Financial prospects analysis")
    additional_insights: Optional[Dict[str, Any]] = Field(None, description="Any additional AI insights")
    property_attributes: List[str] = Field(None, description="List of property attributes")
    market_conditions: str = Field(None, description="Market conditions and trends. Around 100 words.")


class DocumentAIMetadata(BaseModel):
    """AI-generated document metadata."""
    title: str = Field(..., description="Generated title for the document")
    summary: str = Field(..., description="Brief summary of the document content")
    document_type: Optional[str] = Field(None, description="Detected document type (lease, tax, etc.)")

class SlidesResponse(BaseModel):
    """Response model for generated PDF slides."""
    success: bool = Field(..., description="Whether the slides generation was successful")
    pdf_url: str = Field(..., description="URL to access the generated PDF")
    pdf_name: str = Field(..., description="Name of the generated PDF file")
    document_count: int = Field(..., description="Number of documents processed")
    property_count: int = Field(..., description="Number of properties included")
    slides_data_id: str = Field(..., description="ID of the entry in the slides_data table (can be integer or string/UUID)")
    portfolio_id: str = Field(..., description="ID of the portfolio these slides are for") 

