"""
FRED (Federal Reserve Economic Data) Service Module.

This module provides functionality to interact with the FRED API
and retrieve economic data series like rental price indices, inflation rates, etc.
"""
import pandas as pd
from typing import Dict, List, Optional, Any

from app.utils.logger import get_logger
from app.utils.env_cache import get_env

from fredapi import Fred

logger = get_logger(__name__)

class FredDataService:
    """Service for interacting with the FRED API."""
    
    def __init__(self, api_key: Optional[str] = None):
        """Initialize the FRED service."""
        self.api_key = api_key or get_env('FRED_API_KEY')
        self.fred_client = None

        try:
            self.fred_client = Fred(api_key=self.api_key)
            logger.info("FRED client initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize FRED client: {str(e)}")


    def get_fred_data(
        self, 
        series_id: str, 
        start_date: Optional[str] = None, 
        end_date: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """
        Get FRED data series by ID.
        
        Args:
            series_id: FRED series identifier (e.g., "CUUR0000SEHA")
            start_date: Start date in YYYY-MM-DD format (optional)
            end_date: End date in YYYY-MM-DD format (optional)
            
        Returns:
            List of dictionaries with date and value keys
        """
        if not self.fred_client:
            logger.error("FRED client not available")
            return []
            
        try:
            logger.info(f"Fetching FRED data for series: {series_id}")
            
            # Get data series
            data = self.fred_client.get_series(
                series_id, 
                observation_start=start_date, 
                observation_end=end_date
            )
            
            if data is None or data.empty:
                logger.warning(f"No data returned for series {series_id}")
                return []
            
            # Convert pandas Series to list of dictionaries
            result = []
            for date, value in data.items():
                if pd.notna(value):  # Skip NaN values
                    result.append({
                        'date': date.strftime('%Y-%m-%d'),
                        'value': float(value)
                    })
            
            logger.info(f"Retrieved {len(result)} data points for series {series_id}")
            return result
            
        except Exception as e:
            logger.error(f"Error fetching FRED data for series {series_id}: {str(e)}")
            return []

    def get_rental_price_index(
        self, 
        start_date: Optional[str] = None, 
        end_date: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """
        Get the CPI for Rent of Primary Residence data series.
        
        Args:
            start_date: Start date in YYYY-MM-DD format (optional)
            end_date: End date in YYYY-MM-DD format (optional)
            
        Returns:
            List of dictionaries with date and value keys
        """
        # CUUR0000SEHA = Consumer Price Index for All Urban Consumers: Rent of Primary Residence
        return self.get_fred_data("CUUR0000SEHA", start_date, end_date)

    def get_inflation_rate(
        self, 
        start_date: Optional[str] = None, 
        end_date: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """
        Get the Consumer Price Index for All Urban Consumers: All Items.
        
        Args:
            start_date: Start date in YYYY-MM-DD format (optional)
            end_date: End date in YYYY-MM-DD format (optional)
            
        Returns:
            List of dictionaries with date and value keys
        """
        # CPIAUCSL = Consumer Price Index for All Urban Consumers: All Items
        return self.get_fred_data("CPIAUCSL", start_date, end_date)

# Convenience function for easy import
def get_fred_rental_data(
    series_id: str = "CUUR0000SEHA",
    start_date: Optional[str] = None, 
    end_date: Optional[str] = None
) -> List[Dict[str, Any]]:
    """
    Convenience function to get FRED rental price data.
    
    Args:
        series_id: FRED series ID (default: rental price index)
        start_date: Start date in YYYY-MM-DD format
        end_date: End date in YYYY-MM-DD format
        
    Returns:
        List of dictionaries with date and value keys
    """
    fred_service = FredDataService()
    return fred_service.get_fred_data(series_id, start_date, end_date)
