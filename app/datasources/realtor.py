"""
Realtor.com API data service module.

This module provides functionality to interact with the Realtor.com GraphQL API
and retrieve property data based on addresses and postal codes.
"""
import re
import json
import time
import traceback
import urllib.request
import urllib.error

from enum import Enum
from decimal import Decimal
from datetime import datetime, timed<PERSON>ta
from typing import Dict, Any, List, Optional

from app.utils.logger import get_logger
from app.utils.env_cache import get_env
from app.repository.cache import redis_cache

from scrapingbee import ScrapingBeeClient

logger = get_logger(__name__)

# Constants
SEARCH_GQL_URL = "https://www.realtor.com/api/v1/rdc_search_srp?client_id=rdc-search-new-communities&schema=vesta"
PROPERTY_URL = "https://www.realtor.com/realestateandhomes-detail/"
PROPERTY_GQL = "https://graph.realtor.com/graphql"
DEFAULT_PAGE_SIZE = 100

# Proxy settings
SCRAPING_BEE_API_KEY: str = get_env("SCRAPING_BEE_API_KEY") or ""
if not SCRAPING_BEE_API_KEY:
    raise ValueError("SCRAPING_BEE_API_KEY environment variable must be set and non-empty.")
sb_client = ScrapingBeeClient(api_key=SCRAPING_BEE_API_KEY)

# Cache settings
CACHING_ENABLED: bool = True
REALTOR_CACHE_TTL: int = 86400  # 24 hours in seconds

class ListingType(Enum):
    """Enum representing different types of property listings."""
    FOR_RENT = "for_rent"
    FOR_SALE = "for_sale"
    PENDING = "pending"
    OFF_MARKET = "off_market"
    SOLD = "sold"

class CustomJSONEncoder(json.JSONEncoder):
    """Custom JSON encoder to handle non-serializable objects."""
    def default(self, obj):
        if isinstance(obj, Enum):
            return obj.value
        if hasattr(obj, '__dict__'):
            return obj.__dict__
        if isinstance(obj, (datetime, timedelta)):
            return str(obj)
        return str(obj)

class RealtorDataService:
    """
    Service for interacting with the Realtor.com GraphQL API.
    Provides methods to fetch property data with caching support.
    """

    @staticmethod
    def key_factory(full_address: str, data_type: str) -> str:
        """
        Generate a standardized cache key for a given address.
        
        Args:
            full_address: Full address string
            
        Returns:
            str: Formatted cache key
        """
        # Replace spaces with hyphens and remove any characters that could cause issues in Redis keys
        sanitized_address = full_address.replace(' ', '-')
        sanitized_address = ''.join(c for c in sanitized_address if c.isalnum() or c in '-_:')
        return f"prop_search:{sanitized_address}:{data_type}"
    
    @staticmethod
    def convert_floats_to_decimal(obj):
        """
        Convert float values to Decimal for DynamoDB compatibility.
        
        Args:
            obj: The object to convert
            
        Returns:
            Object with floats converted to Decimal
        """
        if isinstance(obj, list):
            return [RealtorDataService.convert_floats_to_decimal(x) for x in obj]
        elif isinstance(obj, dict):
            return {k: RealtorDataService.convert_floats_to_decimal(v) for k, v in obj.items()}
        elif isinstance(obj, float):
            return Decimal(str(obj))
        else:
            return obj
    
    @staticmethod
    async def post_request(url: str, payload: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        Send a POST request using ScrapingBee client with fallback to urllib.
        
        Args:
            url: The URL to send the request to.
            payload: The payload dictionary.
            
        Returns:
            Parsed JSON response as a dictionary, or None if an error occurs.
        """
        try:
            # First try using ScrapingBee client
            logger.info(f"Attempting request via ScrapingBee to {url}")
            data = json.dumps(payload)
            response = sb_client.get(
                url,
                params={
                    'headers': json.dumps({"Content-Type": "application/json"})
                }
            )
            
            if response.status_code == 200:
                logger.info("ScrapingBee request successful")
                return json.loads(response.text)
            else:
                logger.warning(f"ScrapingBee request failed with status {response.status_code}, falling back to urllib")
                
            # Fallback to urllib if ScrapingBee fails
            data_bytes = data.encode("utf-8")
            req = urllib.request.Request(url, data=data_bytes, headers={"Content-Type": "application/json"}, method="POST")
            with urllib.request.urlopen(req) as response:
                response_body = response.read().decode("utf-8")
            return json.loads(response_body)
        except urllib.error.HTTPError as e:
            logger.error(f"HTTP error in post_request: {e}")
            return None
        except urllib.error.URLError as e:
            logger.error(f"URL error in post_request: {e}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error in post_request: {e}")
            logger.error(traceback.format_exc())
            return None
    
    @staticmethod
    async def general_search(start_date: str, 
                       end_date: str, 
                       listing_type: ListingType,               
                       address: Optional[str] = None,  
                       postal_code: Optional[str] = None) -> Dict[str, Any]:
        """
        Search for properties with the given criteria using Realtor.com's GraphQL API.
        
        Args:
            start_date: Start date for the search range in YYYY-MM-DD format.
            end_date: End date for the search range in YYYY-MM-DD format.
            listing_type: Type of listing to search for (from ListingType enum).
            address: Optional address to search for.
            postal_code: Postal code to search in.
            
        Returns:
            Dictionary with the total count and list of properties.
        """
        try:
            logger.info(f"Starting general_search with: start_date={start_date}, end_date={end_date}, listing_type={listing_type}, address={address}, postal_code={postal_code}")
            date_param = f'list_date: {{ min: "{start_date}", max: "{end_date}" }}'
            sort_param = "sort: [{ field: list_date, direction: desc }]"
            results_query = """{
                                count
                                total
                                results {
                                    pending_date
                                    property_id
                                    list_date
                                    status
                                    last_sold_price
                                    last_sold_date
                                    list_price
                                    list_price_max
                                    list_price_min
                                    price_per_sqft
                                    tags
                                    flags {
                                        is_contingent
                                        is_pending
                                        is_for_rent
                                    }
                                    description {
                                        type
                                        sqft
                                        beds
                                        baths_full
                                        baths_half
                                        lot_sqft
                                        sold_price
                                        year_built
                                        garage
                                        sold_price
                                        type
                                        name
                                        stories
                                        text
                                    }
                                    source {
                                        agents {
                                            agent_id
                                            agent_name
                                            agent_phone
                                            agent_email
                                            office_id
                                            office_name
                                            office_phone
                                            type
                                        }
                                        id
                                        listing_id
                                        disclaimer {
                                            logo {
                                                href
                                            }
                                            text
                                            href
                                        }
                                    }
                                    hoa {
                                        fee
                                    }
                                    advertisers {
                                        type
                                        name
                                        email
                                        state_license
                                        nrds_id
                                        broker {
                                            name
                                            accent_color
                                            logo
                                            designations
                                        }
                                        photo {
                                            href
                                        }
                                        phones {
                                            number
                                            type
                                            primary
                                            trackable
                                            ext
                                        }
                                    }
                                    location {
                                        address {
                                            street_direction
                                            street_number
                                            street_name
                                            street_suffix
                                            line
                                            unit
                                            city
                                            state_code
                                            postal_code
                                            coordinate {
                                                lon
                                                lat
                                            }
                                        }
                                        county {
                                            name
                                            fips_code
                                        }
                                        neighborhoods {
                                            name
                                        }
                                    }
                                    tax_record {
                                        public_record_id
                                    }
                                    primary_photo {
                                        href
                                    }
                                    photos {
                                        href
                                    }
                                }
                            }
                        }"""

            search_variables: Dict[str, Any] = {"offset": 0}

            if address:
                location_declaration = "$address: String, $postal_code: String"
                location_param = 'address: $address, postal_code: $postal_code'
                search_variables["address"] = str(address)
                search_variables["postal_code"] = str(postal_code)
            elif postal_code:
                location_declaration = "$postal_code: String"
                location_param = 'postal_code: $postal_code'
                search_variables["postal_code"] = str(postal_code)
            else:
                raise ValueError("Either address and postal_code, or just postal_code must be provided")

            query = """query Home_search(
                                    %s
                                    $offset: Int,
                                ) {
                                    home_search(
                                        query: {
                                            %s
                                            status: %s
                                            %s
                                        }
                                        %s
                                        limit: %d
                                        offset: $offset
                                    ) %s""" % (
                    location_declaration,
                    location_param,
                    listing_type.value.lower(),
                    date_param,
                    sort_param,
                    DEFAULT_PAGE_SIZE,
                    results_query,
                )
            
            payload = {
                "query": query,
                "variables": search_variables
            }
            
            logger.info(f"GraphQL query constructed: variables={search_variables}")
            
            async def fetch_page(offset: int) -> List[Any]:
                try:
                    logger.info(f"Fetching page with offset {offset}")
                    page_variables = search_variables.copy()
                    page_variables["offset"] = offset
                    local_payload = {"query": query, "variables": page_variables}
                    result = await RealtorDataService.post_request(SEARCH_GQL_URL, local_payload)
                    if not result:
                        return []
                    if 'errors' in result:
                        logger.warning(f"GraphQL errors in response: {result['errors']}")
                        return []
                    results = result.get('data', {}).get('home_search', {}).get('results', None)
                    return results if results is not None else []
                except Exception as e:
                    logger.error(f"Error fetching page with offset {offset}: {str(e)}")
                    logger.error(traceback.format_exc())
                    return []
            
            # Initial request
            logger.info(f"Making initial GraphQL request to {SEARCH_GQL_URL}")
            initial_response = await RealtorDataService.post_request(SEARCH_GQL_URL, payload)
            if not initial_response:
                return {"total": 0, "properties": []}
                
            if 'errors' in initial_response:
                logger.warning(f"GraphQL errors in main response: {initial_response['errors']}")
                return {"total": 0, "properties": []}
                
            total_results = initial_response.get('data', {}).get('home_search', {}).get('total', 0)
            count = initial_response.get('data', {}).get('home_search', {}).get('count', 0)
            all_results = initial_response.get('data', {}).get('home_search', {}).get('results', [])
            
            logger.info(f"Initial response: total={total_results}, count={count}, results={len(all_results) if all_results else 0}")
            
            # Filter out None values
            all_results = [r for r in all_results if r is not None]
            
            # Only fetch additional pages if needed
            if total_results > count and count > 0:
                remaining_pages = min((total_results - count) // DEFAULT_PAGE_SIZE + 1, 5)  # Limit to 5 pages max
                logger.info(f"Fetching {remaining_pages} additional pages")
                for page in range(1, remaining_pages + 1):
                    offset = page * DEFAULT_PAGE_SIZE
                    results = await fetch_page(offset)
                    if results:
                        all_results.extend([r for r in results if r is not None])
                                
            logger.info(f"Completed general_search with {len(all_results)} total results")
            return {"total": total_results, "properties": all_results}
                
        except Exception as e:
            logger.error(f"Error in general_search: {str(e)}")
            logger.error(traceback.format_exc())
            return {"total": 0, "properties": []}
    
    @staticmethod
    @staticmethod
    async def get_listing_history(
        full_address: str,
        address: Optional[str],
        zipcode: Optional[str] = None,
        past_days: int = 1825
    ) -> Dict[str, Any]:
        """
        Fetch property listings for a location and group them by address and unit.

        Args:
            full_address: The full address string for cache key.
            address: The address to search for properties.
            zipcode: Postal code to search in (optional).
            past_days: Number of past days to search (default: 5 years/1825 days).

        Returns:
            Dictionary with 'count' and a list of property groups, each with address, unit, and data.
        """

        def clean_address(addr: Optional[str]) -> Optional[str]:
            """
            Shorten common street suffixes in the address using regex.
            Handles all cases and returns the address in sentence case.
            """
            if not addr:
                return addr
            # Define common suffix replacements (case-insensitive)
            suffix_map = {
                r'\bavenue\b': 'ave',
                r'\bav\b': 'ave',
                r'\bstreet\b': 'st',
                r'\broad\b': 'rd',
                r'\bboulevard\b': 'blvd',
                r'\bdrive\b': 'dr',
                r'\bplace\b': 'pl',
                r'\bterrace\b': 'ter',
                r'\bcourt\b': 'ct',
                r'\blane\b': 'ln',
                r'\bparkway\b': 'pkwy',
                r'\bhighway\b': 'hwy',
                r'\btrail\b': 'trl',
                r'\bcenter\b': 'ctr',
                r'\bplaza\b': 'plz',
                r'\bsquare\b': 'sq',
                r'\bloop\b': 'loop',
                r'\bway\b': 'way',
                r'\bmountain\b': 'mtn',
                r'\bpoint\b': 'pt',
                r'\bdriveway\b': 'drwy',
                r'\bexpressway\b': 'expy',
                r'\bfreeway\b': 'fwy',
                r'\bjunction\b': 'jct',
                r'\bturnpike\b': 'tpke',
                r'\broute\b': 'rte',
                r'\bextension\b': 'ext',
                r'\bcrest\b': 'crst',
                r'\bview\b': 'vw',
                r'\bwalk\b': 'walk',
                r'\bpark\b': 'park',
                r'\bmeadow\b': 'mdw',
                r'\bfield\b': 'fld',
                r'\bvalley\b': 'vly',
                r'\bheights\b': 'hts',
                r'\bglen\b': 'gln',
                r'\bforest\b': 'frst',
                r'\bharbor\b': 'hbr',
                r'\bmanor\b': 'mnr',
                r'\bmission\b': 'msn',
                r'\borchard\b': 'orch',
                r'\bpass\b': 'pass',
                r'\bridge\b': 'rdg',
                r'\brun\b': 'run',
                r'\bshore\b': 'shr',
                r'\bwoods\b': 'wds',
                r'\bfort\b': 'ft',
                r'\bmount\b': 'mt',
                r'\bsaint\b': 'st',
            }
            cleaned = addr.lower()
            for pattern, replacement in suffix_map.items():
                cleaned = re.sub(pattern, replacement, cleaned, flags=re.IGNORECASE)
            # Remove extra spaces
            cleaned = re.sub(r'\s+', ' ', cleaned).strip()
            # Convert to sentence case: first letter uppercase, rest lowercase
            if cleaned:
                cleaned = cleaned[0].upper() + cleaned[1:].lower()
            return cleaned

        # Clean the address before further processing
        address = clean_address(address)

        listing_types = [
            ListingType.FOR_SALE,
            ListingType.FOR_RENT,
            ListingType.SOLD,
            ListingType.OFF_MARKET
        ]
        all_properties = []

        cache_key = RealtorDataService.key_factory(full_address, "market_data")
        if CACHING_ENABLED:
            cached_data = await redis_cache.get(cache_key)
            if cached_data:
                return cached_data

        try:
            # Calculate start date based on past_days
            end_date = datetime.now().strftime("%Y-%m-%d")
            start_date = (datetime.now() - timedelta(days=past_days)).strftime("%Y-%m-%d")

            for listing_type in listing_types:
                logger.info(f"Fetching {listing_type.value} listings")
                try:
                    search_results = await RealtorDataService.general_search(
                        start_date=start_date,
                        end_date=end_date,
                        listing_type=listing_type,
                        address=address,
                        postal_code=zipcode
                    )
                    properties_list = search_results.get("properties", [])
                    # Add listing type to each property record
                    for prop in properties_list:
                        if prop is not None:
                            prop['listing_type'] = listing_type.value
                    all_properties.extend([p for p in properties_list if p is not None])
                    if properties_list:
                        logger.info(f"Found {len(properties_list)} {listing_type.value} listings")
                    else:
                        logger.info(f"No {listing_type.value} listings found")
                except Exception as e:
                    logger.error(f"Error fetching {listing_type.value} listings: {str(e)}")
                    logger.error(traceback.format_exc())
                    continue
                time.sleep(1)

        except Exception as e:
            logger.error(f"Error in listing type loop: {str(e)}")
            logger.error(traceback.format_exc())
            return {
                'count': 0,
                'properties': []
            }

        # Group properties by address and unit, and flatten to list of dicts
        if all_properties:
            try:
                logger.info(f"Grouping {len(all_properties)} properties by address and unit")
                grouped_properties: List[Dict[str, Any]] = []

                # Use a tuple (address_key, unit) as a composite key for grouping
                group_map = {}

                for prop in all_properties:
                    try:
                        location_data = prop.get('location', {}) or {}
                        address_data = location_data.get('address', {}) or {}

                        address_line = address_data.get('line', '')
                        city = address_data.get('city', '')
                        state = address_data.get('state_code', '')
                        prop_postal_code = address_data.get('postal_code', '')

                        address_key = f"{address_line}, {city}, {state} {prop_postal_code}".strip(', ')
                        if not address_key:
                            continue

                        unit = address_data.get('unit', 'main')
                        if not unit:
                            unit = 'main'

                        group_id = (address_key, unit)
                        if group_id not in group_map:
                            group_map[group_id] = []
                        group_map[group_id].append(prop)
                    except Exception as e:
                        logger.error(f"Error processing property: {str(e)}")
                        logger.error(traceback.format_exc())
                        continue

                # Convert group_map to the requested list format
                for (address_key, unit), props in group_map.items():
                    grouped_properties.append({
                        "address": address_key,
                        "unit": unit,
                        "data": props
                    })

                payload = {
                    'timestamp': datetime.now().isoformat(),
                    'market_data': grouped_properties
                }

                if CACHING_ENABLED:
                    await redis_cache.set(cache_key, payload)
                    await redis_cache.expire(cache_key, REALTOR_CACHE_TTL)

                return payload
            except Exception as e:
                logger.error(f"Error grouping properties: {str(e)}")
                logger.error(traceback.format_exc())

        logger.info("No properties found or error occurred, returning empty result")
        return {
            'timestamp': datetime.now().isoformat(),
            'market_data': []
        }
