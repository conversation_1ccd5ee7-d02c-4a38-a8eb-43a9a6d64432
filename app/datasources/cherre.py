"""
Cherre API data service module.

This module provides functionality to interact with the Cherre GraphQL API
and retrieve property data based on addresses.
"""
import json
from datetime import datetime
from typing import Dict, Any, List

import urllib.error
import urllib.request

from app.utils.logger import get_logger
from app.utils.env_cache import get_env
from app.repository.cache import redis_cache

logger = get_logger(__name__)

# Cherre API Constants
CHERRE_GRAPHQL_URL = "https://graphql.cherre.com/graphql"
CHERRE_BEARER_TOKEN = get_env("CHERRE_BEARER_TOKEN")

# Cache settings
CACHING_ENABLED = True
CHERRE_CACHE_TTL = 604800  # 7 days in seconds

class CherreDataService:
    """
    Service for interacting with the Cherre GraphQL API.
    Provides methods to fetch property data with caching support.
    """

    @staticmethod
    def get_data_types() -> List[str]:
        """
        Get the list of available data types.
        """
        return ["property_data", "multifamily_data", "tax_history", "demographics"]
    
    @staticmethod
    def key_factory(full_address: str, data_type: str) -> str:
        """
        Generate a standardized cache key for a given address.
        
        Args:
            full_address: Full address string
            
        Returns:
            str: Formatted cache key
        """
        # Replace spaces with hyphens and remove any characters that could cause issues in Redis keys
        sanitized_address = full_address.replace(' ', '-')
        sanitized_address = ''.join(c for c in sanitized_address if c.isalnum() or c in '-_:')
        return f"prop_search:{sanitized_address}:{data_type}"
    
    @staticmethod
    async def post_cherre_request(payload: Dict[str, Any]) -> Dict[str, Any]:
        """
        Send a POST request to the Cherre GraphQL API.
        
        Args:
            payload: Dictionary containing the GraphQL query and variables
            
        Returns:
            Dict[str, Any]: Parsed JSON response
        """
        try:
            data = json.dumps(payload).encode("utf-8")
            headers = {
                "Authorization": f"Bearer {CHERRE_BEARER_TOKEN}",
                "Content-Type": "application/json"
            }
            logger.info(f"Sending request to Cherre API: {CHERRE_GRAPHQL_URL}")
            req = urllib.request.Request(CHERRE_GRAPHQL_URL, data=data, headers=headers, method="POST")
            with urllib.request.urlopen(req) as response:
                response_body = response.read().decode("utf-8")
            response_json = json.loads(response_body)
            
            # Log more details about the response
            if "errors" in response_json:
                logger.error(f"Cherre API returned errors: {response_json['errors']}")
            elif "data" in response_json and response_json["data"] and "address" in response_json["data"]:
                if response_json["data"]["address"] is None:
                    logger.warning("Cherre API returned null address - address may not exist in database")
                else:
                    logger.info("Cherre API returned data successfully")
            else:
                logger.warning(f"Unexpected Cherre API response structure: {response_json}")
            
            return response_json
        except urllib.error.HTTPError as e:
            logger.error(f"HTTP error in post_cherre_request: {e.code} - {e.reason}")
            # Read and log the error response if available
            if hasattr(e, 'read'):
                error_body = e.read().decode('utf-8')
                logger.error(f"Error response body: {error_body}")
            return {}
        except urllib.error.URLError as e:
            logger.error(f"URL error in post_cherre_request: {e.reason}")
            return {}
        except Exception as e:
            logger.error(f"Unexpected error in post_cherre_request: {str(e)}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            return {}

    @staticmethod
    async def fetch_multifamily_property_data(full_address: str) -> Dict[str, Any] | None:
        """
        Fetch multifamily property data from Cherre for a given full address with caching.
        
        Args:
            full_address: Full address string (e.g. "9 Avenue at Port Imperial, West New York, NJ 07093")
            
        Returns:
            Dict[str, Any]: Property data from Cherre including unit details, mortgage information and HOA fees
        """
        # Generate cache key based on the address
        cache_key = CherreDataService.key_factory(full_address, "multifamily_data")
        
        if CACHING_ENABLED:
            cached_data = await redis_cache.get(cache_key)
            if cached_data:
                return cached_data
        
        # If not in cache, fetch from Cherre API
        logger.info(f"Fetching Cherre data for address: {full_address}")
        
        query = (
            "query multifamily {"
            f'  address(address: "{full_address}") {{'
            "    cherre_address__address {"
            "      display_address"
            "      parcel_boundary_v2__address_point(where: {tax_assessor_id: {_is_null: false}}) {"
            "        tax_assessor_v2__tax_assessor_id {"
            "          address"
            "          tax_assessor_id"
            "          bed_count"
            "          bath_count"
            "          hoa_1_fee_value"
            "          recorder_v2__tax_assessor_id {"
            "            document_recorded_date"
            "            recorder_mortgage_v2__recorder_id {"
            "              amount"
            "            }"
            "          }"
            "        }"
            "      }"
            "    }"
            "  }"
            "}"
        )
        
        payload = {"query": query, "variables": {}}
        response = await CherreDataService.post_cherre_request(payload)
        
        # Process the response to extract unit data
        units = []
        if (response and "data" in response and response["data"] and 
            "address" in response["data"] and response["data"]["address"] and 
            "cherre_address__address" in response["data"]["address"]):
            
            address_data = response["data"]["address"]["cherre_address__address"]
            if "parcel_boundary_v2__address_point" in address_data:
                for parcel in address_data["parcel_boundary_v2__address_point"]:
                    if "tax_assessor_v2__tax_assessor_id" in parcel:
                        tax_data = parcel["tax_assessor_v2__tax_assessor_id"]
                        
                        # Extract mortgage data if available
                        mortgage_amount = None
                        document_date = None
                        if "recorder_v2__tax_assessor_id" in tax_data:
                            recorder_data_list = tax_data["recorder_v2__tax_assessor_id"]
                            # Handle recorder_v2__tax_assessor_id as a list
                            if recorder_data_list and isinstance(recorder_data_list, list) and len(recorder_data_list) > 0:
                                # Get the first item in the list
                                recorder_data = recorder_data_list[0]
                                document_date = recorder_data.get("document_recorded_date")
                                
                                if "recorder_mortgage_v2__recorder_id" in recorder_data:
                                    mortgage_data = recorder_data["recorder_mortgage_v2__recorder_id"]
                                    if mortgage_data and isinstance(mortgage_data, list) and len(mortgage_data) > 0:
                                        mortgage_amount = mortgage_data[0].get("amount")
                        # Create unit data dictionary
                        unit = {
                            "address": tax_data.get("address"),
                            "tax_assessor_id": tax_data.get("tax_assessor_id"),
                            "bed_count": tax_data.get("bed_count"),
                            "bath_count": tax_data.get("bath_count"),
                            "hoa_fee": tax_data.get("hoa_1_fee_value"),
                            "document_recorded_date": document_date,
                            "mortgage_amount": mortgage_amount
                        }
                        units.append(unit)
        
        # Add timestamp and units to the response
        structured_data = {
            "timestamp": datetime.now().isoformat(),
            "units": units
        }

        if CACHING_ENABLED:
            await redis_cache.set(cache_key, structured_data)
            await redis_cache.expire(cache_key, CHERRE_CACHE_TTL)
        
        return structured_data
    
    @staticmethod
    async def fetch_property_data(full_address: str) -> Dict[Any, Any] | None:
        """
        Fetch detailed property data from Cherre for a given address with caching.
        
        Args:
            full_address: Full address string
            
        Returns:
            Dict[str, Any]: Property data from Cherre with structured address and property data
        """

        cache_key = CherreDataService.key_factory(full_address, "property_data")
        
        if CACHING_ENABLED:
            cached_data = await redis_cache.get(cache_key)
            if cached_data:
                return cached_data
        
        # If not in cache, fetch from Cherre API
        logger.info(f"Fetching Cherre property data for address: {full_address}")
        
        query = (
            "query PropertyDetails {"
            f'  address(address: "{full_address}") {{'
            "    cherre_address__address {"
            "      display_address"
            "      street_address"
            "      city"
            "      state"
            "      zip"
            "      tax_assessor_v2__property_address {"
            "        bed_count"
            "        bath_count"
            "        gross_sq_ft"
            "        lot_size_acre"
            "        year_built"
            "        property_use_code_mapped"
            "        hvacc_cooling_code"
            "        hvacc_heating_code"
            "        parking_garage_code"
            "        parking_space_count"
            "        basement_sq_ft"
            "        basement_finished_sq_ft"
            "        basement_unfinished_sq_ft"
            "        flooring_material_code"
            "        has_laundry_room"
            "        fireplace_count"
            "        structure_style_code"
            "        roof_material_code"
            "        driveway_material_code"
            "        construction_code"
            "        roof_construction_code"
            "        exterior_code"
            "        sewer_usage_code"
            "        water_source_code"
            "        last_sale_date"
            "        last_sale_amount"
            "        longitude"
            "        latitude"
            "      }"
            "    }"
            "  }"
            "}"
        )
        
        payload = {"query": query, "variables": {}}
        response = await CherreDataService.post_cherre_request(payload)
        
        # Parse and structure the response
        structured_data = {"timestamp": datetime.now().isoformat()}
        
        if response and "data" in response and "address" in response["data"]:
            address_data = response["data"]["address"]["cherre_address__address"]
            property_data = address_data.get("tax_assessor_v2__property_address", {})[0]
        
            # Extract address information
            structured_data["address"] = {
                "display_address": address_data.get("display_address"),
                "street_address": address_data.get("street_address"),
                "city": address_data.get("city"),
                "state": address_data.get("state"),
                "zip": address_data.get("zip"),
                "latitude": property_data.get("latitude"),
                "longitude": property_data.get("longitude")
            }
            
            # Extract property data
            structured_data["data"] = {
                "bed_count": property_data.get("bed_count"),
                "bath_count": property_data.get("bath_count"),
                "gross_sq_ft": property_data.get("gross_sq_ft"),
                "lot_size_acre": property_data.get("lot_size_acre"),
                "year_built": property_data.get("year_built"),
                "property_use_code_mapped": property_data.get("property_use_code_mapped"),
                "hvacc_cooling_code": property_data.get("hvacc_cooling_code"),
                "hvacc_heating_code": property_data.get("hvacc_heating_code"),
                "parking_garage_code": property_data.get("parking_garage_code"),
                "parking_space_count": property_data.get("parking_space_count"),
                "basement_sq_ft": property_data.get("basement_sq_ft"),
                "basement_finished_sq_ft": property_data.get("basement_finished_sq_ft"),
                "basement_unfinished_sq_ft": property_data.get("basement_unfinished_sq_ft"),
                "flooring_material_code": property_data.get("flooring_material_code"),
                "has_laundry_room": property_data.get("has_laundry_room"),
                "fireplace_count": property_data.get("fireplace_count"),
                "structure_style_code": property_data.get("structure_style_code"),
                "roof_material_code": property_data.get("roof_material_code"),
                "driveway_material_code": property_data.get("driveway_material_code"),
                "construction_code": property_data.get("construction_code"),
                "roof_construction_code": property_data.get("roof_construction_code"),
                "exterior_code": property_data.get("exterior_code"),
                "sewer_usage_code": property_data.get("sewer_usage_code"),
                "water_source_code": property_data.get("water_source_code"),
                "last_sale_date": property_data.get("last_sale_date"),
                "last_sale_amount": property_data.get("last_sale_amount")
            }
        else:
            structured_data["address"] = ""
            structured_data["data"] = ""
        
        if CACHING_ENABLED:
            await redis_cache.set(cache_key, structured_data)
            await redis_cache.expire(cache_key, CHERRE_CACHE_TTL)
        
        return structured_data
    
    @staticmethod
    async def fetch_tax_history(full_address: str) -> Dict[str, Any] | None:
        """
        Fetch tax history data from Cherre for a given address with caching.
        
        Args:
            full_address: Full address string
            
        Returns:
            Dict[str, Any]: Tax history data from Cherre as a list of tax records
        """
        # Generate cache key based on the address
        cache_key = CherreDataService.key_factory(full_address, "tax_history")
        
        if CACHING_ENABLED:
            cached_data = await redis_cache.get(cache_key)
            if cached_data:
                return cached_data
        
        # If not in cache, fetch from Cherre API
        logger.info(f"Fetching Cherre tax history for address: {full_address}")
        
        query = (
            "query TaxHistory {"
            f'  address(address: "{full_address}") {{'
            "    cherre_address__address {"
            "      tax_assessor_v2__property_address {"
            "        tax_assessor_history_v2__tax_assessor_id {"
            "          assessed_tax_year"
            "          assessed_value_land"
            "          assessed_value_total"
            "          tax_bill_amount"
            "        }"
            "      }"
            "    }"
            "  }"
            "}"
        )
        
        payload = {"query": query, "variables": {}}
        response = await CherreDataService.post_cherre_request(payload)
        
        # Extract and structure the tax history records as a simple list
        tax_records = []
        if (response and "data" in response and response["data"] and 
            "address" in response["data"] and response["data"]["address"] and 
            "cherre_address__address" in response["data"]["address"]):
            
            address_data = response["data"]["address"]["cherre_address__address"]
            if "tax_assessor_v2__property_address" in address_data and address_data["tax_assessor_v2__property_address"]:
                for property_data in address_data["tax_assessor_v2__property_address"]:
                    if "tax_assessor_history_v2__tax_assessor_id" in property_data:
                        tax_records.extend(property_data["tax_assessor_history_v2__tax_assessor_id"])
        
        # Add timestamp to the response
        result = {
            "timestamp": datetime.now().isoformat(),
            "tax_records": tax_records
        }
        
        if CACHING_ENABLED:
            await redis_cache.set(cache_key, result)
            await redis_cache.expire(cache_key, CHERRE_CACHE_TTL)
        
        return result
    
    @staticmethod
    async def fetch_demographics(zipcode: str) -> Dict[str, Any]:
        """
        Fetch demographic data from Cherre for a given zipcode with caching.
        
        Args:
            zipcode: Zipcode string to fetch demographics for
            
        Returns:
            Dict[str, Any]: Demographic data from Cherre
        """
        # Generate cache key based on the zipcode
        cache_key = CherreDataService.key_factory(zipcode, "demographics")
        
        if CACHING_ENABLED:
            cached_data = await redis_cache.get(cache_key)
            if cached_data:
                return cached_data
        
        # If not in cache, fetch from Cherre API
        logger.info(f"Fetching Cherre demographics for zipcode: {zipcode}")
        
        query = (
            f"""query GetDemographicsByZipcode {{
                usa_zip_code_boundary_v2(where: {{zip_code: {{_eq: "{zipcode}"}}}}) {{
                    zip_code
                    usa_demographics_v2__geography_id(order_by:{{vintage:desc}}limit:1) {{
                        vintage
                        population_2020_count
                        population_median_age
                        median_household_income
                        average_household_income
                        crime_total_risk
                    }}
                }}
            }}"""
        )
        
        payload = {"query": query, "variables": {}}
        response = await CherreDataService.post_cherre_request(payload)
        
        # Extract demographic data
        demographic_data = {}
        if (response and "data" in response and 
            "usa_zip_code_boundary_v2" in response["data"] and 
            response["data"]["usa_zip_code_boundary_v2"] and 
            len(response["data"]["usa_zip_code_boundary_v2"]) > 0):
            
            zip_data = response["data"]["usa_zip_code_boundary_v2"][0]
            if ("usa_demographics_v2__geography_id" in zip_data and 
                zip_data["usa_demographics_v2__geography_id"] and 
                len(zip_data["usa_demographics_v2__geography_id"]) > 0):
                
                demographic_data = zip_data["usa_demographics_v2__geography_id"][0]
        
        # Structure the result
        result = {
            "timestamp": datetime.now().isoformat(),
            "demographics": {
                "vintage": demographic_data.get("vintage"),
                "population_2020_count": demographic_data.get("population_2020_count"),
                "population_median_age": demographic_data.get("population_median_age"),
                "median_household_income": demographic_data.get("median_household_income"),
                "average_household_income": demographic_data.get("average_household_income"),
                "crime_total_risk": demographic_data.get("crime_total_risk")
            }
        }
        
        if CACHING_ENABLED:
            await redis_cache.set(cache_key, result)
            await redis_cache.expire(cache_key, CHERRE_CACHE_TTL)
        
        return result
