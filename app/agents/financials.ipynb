{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2025-05-25 22:32:42 | INFO | Connecting to Redis at redis-17014.fcrce190.us-east-1-1.ec2.redns.redis-cloud.com:17014\n", "2025-05-25 22:32:42 | INFO | Initialized Redis cache\n"]}], "source": ["\"\"\"\n", "Financial Service - Main agent for financial analysis tasks.\n", "\n", "This service acts as the primary orchestrator for financial analysis tasks,\n", "delegating work to specialized sub-agents.\n", "\"\"\"\n", "\n", "# !pip install scrapingbee\n", "# !pip install fredapi\n", "\n", "import asyncio, os, sys, math\n", "from typing import Dict, Any, List, Optional\n", "from dataclasses import dataclass, field\n", "\n", "utility_path = os.path.abspath(os.path.join(os.getcwd(), '../..'))\n", "if utility_path not in sys.path:\n", "    sys.path.append(utility_path)\n", "\n", "from services.financials_service import PropertFinancialService\n", "from services.rental_income_service import RentalIncomeService\n", "from services.property_service import PropertyService\n", "from agents.financials import FinancialServiceAgent, FinancialTaskInput, FinancialResponse\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["prop_id = \"cf1857af-bccc-4547-aad3-047cf7c526e4\"\n", "portfolio_id = \"90db2602-2ed0-4f45-9de7-145f4052409c\"\n", "prop_fin = PropertFinancialService()\n", "prop_fin_df = await prop_fin.get_property_financials_dataframe(prop_id=prop_id)\n", "\n", "financial_service = FinancialServiceAgent()"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>1</th>\n", "      <th>2</th>\n", "      <th>3</th>\n", "      <th>4</th>\n", "      <th>5</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>id</th>\n", "      <td>54d93942-caf6-4132-82a0-762d294039d5</td>\n", "      <td>fa4537fb-2d55-4945-80f3-e90ddb424955</td>\n", "      <td>1a29cbd9-b9a1-4a94-bfcb-868fe2046097</td>\n", "      <td>3dfa39f0-300c-4784-a389-19e600654952</td>\n", "      <td>e91542e6-6678-485f-8b17-c6d26336704c</td>\n", "    </tr>\n", "    <tr>\n", "      <th>prop_id</th>\n", "      <td>cf1857af-bccc-4547-aad3-047cf7c526e4</td>\n", "      <td>cf1857af-bccc-4547-aad3-047cf7c526e4</td>\n", "      <td>cf1857af-bccc-4547-aad3-047cf7c526e4</td>\n", "      <td>cf1857af-bccc-4547-aad3-047cf7c526e4</td>\n", "      <td>cf1857af-bccc-4547-aad3-047cf7c526e4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>year</th>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td>3</td>\n", "      <td>4</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>rental_income</th>\n", "      <td>61140.0</td>\n", "      <td>50000.0</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>long_term_rental</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>short_term_rental</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>other_income</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>vacancy_loss</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>credit_loss</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>effective_gross_income</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>property_tax</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>insurance</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>repairs</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>maintenance</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>professional_fees</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>management_fees</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>leasing_fees</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>legal_fees</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>accounting_fees</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>engineering_fees</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>marketing_fees</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>consulting_fees</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>utilities</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>services</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>reserves</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>total_operating_expenses</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>net_operating_income</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>annual_debt_service</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>dscr</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>cash_flow_before_taxes</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>cash_flow_after_taxes</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>cumulative_cash_flow</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>cap_rate</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>gross_rent_multiplier</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>equity_multiple</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>cash_on_cash_return</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>total_acquisition_cost</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>aggregated_noi</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>blended_cap_rate</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>portfolio_irr</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>created_at</th>\n", "      <td>2025-05-24T17:42:43.568000</td>\n", "      <td>2025-05-24T17:42:43.568000</td>\n", "      <td>2025-05-24T17:42:43.568000</td>\n", "      <td>2025-05-24T17:42:43.568000</td>\n", "      <td>2025-05-24T17:42:43.568000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>updated_at</th>\n", "      <td>2025-05-24T17:42:43.568000</td>\n", "      <td>2025-05-24T17:42:43.568000</td>\n", "      <td>2025-05-24T17:42:43.568000</td>\n", "      <td>2025-05-24T17:42:43.568000</td>\n", "      <td>2025-05-24T17:42:43.568000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>metadata</th>\n", "      <td>{'year': 5, 'repairs': [{'sources': [{'data': ...</td>\n", "      <td>{}</td>\n", "      <td>{}</td>\n", "      <td>{}</td>\n", "      <td>{}</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                                                          1  \\\n", "id                                     54d93942-caf6-4132-82a0-762d294039d5   \n", "prop_id                                cf1857af-bccc-4547-aad3-047cf7c526e4   \n", "year                                                                      1   \n", "rental_income                                                       61140.0   \n", "long_term_rental                                                              \n", "short_term_rental                                                             \n", "other_income                                                                  \n", "vacancy_loss                                                                  \n", "credit_loss                                                                   \n", "effective_gross_income                                                        \n", "property_tax                                                                  \n", "insurance                                                                     \n", "repairs                                                                       \n", "maintenance                                                                   \n", "professional_fees                                                             \n", "management_fees                                                               \n", "leasing_fees                                                                  \n", "legal_fees                                                                    \n", "accounting_fees                                                               \n", "engineering_fees                                                              \n", "marketing_fees                                                                \n", "consulting_fees                                                               \n", "utilities                                                                     \n", "services                                                                      \n", "reserves                                                                      \n", "total_operating_expenses                                                      \n", "net_operating_income                                                          \n", "annual_debt_service                                                           \n", "dscr                                                                          \n", "cash_flow_before_taxes                                                        \n", "cash_flow_after_taxes                                                         \n", "cumulative_cash_flow                                                          \n", "cap_rate                                                                      \n", "gross_rent_multiplier                                                         \n", "equity_multiple                                                               \n", "cash_on_cash_return                                                           \n", "total_acquisition_cost                                                        \n", "aggregated_noi                                                                \n", "blended_cap_rate                                                              \n", "portfolio_irr                                                                 \n", "created_at                                       2025-05-24T17:42:43.568000   \n", "updated_at                                       2025-05-24T17:42:43.568000   \n", "metadata                  {'year': 5, 'repairs': [{'sources': [{'data': ...   \n", "\n", "                                                             2  \\\n", "id                        fa4537fb-2d55-4945-80f3-e90ddb424955   \n", "prop_id                   cf1857af-bccc-4547-aad3-047cf7c526e4   \n", "year                                                         2   \n", "rental_income                                          50000.0   \n", "long_term_rental                                                 \n", "short_term_rental                                                \n", "other_income                                                     \n", "vacancy_loss                                                     \n", "credit_loss                                                      \n", "effective_gross_income                                           \n", "property_tax                                                     \n", "insurance                                                        \n", "repairs                                                          \n", "maintenance                                                      \n", "professional_fees                                                \n", "management_fees                                                  \n", "leasing_fees                                                     \n", "legal_fees                                                       \n", "accounting_fees                                                  \n", "engineering_fees                                                 \n", "marketing_fees                                                   \n", "consulting_fees                                                  \n", "utilities                                                        \n", "services                                                         \n", "reserves                                                         \n", "total_operating_expenses                                         \n", "net_operating_income                                             \n", "annual_debt_service                                              \n", "dscr                                                             \n", "cash_flow_before_taxes                                           \n", "cash_flow_after_taxes                                            \n", "cumulative_cash_flow                                             \n", "cap_rate                                                         \n", "gross_rent_multiplier                                            \n", "equity_multiple                                                  \n", "cash_on_cash_return                                              \n", "total_acquisition_cost                                           \n", "aggregated_noi                                                   \n", "blended_cap_rate                                                 \n", "portfolio_irr                                                    \n", "created_at                          2025-05-24T17:42:43.568000   \n", "updated_at                          2025-05-24T17:42:43.568000   \n", "metadata                                                    {}   \n", "\n", "                                                             3  \\\n", "id                        1a29cbd9-b9a1-4a94-bfcb-868fe2046097   \n", "prop_id                   cf1857af-bccc-4547-aad3-047cf7c526e4   \n", "year                                                         3   \n", "rental_income                                                    \n", "long_term_rental                                                 \n", "short_term_rental                                                \n", "other_income                                                     \n", "vacancy_loss                                                     \n", "credit_loss                                                      \n", "effective_gross_income                                           \n", "property_tax                                                     \n", "insurance                                                        \n", "repairs                                                          \n", "maintenance                                                      \n", "professional_fees                                                \n", "management_fees                                                  \n", "leasing_fees                                                     \n", "legal_fees                                                       \n", "accounting_fees                                                  \n", "engineering_fees                                                 \n", "marketing_fees                                                   \n", "consulting_fees                                                  \n", "utilities                                                        \n", "services                                                         \n", "reserves                                                         \n", "total_operating_expenses                                         \n", "net_operating_income                                             \n", "annual_debt_service                                              \n", "dscr                                                             \n", "cash_flow_before_taxes                                           \n", "cash_flow_after_taxes                                            \n", "cumulative_cash_flow                                             \n", "cap_rate                                                         \n", "gross_rent_multiplier                                            \n", "equity_multiple                                                  \n", "cash_on_cash_return                                              \n", "total_acquisition_cost                                           \n", "aggregated_noi                                                   \n", "blended_cap_rate                                                 \n", "portfolio_irr                                                    \n", "created_at                          2025-05-24T17:42:43.568000   \n", "updated_at                          2025-05-24T17:42:43.568000   \n", "metadata                                                    {}   \n", "\n", "                                                             4  \\\n", "id                        3dfa39f0-300c-4784-a389-19e600654952   \n", "prop_id                   cf1857af-bccc-4547-aad3-047cf7c526e4   \n", "year                                                         4   \n", "rental_income                                                    \n", "long_term_rental                                                 \n", "short_term_rental                                                \n", "other_income                                                     \n", "vacancy_loss                                                     \n", "credit_loss                                                      \n", "effective_gross_income                                           \n", "property_tax                                                     \n", "insurance                                                        \n", "repairs                                                          \n", "maintenance                                                      \n", "professional_fees                                                \n", "management_fees                                                  \n", "leasing_fees                                                     \n", "legal_fees                                                       \n", "accounting_fees                                                  \n", "engineering_fees                                                 \n", "marketing_fees                                                   \n", "consulting_fees                                                  \n", "utilities                                                        \n", "services                                                         \n", "reserves                                                         \n", "total_operating_expenses                                         \n", "net_operating_income                                             \n", "annual_debt_service                                              \n", "dscr                                                             \n", "cash_flow_before_taxes                                           \n", "cash_flow_after_taxes                                            \n", "cumulative_cash_flow                                             \n", "cap_rate                                                         \n", "gross_rent_multiplier                                            \n", "equity_multiple                                                  \n", "cash_on_cash_return                                              \n", "total_acquisition_cost                                           \n", "aggregated_noi                                                   \n", "blended_cap_rate                                                 \n", "portfolio_irr                                                    \n", "created_at                          2025-05-24T17:42:43.568000   \n", "updated_at                          2025-05-24T17:42:43.568000   \n", "metadata                                                    {}   \n", "\n", "                                                             5  \n", "id                        e91542e6-6678-485f-8b17-c6d26336704c  \n", "prop_id                   cf1857af-bccc-4547-aad3-047cf7c526e4  \n", "year                                                         5  \n", "rental_income                                                   \n", "long_term_rental                                                \n", "short_term_rental                                               \n", "other_income                                                    \n", "vacancy_loss                                                    \n", "credit_loss                                                     \n", "effective_gross_income                                          \n", "property_tax                                                    \n", "insurance                                                       \n", "repairs                                                         \n", "maintenance                                                     \n", "professional_fees                                               \n", "management_fees                                                 \n", "leasing_fees                                                    \n", "legal_fees                                                      \n", "accounting_fees                                                 \n", "engineering_fees                                                \n", "marketing_fees                                                  \n", "consulting_fees                                                 \n", "utilities                                                       \n", "services                                                        \n", "reserves                                                        \n", "total_operating_expenses                                        \n", "net_operating_income                                            \n", "annual_debt_service                                             \n", "dscr                                                            \n", "cash_flow_before_taxes                                          \n", "cash_flow_after_taxes                                           \n", "cumulative_cash_flow                                            \n", "cap_rate                                                        \n", "gross_rent_multiplier                                           \n", "equity_multiple                                                 \n", "cash_on_cash_return                                             \n", "total_acquisition_cost                                          \n", "aggregated_noi                                                  \n", "blended_cap_rate                                                \n", "portfolio_irr                                                   \n", "created_at                          2025-05-24T17:42:43.568000  \n", "updated_at                          2025-05-24T17:42:43.568000  \n", "metadata                                                    {}  "]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["prop_fin_df.head(50)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2025-05-25 22:32:43 | INFO | Retrieved current mortgage rate from FRED: 6.86%\n", "2025-05-25 22:32:43 | INFO | Year 1: Estimated acquisition cost: $940,615.38 (6.5% cap rate)\n", "2025-05-25 22:32:43 | INFO | Year 2: Calculated debt service: $59,229.59 (rate: 6.86%, LTV: 80%)\n", "2025-05-25 22:32:43 | WARNING | Year 2: DSCR (0.84) below typical lending requirement (1.25x)\n", "2025-05-25 22:32:43 | WARNING | Could not compute IRR - numpy-financial package may not be available: No module named 'numpy_financial'\n", "2025-05-25 22:32:43 | INFO | Financial computation completed for 5 years\n", "2025-05-25 22:32:43 | INFO | Year 1: NOI=$61,140.00, Expenses=$0.00\n", "2025-05-25 22:32:43 | INFO | Year 2: NOI=$50,000.00, Expenses=$0.00\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>1</th>\n", "      <th>2</th>\n", "      <th>3</th>\n", "      <th>4</th>\n", "      <th>5</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>id</th>\n", "      <td>54d93942-caf6-4132-82a0-762d294039d5</td>\n", "      <td>fa4537fb-2d55-4945-80f3-e90ddb424955</td>\n", "      <td>1a29cbd9-b9a1-4a94-bfcb-868fe2046097</td>\n", "      <td>3dfa39f0-300c-4784-a389-19e600654952</td>\n", "      <td>e91542e6-6678-485f-8b17-c6d26336704c</td>\n", "    </tr>\n", "    <tr>\n", "      <th>prop_id</th>\n", "      <td>cf1857af-bccc-4547-aad3-047cf7c526e4</td>\n", "      <td>cf1857af-bccc-4547-aad3-047cf7c526e4</td>\n", "      <td>cf1857af-bccc-4547-aad3-047cf7c526e4</td>\n", "      <td>cf1857af-bccc-4547-aad3-047cf7c526e4</td>\n", "      <td>cf1857af-bccc-4547-aad3-047cf7c526e4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>year</th>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td>3</td>\n", "      <td>4</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>rental_income</th>\n", "      <td>61140.0</td>\n", "      <td>50000.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>long_term_rental</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>short_term_rental</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>other_income</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>vacancy_loss</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>credit_loss</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>effective_gross_income</th>\n", "      <td>61140.0</td>\n", "      <td>50000.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>property_tax</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>insurance</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>repairs</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>maintenance</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>professional_fees</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>management_fees</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>leasing_fees</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>legal_fees</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>accounting_fees</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>engineering_fees</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>marketing_fees</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>consulting_fees</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>utilities</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>services</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>reserves</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>total_operating_expenses</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>net_operating_income</th>\n", "      <td>61140.0</td>\n", "      <td>50000.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>annual_debt_service</th>\n", "      <td></td>\n", "      <td>59229.59</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>dscr</th>\n", "      <td></td>\n", "      <td>0.8442</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>cash_flow_before_taxes</th>\n", "      <td>61140.0</td>\n", "      <td>-9229.59</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>cash_flow_after_taxes</th>\n", "      <td>61140.0</td>\n", "      <td>-9229.59</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>cumulative_cash_flow</th>\n", "      <td>61140.0</td>\n", "      <td>51910.41</td>\n", "      <td>51910.41</td>\n", "      <td>51910.41</td>\n", "      <td>51910.41</td>\n", "    </tr>\n", "    <tr>\n", "      <th>cap_rate</th>\n", "      <td>6.5</td>\n", "      <td>5.3157</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>gross_rent_multiplier</th>\n", "      <td>15.3846</td>\n", "      <td>18.8123</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>equity_multiple</th>\n", "      <td>1.065</td>\n", "      <td>1.0552</td>\n", "      <td>1.0552</td>\n", "      <td>1.0552</td>\n", "      <td>1.0552</td>\n", "    </tr>\n", "    <tr>\n", "      <th>cash_on_cash_return</th>\n", "      <td>6.5</td>\n", "      <td>-0.9812</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>total_acquisition_cost</th>\n", "      <td>940615.38</td>\n", "      <td>940615.384615</td>\n", "      <td>940615.384615</td>\n", "      <td>940615.384615</td>\n", "      <td>940615.384615</td>\n", "    </tr>\n", "    <tr>\n", "      <th>aggregated_noi</th>\n", "      <td>61140.0</td>\n", "      <td>111140.0</td>\n", "      <td>111140.0</td>\n", "      <td>111140.0</td>\n", "      <td>111140.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>blended_cap_rate</th>\n", "      <td>6.5</td>\n", "      <td>11.8157</td>\n", "      <td>11.8157</td>\n", "      <td>11.8157</td>\n", "      <td>11.8157</td>\n", "    </tr>\n", "    <tr>\n", "      <th>portfolio_irr</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>created_at</th>\n", "      <td>2025-05-24T17:42:43.568000</td>\n", "      <td>2025-05-24T17:42:43.568000</td>\n", "      <td>2025-05-24T17:42:43.568000</td>\n", "      <td>2025-05-24T17:42:43.568000</td>\n", "      <td>2025-05-24T17:42:43.568000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>updated_at</th>\n", "      <td>2025-05-24T17:42:43.568000</td>\n", "      <td>2025-05-24T17:42:43.568000</td>\n", "      <td>2025-05-24T17:42:43.568000</td>\n", "      <td>2025-05-24T17:42:43.568000</td>\n", "      <td>2025-05-24T17:42:43.568000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>metadata</th>\n", "      <td>{'year': 5, 'repairs': [{'sources': [{'data': ...</td>\n", "      <td>{}</td>\n", "      <td>{}</td>\n", "      <td>{}</td>\n", "      <td>{}</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                                                          1  \\\n", "id                                     54d93942-caf6-4132-82a0-762d294039d5   \n", "prop_id                                cf1857af-bccc-4547-aad3-047cf7c526e4   \n", "year                                                                      1   \n", "rental_income                                                       61140.0   \n", "long_term_rental                                                              \n", "short_term_rental                                                             \n", "other_income                                                                  \n", "vacancy_loss                                                                  \n", "credit_loss                                                                   \n", "effective_gross_income                                              61140.0   \n", "property_tax                                                                  \n", "insurance                                                                     \n", "repairs                                                                       \n", "maintenance                                                                   \n", "professional_fees                                                       0.0   \n", "management_fees                                                               \n", "leasing_fees                                                                  \n", "legal_fees                                                                    \n", "accounting_fees                                                               \n", "engineering_fees                                                              \n", "marketing_fees                                                                \n", "consulting_fees                                                               \n", "utilities                                                                     \n", "services                                                                      \n", "reserves                                                                      \n", "total_operating_expenses                                                0.0   \n", "net_operating_income                                                61140.0   \n", "annual_debt_service                                                           \n", "dscr                                                                          \n", "cash_flow_before_taxes                                              61140.0   \n", "cash_flow_after_taxes                                               61140.0   \n", "cumulative_cash_flow                                                61140.0   \n", "cap_rate                                                                6.5   \n", "gross_rent_multiplier                                               15.3846   \n", "equity_multiple                                                       1.065   \n", "cash_on_cash_return                                                     6.5   \n", "total_acquisition_cost                                            940615.38   \n", "aggregated_noi                                                      61140.0   \n", "blended_cap_rate                                                        6.5   \n", "portfolio_irr                                                                 \n", "created_at                                       2025-05-24T17:42:43.568000   \n", "updated_at                                       2025-05-24T17:42:43.568000   \n", "metadata                  {'year': 5, 'repairs': [{'sources': [{'data': ...   \n", "\n", "                                                             2  \\\n", "id                        fa4537fb-2d55-4945-80f3-e90ddb424955   \n", "prop_id                   cf1857af-bccc-4547-aad3-047cf7c526e4   \n", "year                                                         2   \n", "rental_income                                          50000.0   \n", "long_term_rental                                                 \n", "short_term_rental                                                \n", "other_income                                                     \n", "vacancy_loss                                                     \n", "credit_loss                                                      \n", "effective_gross_income                                 50000.0   \n", "property_tax                                                     \n", "insurance                                                        \n", "repairs                                                          \n", "maintenance                                                      \n", "professional_fees                                          0.0   \n", "management_fees                                                  \n", "leasing_fees                                                     \n", "legal_fees                                                       \n", "accounting_fees                                                  \n", "engineering_fees                                                 \n", "marketing_fees                                                   \n", "consulting_fees                                                  \n", "utilities                                                        \n", "services                                                         \n", "reserves                                                         \n", "total_operating_expenses                                   0.0   \n", "net_operating_income                                   50000.0   \n", "annual_debt_service                                   59229.59   \n", "dscr                                                    0.8442   \n", "cash_flow_before_taxes                                -9229.59   \n", "cash_flow_after_taxes                                 -9229.59   \n", "cumulative_cash_flow                                  51910.41   \n", "cap_rate                                                5.3157   \n", "gross_rent_multiplier                                  18.8123   \n", "equity_multiple                                         1.0552   \n", "cash_on_cash_return                                    -0.9812   \n", "total_acquisition_cost                           940615.384615   \n", "aggregated_noi                                        111140.0   \n", "blended_cap_rate                                       11.8157   \n", "portfolio_irr                                                    \n", "created_at                          2025-05-24T17:42:43.568000   \n", "updated_at                          2025-05-24T17:42:43.568000   \n", "metadata                                                    {}   \n", "\n", "                                                             3  \\\n", "id                        1a29cbd9-b9a1-4a94-bfcb-868fe2046097   \n", "prop_id                   cf1857af-bccc-4547-aad3-047cf7c526e4   \n", "year                                                         3   \n", "rental_income                                              0.0   \n", "long_term_rental                                                 \n", "short_term_rental                                                \n", "other_income                                                     \n", "vacancy_loss                                                     \n", "credit_loss                                                      \n", "effective_gross_income                                     0.0   \n", "property_tax                                                     \n", "insurance                                                        \n", "repairs                                                          \n", "maintenance                                                      \n", "professional_fees                                          0.0   \n", "management_fees                                                  \n", "leasing_fees                                                     \n", "legal_fees                                                       \n", "accounting_fees                                                  \n", "engineering_fees                                                 \n", "marketing_fees                                                   \n", "consulting_fees                                                  \n", "utilities                                                        \n", "services                                                         \n", "reserves                                                         \n", "total_operating_expenses                                   0.0   \n", "net_operating_income                                       0.0   \n", "annual_debt_service                                              \n", "dscr                                                             \n", "cash_flow_before_taxes                                     0.0   \n", "cash_flow_after_taxes                                      0.0   \n", "cumulative_cash_flow                                  51910.41   \n", "cap_rate                                                   0.0   \n", "gross_rent_multiplier                                            \n", "equity_multiple                                         1.0552   \n", "cash_on_cash_return                                        0.0   \n", "total_acquisition_cost                           940615.384615   \n", "aggregated_noi                                        111140.0   \n", "blended_cap_rate                                       11.8157   \n", "portfolio_irr                                                    \n", "created_at                          2025-05-24T17:42:43.568000   \n", "updated_at                          2025-05-24T17:42:43.568000   \n", "metadata                                                    {}   \n", "\n", "                                                             4  \\\n", "id                        3dfa39f0-300c-4784-a389-19e600654952   \n", "prop_id                   cf1857af-bccc-4547-aad3-047cf7c526e4   \n", "year                                                         4   \n", "rental_income                                              0.0   \n", "long_term_rental                                                 \n", "short_term_rental                                                \n", "other_income                                                     \n", "vacancy_loss                                                     \n", "credit_loss                                                      \n", "effective_gross_income                                     0.0   \n", "property_tax                                                     \n", "insurance                                                        \n", "repairs                                                          \n", "maintenance                                                      \n", "professional_fees                                          0.0   \n", "management_fees                                                  \n", "leasing_fees                                                     \n", "legal_fees                                                       \n", "accounting_fees                                                  \n", "engineering_fees                                                 \n", "marketing_fees                                                   \n", "consulting_fees                                                  \n", "utilities                                                        \n", "services                                                         \n", "reserves                                                         \n", "total_operating_expenses                                   0.0   \n", "net_operating_income                                       0.0   \n", "annual_debt_service                                              \n", "dscr                                                             \n", "cash_flow_before_taxes                                     0.0   \n", "cash_flow_after_taxes                                      0.0   \n", "cumulative_cash_flow                                  51910.41   \n", "cap_rate                                                   0.0   \n", "gross_rent_multiplier                                            \n", "equity_multiple                                         1.0552   \n", "cash_on_cash_return                                        0.0   \n", "total_acquisition_cost                           940615.384615   \n", "aggregated_noi                                        111140.0   \n", "blended_cap_rate                                       11.8157   \n", "portfolio_irr                                                    \n", "created_at                          2025-05-24T17:42:43.568000   \n", "updated_at                          2025-05-24T17:42:43.568000   \n", "metadata                                                    {}   \n", "\n", "                                                             5  \n", "id                        e91542e6-6678-485f-8b17-c6d26336704c  \n", "prop_id                   cf1857af-bccc-4547-aad3-047cf7c526e4  \n", "year                                                         5  \n", "rental_income                                              0.0  \n", "long_term_rental                                                \n", "short_term_rental                                               \n", "other_income                                                    \n", "vacancy_loss                                                    \n", "credit_loss                                                     \n", "effective_gross_income                                     0.0  \n", "property_tax                                                    \n", "insurance                                                       \n", "repairs                                                         \n", "maintenance                                                     \n", "professional_fees                                          0.0  \n", "management_fees                                                 \n", "leasing_fees                                                    \n", "legal_fees                                                      \n", "accounting_fees                                                 \n", "engineering_fees                                                \n", "marketing_fees                                                  \n", "consulting_fees                                                 \n", "utilities                                                       \n", "services                                                        \n", "reserves                                                        \n", "total_operating_expenses                                   0.0  \n", "net_operating_income                                       0.0  \n", "annual_debt_service                                             \n", "dscr                                                            \n", "cash_flow_before_taxes                                     0.0  \n", "cash_flow_after_taxes                                      0.0  \n", "cumulative_cash_flow                                  51910.41  \n", "cap_rate                                                   0.0  \n", "gross_rent_multiplier                                           \n", "equity_multiple                                         1.0552  \n", "cash_on_cash_return                                        0.0  \n", "total_acquisition_cost                           940615.384615  \n", "aggregated_noi                                        111140.0  \n", "blended_cap_rate                                       11.8157  \n", "portfolio_irr                                                   \n", "created_at                          2025-05-24T17:42:43.568000  \n", "updated_at                          2025-05-24T17:42:43.568000  \n", "metadata                                                    {}  "]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["computed_df = await prop_fin.compute_property_financials(prop_fin_df, override_user_input=True)\n", "computed_df.head(50)\n"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2025-05-25 22:32:43 | INFO | FRED client initialized successfully\n", "2025-05-25 22:32:43 | INFO | Fetching FRED data for series: CUUR0000SEHA\n", "2025-05-25 22:32:43 | INFO | Retrieved 76 data points for series CUUR0000SEHA\n", "[{'date': '2019-01-01', 'value': 325.597}, {'date': '2019-02-01', 'value': 326.351}, {'date': '2019-03-01', 'value': 327.513}, {'date': '2019-04-01', 'value': 328.678}, {'date': '2019-05-01', 'value': 329.333}, {'date': '2019-06-01', 'value': 330.648}, {'date': '2019-07-01', 'value': 331.605}, {'date': '2019-08-01', 'value': 332.638}, {'date': '2019-09-01', 'value': 333.834}, {'date': '2019-10-01', 'value': 334.68}, {'date': '2019-11-01', 'value': 335.819}, {'date': '2019-12-01', 'value': 336.789}, {'date': '2020-01-01', 'value': 337.825}, {'date': '2020-02-01', 'value': 338.616}, {'date': '2020-03-01', 'value': 339.519}, {'date': '2020-04-01', 'value': 340.135}, {'date': '2020-05-01', 'value': 340.811}, {'date': '2020-06-01', 'value': 341.294}, {'date': '2020-07-01', 'value': 341.95}, {'date': '2020-08-01', 'value': 342.444}, {'date': '2020-09-01', 'value': 342.91}, {'date': '2020-10-01', 'value': 343.615}, {'date': '2020-11-01', 'value': 344.039}, {'date': '2020-12-01', 'value': 344.455}, {'date': '2021-01-01', 'value': 344.758}, {'date': '2021-02-01', 'value': 345.242}, {'date': '2021-03-01', 'value': 345.717}, {'date': '2021-04-01', 'value': 346.267}, {'date': '2021-05-01', 'value': 347.016}, {'date': '2021-06-01', 'value': 347.833}, {'date': '2021-07-01', 'value': 348.469}, {'date': '2021-08-01', 'value': 349.71}, {'date': '2021-09-01', 'value': 351.255}, {'date': '2021-10-01', 'value': 352.892}, {'date': '2021-11-01', 'value': 354.526}, {'date': '2021-12-01', 'value': 355.931}, {'date': '2022-01-01', 'value': 357.737}, {'date': '2022-02-01', 'value': 359.627}, {'date': '2022-03-01', 'value': 361.083}, {'date': '2022-04-01', 'value': 362.951}, {'date': '2022-05-01', 'value': 365.116}, {'date': '2022-06-01', 'value': 367.927}, {'date': '2022-07-01', 'value': 370.448}, {'date': '2022-08-01', 'value': 373.283}, {'date': '2022-09-01', 'value': 376.569}, {'date': '2022-10-01', 'value': 379.436}, {'date': '2022-11-01', 'value': 382.562}, {'date': '2022-12-01', 'value': 385.649}, {'date': '2023-01-01', 'value': 388.372}, {'date': '2023-02-01', 'value': 391.141}, {'date': '2023-03-01', 'value': 392.91}, {'date': '2023-04-01', 'value': 394.898}, {'date': '2023-05-01', 'value': 396.726}, {'date': '2023-06-01', 'value': 398.593}, {'date': '2023-07-01', 'value': 400.21}, {'date': '2023-08-01', 'value': 402.247}, {'date': '2023-09-01', 'value': 404.487}, {'date': '2023-10-01', 'value': 406.683}, {'date': '2023-11-01', 'value': 408.838}, {'date': '2023-12-01', 'value': 410.606}, {'date': '2024-01-01', 'value': 412.019}, {'date': '2024-02-01', 'value': 413.695}, {'date': '2024-03-01', 'value': 415.219}, {'date': '2024-04-01', 'value': 416.386}, {'date': '2024-05-01', 'value': 417.772}, {'date': '2024-06-01', 'value': 418.82}, {'date': '2024-07-01', 'value': 420.577}, {'date': '2024-08-01', 'value': 422.223}, {'date': '2024-09-01', 'value': 423.821}, {'date': '2024-10-01', 'value': 425.381}, {'date': '2024-11-01', 'value': 426.651}, {'date': '2024-12-01', 'value': 428.151}, {'date': '2025-01-01', 'value': 429.506}, {'date': '2025-02-01', 'value': 430.603}, {'date': '2025-03-01', 'value': 431.798}, {'date': '2025-04-01', 'value': 432.956}]\n"]}], "source": ["from app.datasources.fred import get_fred_rental_data\n", "\n", "fred_rental_data = get_fred_rental_data(\n", "                series_id=\"CUUR0000SEHA\",\n", "                start_date=\"2019-01-01\",\n", "                end_date=None  # Current date\n", "            )\n", "\n", "print(fred_rental_data)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2025-05-25 22:32:48 | INFO | Supabase client initialized and verified, count: 39\n", "2025-05-25 22:32:48 | INFO | Successfully initialized Supabase database connection\n", "2025-05-25 22:32:48 | INFO | Property cf1857af-bccc-4547-aad3-047cf7c526e4: 5 units with rent, 0 units missing rent\n", "2025-05-25 22:32:48 | WARNING | No units have sqft data - cannot calculate rent per sqft\n", "2025-05-25 22:32:48 | INFO | Sqft data not available, using median rent estimation\n", "2025-05-25 22:32:48 | INFO | Calculated median rent: $990.00 from 5 units\n", "2025-05-25 22:32:48 | INFO | Individual rents: ['$875.00', '$950.00', '$990.00', '$1030.00', '$1250.00']\n", "2025-05-25 22:32:48 | INFO | Known rent - Unit 3: $950.00\n", "2025-05-25 22:32:48 | INFO | Known rent - Unit 4: $1250.00\n", "2025-05-25 22:32:48 | INFO | Known rent - Unit 2: $1030.00\n", "2025-05-25 22:32:48 | INFO | Known rent - Unit 1: $875.00\n", "2025-05-25 22:32:48 | INFO | Known rent - Unit 5: $990.00\n", "2025-05-25 22:32:48 | INFO | Total estimated rental income for property cf1857af-bccc-4547-aad3-047cf7c526e4: $5095.00/month\n", "2025-05-25 22:32:48 | INFO | Calculating rental income projections for years 2-5\n", "2025-05-25 22:32:48 | INFO | Retrieved historical inflation data for projections\n", "2025-05-25 22:32:48 | INFO | FRED client initialized successfully\n", "2025-05-25 22:32:48 | INFO | Fetching FRED data for series: CUUR0000SEHA\n", "2025-05-25 22:32:48 | INFO | Retrieved 76 data points for series CUUR0000SEHA\n", "2025-05-25 22:32:48 | INFO | FRED-based rental growth rates calculated: {2: 0.015, 3: 0.014249999999999999, 4: 0.0135, 5: 0.01275}\n", "2025-05-25 22:32:48 | INFO | Based on 36 months of recent FRED rental price data\n", "2025-05-25 22:32:48 | INFO | Average historical growth rate: 0.49%\n", "2025-05-25 22:32:48 | INFO | Year 2: $62057.10 (growth: 1.5%)\n", "2025-05-25 22:32:48 | INFO | Year 3: $62941.41 (growth: 1.4%)\n", "2025-05-25 22:32:48 | INFO | Year 4: $63791.12 (growth: 1.4%)\n", "2025-05-25 22:32:48 | INFO | Year 5: $64604.46 (growth: 1.3%)\n", "Setting rental_income for year 1 to 61140.0\n", "Setting rental_income for year 2 to 62057.09999999999\n", "Setting rental_income for year 3 to 62941.413674999996\n", "Setting rental_income for year 4 to 63791.1227596125\n", "Setting rental_income for year 5 to 64604.45957479756\n", "2025-05-25 22:32:59 | INFO | Retrieved current mortgage rate from FRED: 6.86%\n", "2025-05-25 22:32:59 | INFO | Year 1: Calculated debt service: $59,229.59 (rate: 6.86%, LTV: 80%)\n", "2025-05-25 22:32:59 | WARNING | Year 1: DSCR (1.03) below typical lending requirement (1.25x)\n", "2025-05-25 22:32:59 | WARNING | Year 2: DSCR (1.05) below typical lending requirement (1.25x)\n", "2025-05-25 22:32:59 | INFO | Year 3: Calculated debt service: $59,229.59 (rate: 6.86%, LTV: 80%)\n", "2025-05-25 22:32:59 | WARNING | Year 3: DSCR (1.06) below typical lending requirement (1.25x)\n", "2025-05-25 22:32:59 | INFO | Year 4: Calculated debt service: $59,229.59 (rate: 6.86%, LTV: 80%)\n", "2025-05-25 22:32:59 | WARNING | Year 4: DSCR (1.08) below typical lending requirement (1.25x)\n", "2025-05-25 22:32:59 | INFO | Year 5: Calculated debt service: $59,229.59 (rate: 6.86%, LTV: 80%)\n", "2025-05-25 22:32:59 | WARNING | Year 5: DSCR (1.09) below typical lending requirement (1.25x)\n", "2025-05-25 22:32:59 | WARNING | Could not compute IRR - numpy-financial package may not be available: No module named 'numpy_financial'\n", "2025-05-25 22:32:59 | INFO | Financial computation completed for 5 years\n", "2025-05-25 22:32:59 | INFO | Year 1: NOI=$61,140.00, Expenses=$0.00\n", "2025-05-25 22:32:59 | INFO | Year 2: NOI=$62,057.00, Expenses=$0.00\n", "2025-05-25 22:32:59 | INFO | Year 3: NOI=$62,941.00, Expenses=$0.00\n", "2025-05-25 22:32:59 | INFO | Year 4: NOI=$63,791.00, Expenses=$0.00\n", "2025-05-25 22:32:59 | INFO | Year 5: NOI=$64,604.00, Expenses=$0.00\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>1</th>\n", "      <th>2</th>\n", "      <th>3</th>\n", "      <th>4</th>\n", "      <th>5</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>id</th>\n", "      <td>54d93942-caf6-4132-82a0-762d294039d5</td>\n", "      <td>fa4537fb-2d55-4945-80f3-e90ddb424955</td>\n", "      <td>1a29cbd9-b9a1-4a94-bfcb-868fe2046097</td>\n", "      <td>3dfa39f0-300c-4784-a389-19e600654952</td>\n", "      <td>e91542e6-6678-485f-8b17-c6d26336704c</td>\n", "    </tr>\n", "    <tr>\n", "      <th>prop_id</th>\n", "      <td>cf1857af-bccc-4547-aad3-047cf7c526e4</td>\n", "      <td>cf1857af-bccc-4547-aad3-047cf7c526e4</td>\n", "      <td>cf1857af-bccc-4547-aad3-047cf7c526e4</td>\n", "      <td>cf1857af-bccc-4547-aad3-047cf7c526e4</td>\n", "      <td>cf1857af-bccc-4547-aad3-047cf7c526e4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>year</th>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td>3</td>\n", "      <td>4</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>rental_income</th>\n", "      <td>61140.0</td>\n", "      <td>62057.0</td>\n", "      <td>62941.0</td>\n", "      <td>63791.0</td>\n", "      <td>64604.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>long_term_rental</th>\n", "      <td>61140</td>\n", "      <td>62057</td>\n", "      <td>62941</td>\n", "      <td>63791</td>\n", "      <td>64604</td>\n", "    </tr>\n", "    <tr>\n", "      <th>short_term_rental</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>other_income</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>vacancy_loss</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>credit_loss</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>effective_gross_income</th>\n", "      <td>61140.0</td>\n", "      <td>62057.0</td>\n", "      <td>62941.0</td>\n", "      <td>63791.0</td>\n", "      <td>64604.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>property_tax</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>insurance</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>repairs</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>maintenance</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>professional_fees</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>management_fees</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>leasing_fees</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>legal_fees</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>accounting_fees</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>engineering_fees</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>marketing_fees</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>consulting_fees</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>utilities</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>services</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>reserves</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>total_operating_expenses</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>net_operating_income</th>\n", "      <td>61140.0</td>\n", "      <td>62057.0</td>\n", "      <td>62941.0</td>\n", "      <td>63791.0</td>\n", "      <td>64604.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>annual_debt_service</th>\n", "      <td>59229.59</td>\n", "      <td>59229.59</td>\n", "      <td>59229.59</td>\n", "      <td>59229.59</td>\n", "      <td>59229.59</td>\n", "    </tr>\n", "    <tr>\n", "      <th>dscr</th>\n", "      <td>1.0323</td>\n", "      <td>1.0477</td>\n", "      <td>1.0627</td>\n", "      <td>1.077</td>\n", "      <td>1.0907</td>\n", "    </tr>\n", "    <tr>\n", "      <th>cash_flow_before_taxes</th>\n", "      <td>1910.41</td>\n", "      <td>2827.41</td>\n", "      <td>3711.41</td>\n", "      <td>4561.41</td>\n", "      <td>5374.41</td>\n", "    </tr>\n", "    <tr>\n", "      <th>cash_flow_after_taxes</th>\n", "      <td>1910.41</td>\n", "      <td>2827.41</td>\n", "      <td>3711.41</td>\n", "      <td>4561.41</td>\n", "      <td>5374.41</td>\n", "    </tr>\n", "    <tr>\n", "      <th>cumulative_cash_flow</th>\n", "      <td>1910.41</td>\n", "      <td>4737.82</td>\n", "      <td>8449.23</td>\n", "      <td>13010.64</td>\n", "      <td>18385.05</td>\n", "    </tr>\n", "    <tr>\n", "      <th>cap_rate</th>\n", "      <td>6.5</td>\n", "      <td>6.5975</td>\n", "      <td>6.6915</td>\n", "      <td>6.7818</td>\n", "      <td>6.8683</td>\n", "    </tr>\n", "    <tr>\n", "      <th>gross_rent_multiplier</th>\n", "      <td>15.3846</td>\n", "      <td>15.1573</td>\n", "      <td>14.9444</td>\n", "      <td>14.7453</td>\n", "      <td>14.5597</td>\n", "    </tr>\n", "    <tr>\n", "      <th>equity_multiple</th>\n", "      <td>1.002</td>\n", "      <td>1.005</td>\n", "      <td>1.009</td>\n", "      <td>1.0138</td>\n", "      <td>1.0195</td>\n", "    </tr>\n", "    <tr>\n", "      <th>cash_on_cash_return</th>\n", "      <td>0.2031</td>\n", "      <td>0.3006</td>\n", "      <td>0.3946</td>\n", "      <td>0.4849</td>\n", "      <td>0.5714</td>\n", "    </tr>\n", "    <tr>\n", "      <th>total_acquisition_cost</th>\n", "      <td>940615.38</td>\n", "      <td>940615.38</td>\n", "      <td>940615.38</td>\n", "      <td>940615.38</td>\n", "      <td>940615.38</td>\n", "    </tr>\n", "    <tr>\n", "      <th>aggregated_noi</th>\n", "      <td>61140.0</td>\n", "      <td>123197.0</td>\n", "      <td>186138.0</td>\n", "      <td>249929.0</td>\n", "      <td>314533.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>blended_cap_rate</th>\n", "      <td>6.5</td>\n", "      <td>13.0975</td>\n", "      <td>19.789</td>\n", "      <td>26.5708</td>\n", "      <td>33.4391</td>\n", "    </tr>\n", "    <tr>\n", "      <th>portfolio_irr</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>created_at</th>\n", "      <td>2025-05-24T17:42:43.568000</td>\n", "      <td>2025-05-24T17:42:43.568000</td>\n", "      <td>2025-05-24T17:42:43.568000</td>\n", "      <td>2025-05-24T17:42:43.568000</td>\n", "      <td>2025-05-24T17:42:43.568000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>updated_at</th>\n", "      <td>2025-05-24T17:42:43.568000</td>\n", "      <td>2025-05-24T17:42:43.568000</td>\n", "      <td>2025-05-24T17:42:43.568000</td>\n", "      <td>2025-05-24T17:42:43.568000</td>\n", "      <td>2025-05-24T17:42:43.568000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>metadata</th>\n", "      <td>{'year': 5, 'repairs': [{'sources': [{'data': ...</td>\n", "      <td>{}</td>\n", "      <td>{}</td>\n", "      <td>{}</td>\n", "      <td>{}</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                                                          1  \\\n", "id                                     54d93942-caf6-4132-82a0-762d294039d5   \n", "prop_id                                cf1857af-bccc-4547-aad3-047cf7c526e4   \n", "year                                                                      1   \n", "rental_income                                                       61140.0   \n", "long_term_rental                                                      61140   \n", "short_term_rental                                                             \n", "other_income                                                                  \n", "vacancy_loss                                                                  \n", "credit_loss                                                                   \n", "effective_gross_income                                              61140.0   \n", "property_tax                                                                  \n", "insurance                                                                     \n", "repairs                                                                       \n", "maintenance                                                                   \n", "professional_fees                                                       0.0   \n", "management_fees                                                               \n", "leasing_fees                                                                  \n", "legal_fees                                                                    \n", "accounting_fees                                                               \n", "engineering_fees                                                              \n", "marketing_fees                                                                \n", "consulting_fees                                                               \n", "utilities                                                                     \n", "services                                                                      \n", "reserves                                                                      \n", "total_operating_expenses                                                0.0   \n", "net_operating_income                                                61140.0   \n", "annual_debt_service                                                59229.59   \n", "dscr                                                                 1.0323   \n", "cash_flow_before_taxes                                              1910.41   \n", "cash_flow_after_taxes                                               1910.41   \n", "cumulative_cash_flow                                                1910.41   \n", "cap_rate                                                                6.5   \n", "gross_rent_multiplier                                               15.3846   \n", "equity_multiple                                                       1.002   \n", "cash_on_cash_return                                                  0.2031   \n", "total_acquisition_cost                                            940615.38   \n", "aggregated_noi                                                      61140.0   \n", "blended_cap_rate                                                        6.5   \n", "portfolio_irr                                                                 \n", "created_at                                       2025-05-24T17:42:43.568000   \n", "updated_at                                       2025-05-24T17:42:43.568000   \n", "metadata                  {'year': 5, 'repairs': [{'sources': [{'data': ...   \n", "\n", "                                                             2  \\\n", "id                        fa4537fb-2d55-4945-80f3-e90ddb424955   \n", "prop_id                   cf1857af-bccc-4547-aad3-047cf7c526e4   \n", "year                                                         2   \n", "rental_income                                          62057.0   \n", "long_term_rental                                         62057   \n", "short_term_rental                                                \n", "other_income                                                     \n", "vacancy_loss                                                     \n", "credit_loss                                                      \n", "effective_gross_income                                 62057.0   \n", "property_tax                                                     \n", "insurance                                                        \n", "repairs                                                          \n", "maintenance                                                      \n", "professional_fees                                          0.0   \n", "management_fees                                                  \n", "leasing_fees                                                     \n", "legal_fees                                                       \n", "accounting_fees                                                  \n", "engineering_fees                                                 \n", "marketing_fees                                                   \n", "consulting_fees                                                  \n", "utilities                                                        \n", "services                                                         \n", "reserves                                                         \n", "total_operating_expenses                                   0.0   \n", "net_operating_income                                   62057.0   \n", "annual_debt_service                                   59229.59   \n", "dscr                                                    1.0477   \n", "cash_flow_before_taxes                                 2827.41   \n", "cash_flow_after_taxes                                  2827.41   \n", "cumulative_cash_flow                                   4737.82   \n", "cap_rate                                                6.5975   \n", "gross_rent_multiplier                                  15.1573   \n", "equity_multiple                                          1.005   \n", "cash_on_cash_return                                     0.3006   \n", "total_acquisition_cost                               940615.38   \n", "aggregated_noi                                        123197.0   \n", "blended_cap_rate                                       13.0975   \n", "portfolio_irr                                                    \n", "created_at                          2025-05-24T17:42:43.568000   \n", "updated_at                          2025-05-24T17:42:43.568000   \n", "metadata                                                    {}   \n", "\n", "                                                             3  \\\n", "id                        1a29cbd9-b9a1-4a94-bfcb-868fe2046097   \n", "prop_id                   cf1857af-bccc-4547-aad3-047cf7c526e4   \n", "year                                                         3   \n", "rental_income                                          62941.0   \n", "long_term_rental                                         62941   \n", "short_term_rental                                                \n", "other_income                                                     \n", "vacancy_loss                                                     \n", "credit_loss                                                      \n", "effective_gross_income                                 62941.0   \n", "property_tax                                                     \n", "insurance                                                        \n", "repairs                                                          \n", "maintenance                                                      \n", "professional_fees                                          0.0   \n", "management_fees                                                  \n", "leasing_fees                                                     \n", "legal_fees                                                       \n", "accounting_fees                                                  \n", "engineering_fees                                                 \n", "marketing_fees                                                   \n", "consulting_fees                                                  \n", "utilities                                                        \n", "services                                                         \n", "reserves                                                         \n", "total_operating_expenses                                   0.0   \n", "net_operating_income                                   62941.0   \n", "annual_debt_service                                   59229.59   \n", "dscr                                                    1.0627   \n", "cash_flow_before_taxes                                 3711.41   \n", "cash_flow_after_taxes                                  3711.41   \n", "cumulative_cash_flow                                   8449.23   \n", "cap_rate                                                6.6915   \n", "gross_rent_multiplier                                  14.9444   \n", "equity_multiple                                          1.009   \n", "cash_on_cash_return                                     0.3946   \n", "total_acquisition_cost                               940615.38   \n", "aggregated_noi                                        186138.0   \n", "blended_cap_rate                                        19.789   \n", "portfolio_irr                                                    \n", "created_at                          2025-05-24T17:42:43.568000   \n", "updated_at                          2025-05-24T17:42:43.568000   \n", "metadata                                                    {}   \n", "\n", "                                                             4  \\\n", "id                        3dfa39f0-300c-4784-a389-19e600654952   \n", "prop_id                   cf1857af-bccc-4547-aad3-047cf7c526e4   \n", "year                                                         4   \n", "rental_income                                          63791.0   \n", "long_term_rental                                         63791   \n", "short_term_rental                                                \n", "other_income                                                     \n", "vacancy_loss                                                     \n", "credit_loss                                                      \n", "effective_gross_income                                 63791.0   \n", "property_tax                                                     \n", "insurance                                                        \n", "repairs                                                          \n", "maintenance                                                      \n", "professional_fees                                          0.0   \n", "management_fees                                                  \n", "leasing_fees                                                     \n", "legal_fees                                                       \n", "accounting_fees                                                  \n", "engineering_fees                                                 \n", "marketing_fees                                                   \n", "consulting_fees                                                  \n", "utilities                                                        \n", "services                                                         \n", "reserves                                                         \n", "total_operating_expenses                                   0.0   \n", "net_operating_income                                   63791.0   \n", "annual_debt_service                                   59229.59   \n", "dscr                                                     1.077   \n", "cash_flow_before_taxes                                 4561.41   \n", "cash_flow_after_taxes                                  4561.41   \n", "cumulative_cash_flow                                  13010.64   \n", "cap_rate                                                6.7818   \n", "gross_rent_multiplier                                  14.7453   \n", "equity_multiple                                         1.0138   \n", "cash_on_cash_return                                     0.4849   \n", "total_acquisition_cost                               940615.38   \n", "aggregated_noi                                        249929.0   \n", "blended_cap_rate                                       26.5708   \n", "portfolio_irr                                                    \n", "created_at                          2025-05-24T17:42:43.568000   \n", "updated_at                          2025-05-24T17:42:43.568000   \n", "metadata                                                    {}   \n", "\n", "                                                             5  \n", "id                        e91542e6-6678-485f-8b17-c6d26336704c  \n", "prop_id                   cf1857af-bccc-4547-aad3-047cf7c526e4  \n", "year                                                         5  \n", "rental_income                                          64604.0  \n", "long_term_rental                                         64604  \n", "short_term_rental                                               \n", "other_income                                                    \n", "vacancy_loss                                                    \n", "credit_loss                                                     \n", "effective_gross_income                                 64604.0  \n", "property_tax                                                    \n", "insurance                                                       \n", "repairs                                                         \n", "maintenance                                                     \n", "professional_fees                                          0.0  \n", "management_fees                                                 \n", "leasing_fees                                                    \n", "legal_fees                                                      \n", "accounting_fees                                                 \n", "engineering_fees                                                \n", "marketing_fees                                                  \n", "consulting_fees                                                 \n", "utilities                                                       \n", "services                                                        \n", "reserves                                                        \n", "total_operating_expenses                                   0.0  \n", "net_operating_income                                   64604.0  \n", "annual_debt_service                                   59229.59  \n", "dscr                                                    1.0907  \n", "cash_flow_before_taxes                                 5374.41  \n", "cash_flow_after_taxes                                  5374.41  \n", "cumulative_cash_flow                                  18385.05  \n", "cap_rate                                                6.8683  \n", "gross_rent_multiplier                                  14.5597  \n", "equity_multiple                                         1.0195  \n", "cash_on_cash_return                                     0.5714  \n", "total_acquisition_cost                               940615.38  \n", "aggregated_noi                                        314533.0  \n", "blended_cap_rate                                       33.4391  \n", "portfolio_irr                                                   \n", "created_at                          2025-05-24T17:42:43.568000  \n", "updated_at                          2025-05-24T17:42:43.568000  \n", "metadata                                                    {}  "]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["# Calculate rental income using financial service agent\n", "rental_income = await financial_service.agent.run(\n", "    f\"Calculate rental income for property {prop_id}\",\n", "    deps=FinancialTaskInput(\n", "        task_type=\"rental_income\",\n", "        prop_id=prop_id,\n", "        year=1,\n", "        portfolio_id=portfolio_id,\n", "        db=PropertyService(),\n", "        return_projections=True,\n", "        params={}\n", "    )\n", ")\n", "\n", "rental_income = rental_income.output.responses[0]\n", "\n", "# Update computed_df rental_income column if the agent returned a valid money_value\n", "if rental_income.money_value and rental_income.money_value > 0:\n", "    print(f\"Setting rental_income for year 1 to {rental_income.money_value}\")\n", "    computed_df.loc['long_term_rental', 1] = round(rental_income.money_value)\n", "\n", "if rental_income.additional_data:\n", "    for key, value in rental_income.additional_data.items():\n", "        if value.money_value and value.money_value > 0:\n", "            # Set rental_income for years 2, 3, 4, 5 only\n", "            year = value.year\n", "            if year in [2, 3, 4, 5]:\n", "                print(f\"Setting rental_income for year {year} to {value.money_value}\")\n", "                computed_df.loc['long_term_rental', year] = round(value.money_value)\n", "computed_df = await prop_fin.compute_property_financials(computed_df)\n", "computed_df.head(50)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2025-05-25 22:33:05 | INFO | No usable database solar data for property cf1857af-bccc-4547-aad3-047cf7c526e4, trying Google Solar API\n", "2025-05-25 22:33:05 | WARNING | Geocoding failed for address: 214 SEASIDE AVENUE ATLANTIC CITY NJ 08401\n", "2025-05-25 22:33:05 | WARNING | Could not get coordinates for property cf1857af-bccc-4547-aad3-047cf7c526e4\n", "2025-05-25 22:33:05 | INFO | No Google Solar API data available, estimating from property area for cf1857af-bccc-4547-aad3-047cf7c526e4\n", "2025-05-25 22:33:05 | INFO | Fallback estimation: 464.51 m2 from 5 units (assumed 1000 sqft/unit)\n", "2025-05-25 22:33:05 | INFO | Using estimated solar potential for property cf1857af-bccc-4547-aad3-047cf7c526e4\n", "2025-05-25 22:33:05 | INFO | Searching for electricity rates: electricity rates kwh cost ZIP code 08401 utility company residential 2024\n", "2025-05-25 22:33:06 | INFO | Found electricity rates: {'rate_per_kwh': 0.0943, 'source': 'https://www.chooseenergy.com/electricity-rates/new-jersey/', 'description': 'Electricity rate for ZIP code 08401', 'content_snippet': '| utility | price to compare | effective date |\\n| --- | --- | --- |\\n| atlantic city electric | 9.43¢ per kwh | effective 10/1/2024 |\\n| jersey central power & light | 10¢ per kwh | effective 12/1/2024 '}\n", "2025-05-25 22:33:06 | INFO | Solar calculation (property_area_estimation): 78039 kWh/year * $0.094/kWh = $7359.03/year\n", "2025-05-25 22:33:06 | INFO | Solar Year 1: $7359.03\n", "2025-05-25 22:33:06 | INFO | Solar Year 2: $7543.01\n", "2025-05-25 22:33:06 | INFO | Solar Year 3: $7731.58\n", "2025-05-25 22:33:06 | INFO | Solar Year 4: $7924.87\n", "2025-05-25 22:33:06 | INFO | Solar Year 5: $8122.99\n", "Setting other_income for year 1 to 7359.032436\n", "Setting other_income for year 2 to 7543.0082469\n", "Setting other_income for year 3 to 7731.5834530725\n", "Setting other_income for year 4 to 7924.873039399312\n", "Setting other_income for year 5 to 8122.994865384294\n", "2025-05-25 22:33:20 | INFO | Retrieved current mortgage rate from FRED: 6.86%\n", "2025-05-25 22:33:20 | WARNING | Year 1: DSCR (1.16) below typical lending requirement (1.25x)\n", "2025-05-25 22:33:20 | WARNING | Year 2: DSCR (1.18) below typical lending requirement (1.25x)\n", "2025-05-25 22:33:20 | WARNING | Year 3: DSCR (1.19) below typical lending requirement (1.25x)\n", "2025-05-25 22:33:20 | WARNING | Year 4: DSCR (1.21) below typical lending requirement (1.25x)\n", "2025-05-25 22:33:20 | WARNING | Year 5: DSCR (1.23) below typical lending requirement (1.25x)\n", "2025-05-25 22:33:20 | WARNING | Could not compute IRR - numpy-financial package may not be available: No module named 'numpy_financial'\n", "2025-05-25 22:33:20 | INFO | Financial computation completed for 5 years\n", "2025-05-25 22:33:20 | INFO | Year 1: NOI=$68,499.00, Expenses=$0.00\n", "2025-05-25 22:33:20 | INFO | Year 2: NOI=$69,600.00, Expenses=$0.00\n", "2025-05-25 22:33:20 | INFO | Year 3: NOI=$70,673.00, Expenses=$0.00\n", "2025-05-25 22:33:20 | INFO | Year 4: NOI=$71,716.00, Expenses=$0.00\n", "2025-05-25 22:33:20 | INFO | Year 5: NOI=$72,727.00, Expenses=$0.00\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>1</th>\n", "      <th>2</th>\n", "      <th>3</th>\n", "      <th>4</th>\n", "      <th>5</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>id</th>\n", "      <td>54d93942-caf6-4132-82a0-762d294039d5</td>\n", "      <td>fa4537fb-2d55-4945-80f3-e90ddb424955</td>\n", "      <td>1a29cbd9-b9a1-4a94-bfcb-868fe2046097</td>\n", "      <td>3dfa39f0-300c-4784-a389-19e600654952</td>\n", "      <td>e91542e6-6678-485f-8b17-c6d26336704c</td>\n", "    </tr>\n", "    <tr>\n", "      <th>prop_id</th>\n", "      <td>cf1857af-bccc-4547-aad3-047cf7c526e4</td>\n", "      <td>cf1857af-bccc-4547-aad3-047cf7c526e4</td>\n", "      <td>cf1857af-bccc-4547-aad3-047cf7c526e4</td>\n", "      <td>cf1857af-bccc-4547-aad3-047cf7c526e4</td>\n", "      <td>cf1857af-bccc-4547-aad3-047cf7c526e4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>year</th>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td>3</td>\n", "      <td>4</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>rental_income</th>\n", "      <td>61140.0</td>\n", "      <td>62057.0</td>\n", "      <td>62941.0</td>\n", "      <td>63791.0</td>\n", "      <td>64604.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>long_term_rental</th>\n", "      <td>61140</td>\n", "      <td>62057</td>\n", "      <td>62941</td>\n", "      <td>63791</td>\n", "      <td>64604</td>\n", "    </tr>\n", "    <tr>\n", "      <th>short_term_rental</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>other_income</th>\n", "      <td>7359</td>\n", "      <td>7543</td>\n", "      <td>7732</td>\n", "      <td>7925</td>\n", "      <td>8123</td>\n", "    </tr>\n", "    <tr>\n", "      <th>vacancy_loss</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>credit_loss</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>effective_gross_income</th>\n", "      <td>68499.0</td>\n", "      <td>69600.0</td>\n", "      <td>70673.0</td>\n", "      <td>71716.0</td>\n", "      <td>72727.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>property_tax</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>insurance</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>repairs</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>maintenance</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>professional_fees</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>management_fees</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>leasing_fees</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>legal_fees</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>accounting_fees</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>engineering_fees</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>marketing_fees</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>consulting_fees</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>utilities</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>services</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>reserves</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>total_operating_expenses</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>net_operating_income</th>\n", "      <td>68499.0</td>\n", "      <td>69600.0</td>\n", "      <td>70673.0</td>\n", "      <td>71716.0</td>\n", "      <td>72727.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>annual_debt_service</th>\n", "      <td>59229.59</td>\n", "      <td>59229.59</td>\n", "      <td>59229.59</td>\n", "      <td>59229.59</td>\n", "      <td>59229.59</td>\n", "    </tr>\n", "    <tr>\n", "      <th>dscr</th>\n", "      <td>1.1565</td>\n", "      <td>1.1751</td>\n", "      <td>1.1932</td>\n", "      <td>1.2108</td>\n", "      <td>1.2279</td>\n", "    </tr>\n", "    <tr>\n", "      <th>cash_flow_before_taxes</th>\n", "      <td>9269.41</td>\n", "      <td>10370.41</td>\n", "      <td>11443.41</td>\n", "      <td>12486.41</td>\n", "      <td>13497.41</td>\n", "    </tr>\n", "    <tr>\n", "      <th>cash_flow_after_taxes</th>\n", "      <td>9269.41</td>\n", "      <td>10370.41</td>\n", "      <td>11443.41</td>\n", "      <td>12486.41</td>\n", "      <td>13497.41</td>\n", "    </tr>\n", "    <tr>\n", "      <th>cumulative_cash_flow</th>\n", "      <td>9269.41</td>\n", "      <td>19639.82</td>\n", "      <td>31083.23</td>\n", "      <td>43569.64</td>\n", "      <td>57067.05</td>\n", "    </tr>\n", "    <tr>\n", "      <th>cap_rate</th>\n", "      <td>7.2824</td>\n", "      <td>7.3994</td>\n", "      <td>7.5135</td>\n", "      <td>7.6244</td>\n", "      <td>7.7319</td>\n", "    </tr>\n", "    <tr>\n", "      <th>gross_rent_multiplier</th>\n", "      <td>15.3846</td>\n", "      <td>15.1573</td>\n", "      <td>14.9444</td>\n", "      <td>14.7453</td>\n", "      <td>14.5597</td>\n", "    </tr>\n", "    <tr>\n", "      <th>equity_multiple</th>\n", "      <td>1.0099</td>\n", "      <td>1.0209</td>\n", "      <td>1.033</td>\n", "      <td>1.0463</td>\n", "      <td>1.0607</td>\n", "    </tr>\n", "    <tr>\n", "      <th>cash_on_cash_return</th>\n", "      <td>0.9855</td>\n", "      <td>1.1025</td>\n", "      <td>1.2166</td>\n", "      <td>1.3275</td>\n", "      <td>1.435</td>\n", "    </tr>\n", "    <tr>\n", "      <th>total_acquisition_cost</th>\n", "      <td>940615.38</td>\n", "      <td>940615.38</td>\n", "      <td>940615.38</td>\n", "      <td>940615.38</td>\n", "      <td>940615.38</td>\n", "    </tr>\n", "    <tr>\n", "      <th>aggregated_noi</th>\n", "      <td>68499.0</td>\n", "      <td>138099.0</td>\n", "      <td>208772.0</td>\n", "      <td>280488.0</td>\n", "      <td>353215.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>blended_cap_rate</th>\n", "      <td>7.2824</td>\n", "      <td>14.6818</td>\n", "      <td>22.1953</td>\n", "      <td>29.8196</td>\n", "      <td>37.5515</td>\n", "    </tr>\n", "    <tr>\n", "      <th>portfolio_irr</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>created_at</th>\n", "      <td>2025-05-24T17:42:43.568000</td>\n", "      <td>2025-05-24T17:42:43.568000</td>\n", "      <td>2025-05-24T17:42:43.568000</td>\n", "      <td>2025-05-24T17:42:43.568000</td>\n", "      <td>2025-05-24T17:42:43.568000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>updated_at</th>\n", "      <td>2025-05-24T17:42:43.568000</td>\n", "      <td>2025-05-24T17:42:43.568000</td>\n", "      <td>2025-05-24T17:42:43.568000</td>\n", "      <td>2025-05-24T17:42:43.568000</td>\n", "      <td>2025-05-24T17:42:43.568000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>metadata</th>\n", "      <td>{'year': 5, 'repairs': [{'sources': [{'data': ...</td>\n", "      <td>{}</td>\n", "      <td>{}</td>\n", "      <td>{}</td>\n", "      <td>{}</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                                                          1  \\\n", "id                                     54d93942-caf6-4132-82a0-762d294039d5   \n", "prop_id                                cf1857af-bccc-4547-aad3-047cf7c526e4   \n", "year                                                                      1   \n", "rental_income                                                       61140.0   \n", "long_term_rental                                                      61140   \n", "short_term_rental                                                             \n", "other_income                                                           7359   \n", "vacancy_loss                                                                  \n", "credit_loss                                                                   \n", "effective_gross_income                                              68499.0   \n", "property_tax                                                                  \n", "insurance                                                                     \n", "repairs                                                                       \n", "maintenance                                                                   \n", "professional_fees                                                       0.0   \n", "management_fees                                                               \n", "leasing_fees                                                                  \n", "legal_fees                                                                    \n", "accounting_fees                                                               \n", "engineering_fees                                                              \n", "marketing_fees                                                                \n", "consulting_fees                                                               \n", "utilities                                                                     \n", "services                                                                      \n", "reserves                                                                      \n", "total_operating_expenses                                                0.0   \n", "net_operating_income                                                68499.0   \n", "annual_debt_service                                                59229.59   \n", "dscr                                                                 1.1565   \n", "cash_flow_before_taxes                                              9269.41   \n", "cash_flow_after_taxes                                               9269.41   \n", "cumulative_cash_flow                                                9269.41   \n", "cap_rate                                                             7.2824   \n", "gross_rent_multiplier                                               15.3846   \n", "equity_multiple                                                      1.0099   \n", "cash_on_cash_return                                                  0.9855   \n", "total_acquisition_cost                                            940615.38   \n", "aggregated_noi                                                      68499.0   \n", "blended_cap_rate                                                     7.2824   \n", "portfolio_irr                                                                 \n", "created_at                                       2025-05-24T17:42:43.568000   \n", "updated_at                                       2025-05-24T17:42:43.568000   \n", "metadata                  {'year': 5, 'repairs': [{'sources': [{'data': ...   \n", "\n", "                                                             2  \\\n", "id                        fa4537fb-2d55-4945-80f3-e90ddb424955   \n", "prop_id                   cf1857af-bccc-4547-aad3-047cf7c526e4   \n", "year                                                         2   \n", "rental_income                                          62057.0   \n", "long_term_rental                                         62057   \n", "short_term_rental                                                \n", "other_income                                              7543   \n", "vacancy_loss                                                     \n", "credit_loss                                                      \n", "effective_gross_income                                 69600.0   \n", "property_tax                                                     \n", "insurance                                                        \n", "repairs                                                          \n", "maintenance                                                      \n", "professional_fees                                          0.0   \n", "management_fees                                                  \n", "leasing_fees                                                     \n", "legal_fees                                                       \n", "accounting_fees                                                  \n", "engineering_fees                                                 \n", "marketing_fees                                                   \n", "consulting_fees                                                  \n", "utilities                                                        \n", "services                                                         \n", "reserves                                                         \n", "total_operating_expenses                                   0.0   \n", "net_operating_income                                   69600.0   \n", "annual_debt_service                                   59229.59   \n", "dscr                                                    1.1751   \n", "cash_flow_before_taxes                                10370.41   \n", "cash_flow_after_taxes                                 10370.41   \n", "cumulative_cash_flow                                  19639.82   \n", "cap_rate                                                7.3994   \n", "gross_rent_multiplier                                  15.1573   \n", "equity_multiple                                         1.0209   \n", "cash_on_cash_return                                     1.1025   \n", "total_acquisition_cost                               940615.38   \n", "aggregated_noi                                        138099.0   \n", "blended_cap_rate                                       14.6818   \n", "portfolio_irr                                                    \n", "created_at                          2025-05-24T17:42:43.568000   \n", "updated_at                          2025-05-24T17:42:43.568000   \n", "metadata                                                    {}   \n", "\n", "                                                             3  \\\n", "id                        1a29cbd9-b9a1-4a94-bfcb-868fe2046097   \n", "prop_id                   cf1857af-bccc-4547-aad3-047cf7c526e4   \n", "year                                                         3   \n", "rental_income                                          62941.0   \n", "long_term_rental                                         62941   \n", "short_term_rental                                                \n", "other_income                                              7732   \n", "vacancy_loss                                                     \n", "credit_loss                                                      \n", "effective_gross_income                                 70673.0   \n", "property_tax                                                     \n", "insurance                                                        \n", "repairs                                                          \n", "maintenance                                                      \n", "professional_fees                                          0.0   \n", "management_fees                                                  \n", "leasing_fees                                                     \n", "legal_fees                                                       \n", "accounting_fees                                                  \n", "engineering_fees                                                 \n", "marketing_fees                                                   \n", "consulting_fees                                                  \n", "utilities                                                        \n", "services                                                         \n", "reserves                                                         \n", "total_operating_expenses                                   0.0   \n", "net_operating_income                                   70673.0   \n", "annual_debt_service                                   59229.59   \n", "dscr                                                    1.1932   \n", "cash_flow_before_taxes                                11443.41   \n", "cash_flow_after_taxes                                 11443.41   \n", "cumulative_cash_flow                                  31083.23   \n", "cap_rate                                                7.5135   \n", "gross_rent_multiplier                                  14.9444   \n", "equity_multiple                                          1.033   \n", "cash_on_cash_return                                     1.2166   \n", "total_acquisition_cost                               940615.38   \n", "aggregated_noi                                        208772.0   \n", "blended_cap_rate                                       22.1953   \n", "portfolio_irr                                                    \n", "created_at                          2025-05-24T17:42:43.568000   \n", "updated_at                          2025-05-24T17:42:43.568000   \n", "metadata                                                    {}   \n", "\n", "                                                             4  \\\n", "id                        3dfa39f0-300c-4784-a389-19e600654952   \n", "prop_id                   cf1857af-bccc-4547-aad3-047cf7c526e4   \n", "year                                                         4   \n", "rental_income                                          63791.0   \n", "long_term_rental                                         63791   \n", "short_term_rental                                                \n", "other_income                                              7925   \n", "vacancy_loss                                                     \n", "credit_loss                                                      \n", "effective_gross_income                                 71716.0   \n", "property_tax                                                     \n", "insurance                                                        \n", "repairs                                                          \n", "maintenance                                                      \n", "professional_fees                                          0.0   \n", "management_fees                                                  \n", "leasing_fees                                                     \n", "legal_fees                                                       \n", "accounting_fees                                                  \n", "engineering_fees                                                 \n", "marketing_fees                                                   \n", "consulting_fees                                                  \n", "utilities                                                        \n", "services                                                         \n", "reserves                                                         \n", "total_operating_expenses                                   0.0   \n", "net_operating_income                                   71716.0   \n", "annual_debt_service                                   59229.59   \n", "dscr                                                    1.2108   \n", "cash_flow_before_taxes                                12486.41   \n", "cash_flow_after_taxes                                 12486.41   \n", "cumulative_cash_flow                                  43569.64   \n", "cap_rate                                                7.6244   \n", "gross_rent_multiplier                                  14.7453   \n", "equity_multiple                                         1.0463   \n", "cash_on_cash_return                                     1.3275   \n", "total_acquisition_cost                               940615.38   \n", "aggregated_noi                                        280488.0   \n", "blended_cap_rate                                       29.8196   \n", "portfolio_irr                                                    \n", "created_at                          2025-05-24T17:42:43.568000   \n", "updated_at                          2025-05-24T17:42:43.568000   \n", "metadata                                                    {}   \n", "\n", "                                                             5  \n", "id                        e91542e6-6678-485f-8b17-c6d26336704c  \n", "prop_id                   cf1857af-bccc-4547-aad3-047cf7c526e4  \n", "year                                                         5  \n", "rental_income                                          64604.0  \n", "long_term_rental                                         64604  \n", "short_term_rental                                               \n", "other_income                                              8123  \n", "vacancy_loss                                                    \n", "credit_loss                                                     \n", "effective_gross_income                                 72727.0  \n", "property_tax                                                    \n", "insurance                                                       \n", "repairs                                                         \n", "maintenance                                                     \n", "professional_fees                                          0.0  \n", "management_fees                                                 \n", "leasing_fees                                                    \n", "legal_fees                                                      \n", "accounting_fees                                                 \n", "engineering_fees                                                \n", "marketing_fees                                                  \n", "consulting_fees                                                 \n", "utilities                                                       \n", "services                                                        \n", "reserves                                                        \n", "total_operating_expenses                                   0.0  \n", "net_operating_income                                   72727.0  \n", "annual_debt_service                                   59229.59  \n", "dscr                                                    1.2279  \n", "cash_flow_before_taxes                                13497.41  \n", "cash_flow_after_taxes                                 13497.41  \n", "cumulative_cash_flow                                  57067.05  \n", "cap_rate                                                7.7319  \n", "gross_rent_multiplier                                  14.5597  \n", "equity_multiple                                         1.0607  \n", "cash_on_cash_return                                      1.435  \n", "total_acquisition_cost                               940615.38  \n", "aggregated_noi                                        353215.0  \n", "blended_cap_rate                                       37.5515  \n", "portfolio_irr                                                   \n", "created_at                          2025-05-24T17:42:43.568000  \n", "updated_at                          2025-05-24T17:42:43.568000  \n", "metadata                                                    {}  "]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["# Calculate rental income using financial service agent\n", "other_income = await financial_service.agent.run(\n", "    f\"Calculate other income for property {prop_id}\",\n", "    deps=FinancialTaskInput(\n", "        task_type=\"other_income\",\n", "        prop_id=prop_id,\n", "        year=1,\n", "        portfolio_id=portfolio_id,\n", "        db=PropertyService(),\n", "        return_projections=True,\n", "        params={}\n", "    )\n", ")\n", "\n", "other_income = other_income.output.responses[0]\n", "\n", "# Update computed_df rental_income column if the agent returned a valid money_value\n", "if other_income.money_value and other_income.money_value > 0:\n", "    print(f\"Setting other_income for year 1 to {other_income.money_value}\")\n", "    computed_df.loc['other_income', 1] = round(other_income.money_value)\n", "\n", "if other_income.additional_data:\n", "    for key, value in other_income.additional_data.items():\n", "        if value.money_value and value.money_value > 0:\n", "            # Set rental_income for years 2, 3, 4, 5 only\n", "            year = value.year\n", "            if year in [2, 3, 4, 5]:\n", "                print(f\"Setting other_income for year {year} to {value.money_value}\")\n", "                computed_df.loc['other_income', year] = round(value.money_value)\n", "computed_df = await prop_fin.compute_property_financials(computed_df)\n", "computed_df.head(50)"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2025-05-25 22:33:33 | INFO | Generating rental projections for vacancy loss calculation\n", "2025-05-25 22:33:33 | INFO | Property cf1857af-bccc-4547-aad3-047cf7c526e4: 5 units with rent, 0 units missing rent\n", "2025-05-25 22:33:33 | WARNING | No units have sqft data - cannot calculate rent per sqft\n", "2025-05-25 22:33:33 | INFO | Sqft data not available, using median rent estimation\n", "2025-05-25 22:33:33 | INFO | Calculated median rent: $990.00 from 5 units\n", "2025-05-25 22:33:33 | INFO | Individual rents: ['$875.00', '$950.00', '$990.00', '$1030.00', '$1250.00']\n", "2025-05-25 22:33:33 | INFO | Known rent - Unit 3: $950.00\n", "2025-05-25 22:33:33 | INFO | Known rent - Unit 4: $1250.00\n", "2025-05-25 22:33:33 | INFO | Known rent - Unit 2: $1030.00\n", "2025-05-25 22:33:33 | INFO | Known rent - Unit 1: $875.00\n", "2025-05-25 22:33:33 | INFO | Known rent - Unit 5: $990.00\n", "2025-05-25 22:33:33 | INFO | Total estimated rental income for property cf1857af-bccc-4547-aad3-047cf7c526e4: $5095.00/month\n", "2025-05-25 22:33:33 | INFO | Retrieved historical inflation data for projections\n", "2025-05-25 22:33:33 | INFO | FRED client initialized successfully\n", "2025-05-25 22:33:33 | INFO | Fetching FRED data for series: CUUR0000SEHA\n", "2025-05-25 22:33:33 | INFO | Retrieved 76 data points for series CUUR0000SEHA\n", "2025-05-25 22:33:33 | INFO | FRED-based rental growth rates calculated: {2: 0.015, 3: 0.014249999999999999, 4: 0.0135, 5: 0.01275}\n", "2025-05-25 22:33:33 | INFO | Based on 36 months of recent FRED rental price data\n", "2025-05-25 22:33:33 | INFO | Average historical growth rate: 0.49%\n", "2025-05-25 22:33:33 | INFO | Year 2: $62057.10 (growth: 1.5%)\n", "2025-05-25 22:33:33 | INFO | Year 3: $62941.41 (growth: 1.4%)\n", "2025-05-25 22:33:33 | INFO | Year 4: $63791.12 (growth: 1.4%)\n", "2025-05-25 22:33:33 | INFO | Year 5: $64604.46 (growth: 1.3%)\n", "2025-05-25 22:33:33 | INFO | Searching for vacancy rates: rental vacancy rate ZIP code 08401 apartment housing market 2024 percentage\n", "2025-05-25 22:33:36 | WARNING | Could not extract specific vacancy rate from search results for ZIP code 08401\n", "2025-05-25 22:33:36 | INFO | Found vacancy rates: {'vacancy_rate': 0.05, 'source': 'estimated_average'}\n", "2025-05-25 22:33:36 | INFO | Vacancy loss calculation: $61140.00 * 5.0% = $-3057.00\n", "2025-05-25 22:33:36 | INFO | Vacancy Year 1: $-3057.00 loss (5.0% of $61140.00)\n", "2025-05-25 22:33:36 | INFO | Vacancy loss calculation: $62057.10 * 5.0% = $-3102.85\n", "2025-05-25 22:33:36 | INFO | Vacancy Year 2: $-3102.85 loss (5.0% of $62057.10)\n", "2025-05-25 22:33:36 | INFO | Vacancy loss calculation: $62941.41 * 5.0% = $-3147.07\n", "2025-05-25 22:33:36 | INFO | Vacancy Year 3: $-3147.07 loss (5.0% of $62941.41)\n", "2025-05-25 22:33:36 | INFO | Vacancy loss calculation: $63791.12 * 5.0% = $-3189.56\n", "2025-05-25 22:33:36 | INFO | Vacancy Year 4: $-3189.56 loss (5.0% of $63791.12)\n", "2025-05-25 22:33:36 | INFO | Vacancy loss calculation: $64604.46 * 5.0% = $-3230.22\n", "2025-05-25 22:33:36 | INFO | Vacancy Year 5: $-3230.22 loss (5.0% of $64604.46)\n", "Setting vacancy_loss for year 1 to -3057.0\n", "Setting vacancy_loss for year 2 to -3102.8549999999996\n", "Setting vacancy_loss for year 3 to -3147.07068375\n", "Setting vacancy_loss for year 4 to -3189.5561379806254\n", "Setting vacancy_loss for year 5 to -3230.2229787398783\n", "2025-05-25 22:33:55 | INFO | Retrieved current mortgage rate from FRED: 6.86%\n", "2025-05-25 22:33:55 | WARNING | Year 1: DSCR (1.10) below typical lending requirement (1.25x)\n", "2025-05-25 22:33:55 | WARNING | Year 2: DSCR (1.12) below typical lending requirement (1.25x)\n", "2025-05-25 22:33:55 | WARNING | Year 3: DSCR (1.14) below typical lending requirement (1.25x)\n", "2025-05-25 22:33:55 | WARNING | Year 4: DSCR (1.16) below typical lending requirement (1.25x)\n", "2025-05-25 22:33:55 | WARNING | Year 5: DSCR (1.17) below typical lending requirement (1.25x)\n", "2025-05-25 22:33:55 | WARNING | Could not compute IRR - numpy-financial package may not be available: No module named 'numpy_financial'\n", "2025-05-25 22:33:55 | INFO | Financial computation completed for 5 years\n", "2025-05-25 22:33:55 | INFO | Year 1: NOI=$65,442.00, Expenses=$0.00\n", "2025-05-25 22:33:55 | INFO | Year 2: NOI=$66,497.00, Expenses=$0.00\n", "2025-05-25 22:33:55 | INFO | Year 3: NOI=$67,526.00, Expenses=$0.00\n", "2025-05-25 22:33:55 | INFO | Year 4: NOI=$68,526.00, Expenses=$0.00\n", "2025-05-25 22:33:55 | INFO | Year 5: NOI=$69,497.00, Expenses=$0.00\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>1</th>\n", "      <th>2</th>\n", "      <th>3</th>\n", "      <th>4</th>\n", "      <th>5</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>id</th>\n", "      <td>54d93942-caf6-4132-82a0-762d294039d5</td>\n", "      <td>fa4537fb-2d55-4945-80f3-e90ddb424955</td>\n", "      <td>1a29cbd9-b9a1-4a94-bfcb-868fe2046097</td>\n", "      <td>3dfa39f0-300c-4784-a389-19e600654952</td>\n", "      <td>e91542e6-6678-485f-8b17-c6d26336704c</td>\n", "    </tr>\n", "    <tr>\n", "      <th>prop_id</th>\n", "      <td>cf1857af-bccc-4547-aad3-047cf7c526e4</td>\n", "      <td>cf1857af-bccc-4547-aad3-047cf7c526e4</td>\n", "      <td>cf1857af-bccc-4547-aad3-047cf7c526e4</td>\n", "      <td>cf1857af-bccc-4547-aad3-047cf7c526e4</td>\n", "      <td>cf1857af-bccc-4547-aad3-047cf7c526e4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>year</th>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td>3</td>\n", "      <td>4</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>rental_income</th>\n", "      <td>61140.0</td>\n", "      <td>62057.0</td>\n", "      <td>62941.0</td>\n", "      <td>63791.0</td>\n", "      <td>64604.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>long_term_rental</th>\n", "      <td>61140</td>\n", "      <td>62057</td>\n", "      <td>62941</td>\n", "      <td>63791</td>\n", "      <td>64604</td>\n", "    </tr>\n", "    <tr>\n", "      <th>short_term_rental</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>other_income</th>\n", "      <td>7359</td>\n", "      <td>7543</td>\n", "      <td>7732</td>\n", "      <td>7925</td>\n", "      <td>8123</td>\n", "    </tr>\n", "    <tr>\n", "      <th>vacancy_loss</th>\n", "      <td>-3057</td>\n", "      <td>-3103</td>\n", "      <td>-3147</td>\n", "      <td>-3190</td>\n", "      <td>-3230</td>\n", "    </tr>\n", "    <tr>\n", "      <th>credit_loss</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>effective_gross_income</th>\n", "      <td>65442.0</td>\n", "      <td>66497.0</td>\n", "      <td>67526.0</td>\n", "      <td>68526.0</td>\n", "      <td>69497.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>property_tax</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>insurance</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>repairs</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>maintenance</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>professional_fees</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>management_fees</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>leasing_fees</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>legal_fees</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>accounting_fees</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>engineering_fees</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>marketing_fees</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>consulting_fees</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>utilities</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>services</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>reserves</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>total_operating_expenses</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>net_operating_income</th>\n", "      <td>65442.0</td>\n", "      <td>66497.0</td>\n", "      <td>67526.0</td>\n", "      <td>68526.0</td>\n", "      <td>69497.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>annual_debt_service</th>\n", "      <td>59229.59</td>\n", "      <td>59229.59</td>\n", "      <td>59229.59</td>\n", "      <td>59229.59</td>\n", "      <td>59229.59</td>\n", "    </tr>\n", "    <tr>\n", "      <th>dscr</th>\n", "      <td>1.1049</td>\n", "      <td>1.1227</td>\n", "      <td>1.1401</td>\n", "      <td>1.157</td>\n", "      <td>1.1733</td>\n", "    </tr>\n", "    <tr>\n", "      <th>cash_flow_before_taxes</th>\n", "      <td>6212.41</td>\n", "      <td>7267.41</td>\n", "      <td>8296.41</td>\n", "      <td>9296.41</td>\n", "      <td>10267.41</td>\n", "    </tr>\n", "    <tr>\n", "      <th>cash_flow_after_taxes</th>\n", "      <td>6212.41</td>\n", "      <td>7267.41</td>\n", "      <td>8296.41</td>\n", "      <td>9296.41</td>\n", "      <td>10267.41</td>\n", "    </tr>\n", "    <tr>\n", "      <th>cumulative_cash_flow</th>\n", "      <td>6212.41</td>\n", "      <td>13479.82</td>\n", "      <td>21776.23</td>\n", "      <td>31072.64</td>\n", "      <td>41340.05</td>\n", "    </tr>\n", "    <tr>\n", "      <th>cap_rate</th>\n", "      <td>6.9574</td>\n", "      <td>7.0695</td>\n", "      <td>7.1789</td>\n", "      <td>7.2852</td>\n", "      <td>7.3885</td>\n", "    </tr>\n", "    <tr>\n", "      <th>gross_rent_multiplier</th>\n", "      <td>15.3846</td>\n", "      <td>15.1573</td>\n", "      <td>14.9444</td>\n", "      <td>14.7453</td>\n", "      <td>14.5597</td>\n", "    </tr>\n", "    <tr>\n", "      <th>equity_multiple</th>\n", "      <td>1.0066</td>\n", "      <td>1.0143</td>\n", "      <td>1.0232</td>\n", "      <td>1.033</td>\n", "      <td>1.044</td>\n", "    </tr>\n", "    <tr>\n", "      <th>cash_on_cash_return</th>\n", "      <td>0.6605</td>\n", "      <td>0.7726</td>\n", "      <td>0.882</td>\n", "      <td>0.9883</td>\n", "      <td>1.0916</td>\n", "    </tr>\n", "    <tr>\n", "      <th>total_acquisition_cost</th>\n", "      <td>940615.38</td>\n", "      <td>940615.38</td>\n", "      <td>940615.38</td>\n", "      <td>940615.38</td>\n", "      <td>940615.38</td>\n", "    </tr>\n", "    <tr>\n", "      <th>aggregated_noi</th>\n", "      <td>65442.0</td>\n", "      <td>131939.0</td>\n", "      <td>199465.0</td>\n", "      <td>267991.0</td>\n", "      <td>337488.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>blended_cap_rate</th>\n", "      <td>6.9574</td>\n", "      <td>14.0269</td>\n", "      <td>21.2058</td>\n", "      <td>28.491</td>\n", "      <td>35.8795</td>\n", "    </tr>\n", "    <tr>\n", "      <th>portfolio_irr</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>created_at</th>\n", "      <td>2025-05-24T17:42:43.568000</td>\n", "      <td>2025-05-24T17:42:43.568000</td>\n", "      <td>2025-05-24T17:42:43.568000</td>\n", "      <td>2025-05-24T17:42:43.568000</td>\n", "      <td>2025-05-24T17:42:43.568000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>updated_at</th>\n", "      <td>2025-05-24T17:42:43.568000</td>\n", "      <td>2025-05-24T17:42:43.568000</td>\n", "      <td>2025-05-24T17:42:43.568000</td>\n", "      <td>2025-05-24T17:42:43.568000</td>\n", "      <td>2025-05-24T17:42:43.568000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>metadata</th>\n", "      <td>{'year': 5, 'repairs': [{'sources': [{'data': ...</td>\n", "      <td>{}</td>\n", "      <td>{}</td>\n", "      <td>{}</td>\n", "      <td>{}</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                                                          1  \\\n", "id                                     54d93942-caf6-4132-82a0-762d294039d5   \n", "prop_id                                cf1857af-bccc-4547-aad3-047cf7c526e4   \n", "year                                                                      1   \n", "rental_income                                                       61140.0   \n", "long_term_rental                                                      61140   \n", "short_term_rental                                                             \n", "other_income                                                           7359   \n", "vacancy_loss                                                          -3057   \n", "credit_loss                                                                   \n", "effective_gross_income                                              65442.0   \n", "property_tax                                                                  \n", "insurance                                                                     \n", "repairs                                                                       \n", "maintenance                                                                   \n", "professional_fees                                                       0.0   \n", "management_fees                                                               \n", "leasing_fees                                                                  \n", "legal_fees                                                                    \n", "accounting_fees                                                               \n", "engineering_fees                                                              \n", "marketing_fees                                                                \n", "consulting_fees                                                               \n", "utilities                                                                     \n", "services                                                                      \n", "reserves                                                                      \n", "total_operating_expenses                                                0.0   \n", "net_operating_income                                                65442.0   \n", "annual_debt_service                                                59229.59   \n", "dscr                                                                 1.1049   \n", "cash_flow_before_taxes                                              6212.41   \n", "cash_flow_after_taxes                                               6212.41   \n", "cumulative_cash_flow                                                6212.41   \n", "cap_rate                                                             6.9574   \n", "gross_rent_multiplier                                               15.3846   \n", "equity_multiple                                                      1.0066   \n", "cash_on_cash_return                                                  0.6605   \n", "total_acquisition_cost                                            940615.38   \n", "aggregated_noi                                                      65442.0   \n", "blended_cap_rate                                                     6.9574   \n", "portfolio_irr                                                                 \n", "created_at                                       2025-05-24T17:42:43.568000   \n", "updated_at                                       2025-05-24T17:42:43.568000   \n", "metadata                  {'year': 5, 'repairs': [{'sources': [{'data': ...   \n", "\n", "                                                             2  \\\n", "id                        fa4537fb-2d55-4945-80f3-e90ddb424955   \n", "prop_id                   cf1857af-bccc-4547-aad3-047cf7c526e4   \n", "year                                                         2   \n", "rental_income                                          62057.0   \n", "long_term_rental                                         62057   \n", "short_term_rental                                                \n", "other_income                                              7543   \n", "vacancy_loss                                             -3103   \n", "credit_loss                                                      \n", "effective_gross_income                                 66497.0   \n", "property_tax                                                     \n", "insurance                                                        \n", "repairs                                                          \n", "maintenance                                                      \n", "professional_fees                                          0.0   \n", "management_fees                                                  \n", "leasing_fees                                                     \n", "legal_fees                                                       \n", "accounting_fees                                                  \n", "engineering_fees                                                 \n", "marketing_fees                                                   \n", "consulting_fees                                                  \n", "utilities                                                        \n", "services                                                         \n", "reserves                                                         \n", "total_operating_expenses                                   0.0   \n", "net_operating_income                                   66497.0   \n", "annual_debt_service                                   59229.59   \n", "dscr                                                    1.1227   \n", "cash_flow_before_taxes                                 7267.41   \n", "cash_flow_after_taxes                                  7267.41   \n", "cumulative_cash_flow                                  13479.82   \n", "cap_rate                                                7.0695   \n", "gross_rent_multiplier                                  15.1573   \n", "equity_multiple                                         1.0143   \n", "cash_on_cash_return                                     0.7726   \n", "total_acquisition_cost                               940615.38   \n", "aggregated_noi                                        131939.0   \n", "blended_cap_rate                                       14.0269   \n", "portfolio_irr                                                    \n", "created_at                          2025-05-24T17:42:43.568000   \n", "updated_at                          2025-05-24T17:42:43.568000   \n", "metadata                                                    {}   \n", "\n", "                                                             3  \\\n", "id                        1a29cbd9-b9a1-4a94-bfcb-868fe2046097   \n", "prop_id                   cf1857af-bccc-4547-aad3-047cf7c526e4   \n", "year                                                         3   \n", "rental_income                                          62941.0   \n", "long_term_rental                                         62941   \n", "short_term_rental                                                \n", "other_income                                              7732   \n", "vacancy_loss                                             -3147   \n", "credit_loss                                                      \n", "effective_gross_income                                 67526.0   \n", "property_tax                                                     \n", "insurance                                                        \n", "repairs                                                          \n", "maintenance                                                      \n", "professional_fees                                          0.0   \n", "management_fees                                                  \n", "leasing_fees                                                     \n", "legal_fees                                                       \n", "accounting_fees                                                  \n", "engineering_fees                                                 \n", "marketing_fees                                                   \n", "consulting_fees                                                  \n", "utilities                                                        \n", "services                                                         \n", "reserves                                                         \n", "total_operating_expenses                                   0.0   \n", "net_operating_income                                   67526.0   \n", "annual_debt_service                                   59229.59   \n", "dscr                                                    1.1401   \n", "cash_flow_before_taxes                                 8296.41   \n", "cash_flow_after_taxes                                  8296.41   \n", "cumulative_cash_flow                                  21776.23   \n", "cap_rate                                                7.1789   \n", "gross_rent_multiplier                                  14.9444   \n", "equity_multiple                                         1.0232   \n", "cash_on_cash_return                                      0.882   \n", "total_acquisition_cost                               940615.38   \n", "aggregated_noi                                        199465.0   \n", "blended_cap_rate                                       21.2058   \n", "portfolio_irr                                                    \n", "created_at                          2025-05-24T17:42:43.568000   \n", "updated_at                          2025-05-24T17:42:43.568000   \n", "metadata                                                    {}   \n", "\n", "                                                             4  \\\n", "id                        3dfa39f0-300c-4784-a389-19e600654952   \n", "prop_id                   cf1857af-bccc-4547-aad3-047cf7c526e4   \n", "year                                                         4   \n", "rental_income                                          63791.0   \n", "long_term_rental                                         63791   \n", "short_term_rental                                                \n", "other_income                                              7925   \n", "vacancy_loss                                             -3190   \n", "credit_loss                                                      \n", "effective_gross_income                                 68526.0   \n", "property_tax                                                     \n", "insurance                                                        \n", "repairs                                                          \n", "maintenance                                                      \n", "professional_fees                                          0.0   \n", "management_fees                                                  \n", "leasing_fees                                                     \n", "legal_fees                                                       \n", "accounting_fees                                                  \n", "engineering_fees                                                 \n", "marketing_fees                                                   \n", "consulting_fees                                                  \n", "utilities                                                        \n", "services                                                         \n", "reserves                                                         \n", "total_operating_expenses                                   0.0   \n", "net_operating_income                                   68526.0   \n", "annual_debt_service                                   59229.59   \n", "dscr                                                     1.157   \n", "cash_flow_before_taxes                                 9296.41   \n", "cash_flow_after_taxes                                  9296.41   \n", "cumulative_cash_flow                                  31072.64   \n", "cap_rate                                                7.2852   \n", "gross_rent_multiplier                                  14.7453   \n", "equity_multiple                                          1.033   \n", "cash_on_cash_return                                     0.9883   \n", "total_acquisition_cost                               940615.38   \n", "aggregated_noi                                        267991.0   \n", "blended_cap_rate                                        28.491   \n", "portfolio_irr                                                    \n", "created_at                          2025-05-24T17:42:43.568000   \n", "updated_at                          2025-05-24T17:42:43.568000   \n", "metadata                                                    {}   \n", "\n", "                                                             5  \n", "id                        e91542e6-6678-485f-8b17-c6d26336704c  \n", "prop_id                   cf1857af-bccc-4547-aad3-047cf7c526e4  \n", "year                                                         5  \n", "rental_income                                          64604.0  \n", "long_term_rental                                         64604  \n", "short_term_rental                                               \n", "other_income                                              8123  \n", "vacancy_loss                                             -3230  \n", "credit_loss                                                     \n", "effective_gross_income                                 69497.0  \n", "property_tax                                                    \n", "insurance                                                       \n", "repairs                                                         \n", "maintenance                                                     \n", "professional_fees                                          0.0  \n", "management_fees                                                 \n", "leasing_fees                                                    \n", "legal_fees                                                      \n", "accounting_fees                                                 \n", "engineering_fees                                                \n", "marketing_fees                                                  \n", "consulting_fees                                                 \n", "utilities                                                       \n", "services                                                        \n", "reserves                                                        \n", "total_operating_expenses                                   0.0  \n", "net_operating_income                                   69497.0  \n", "annual_debt_service                                   59229.59  \n", "dscr                                                    1.1733  \n", "cash_flow_before_taxes                                10267.41  \n", "cash_flow_after_taxes                                 10267.41  \n", "cumulative_cash_flow                                  41340.05  \n", "cap_rate                                                7.3885  \n", "gross_rent_multiplier                                  14.5597  \n", "equity_multiple                                          1.044  \n", "cash_on_cash_return                                     1.0916  \n", "total_acquisition_cost                               940615.38  \n", "aggregated_noi                                        337488.0  \n", "blended_cap_rate                                       35.8795  \n", "portfolio_irr                                                   \n", "created_at                          2025-05-24T17:42:43.568000  \n", "updated_at                          2025-05-24T17:42:43.568000  \n", "metadata                                                    {}  "]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["# Calculate rental income using financial service agent\n", "vacancy_loss  = await financial_service.agent.run(\n", "    f\"Calculate vacancy loss for property {prop_id}\",\n", "    deps=FinancialTaskInput(\n", "        task_type=\"vacancy_loss\",\n", "        prop_id=prop_id,\n", "        year=1,\n", "        portfolio_id=portfolio_id,\n", "        db=PropertyService(),\n", "        return_projections=True,\n", "        params={}\n", "    )\n", ")\n", "\n", "vacancy_loss = vacancy_loss.output.responses[0]\n", "\n", "# Update computed_df rental_income column if the agent returned a valid money_value\n", "if vacancy_loss.money_value:\n", "    print(f\"Setting vacancy_loss for year 1 to {vacancy_loss.money_value}\")\n", "    computed_df.loc['vacancy_loss', 1] = round(vacancy_loss.money_value)\n", "\n", "if vacancy_loss.additional_data:\n", "    for key, value in vacancy_loss.additional_data.items():\n", "        if value.money_value:\n", "            # Set rental_income for years 2, 3, 4, 5 only\n", "            year = value.year\n", "            if year in [2, 3, 4, 5]:\n", "                print(f\"Setting vacancy_loss for year {year} to {value.money_value}\")\n", "                computed_df.loc['vacancy_loss', year] = round(value.money_value)\n", "computed_df = await prop_fin.compute_property_financials(computed_df)\n", "computed_df.head(50)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2025-05-25 22:33:58 | INFO | Generating rental projections for credit loss calculation\n", "2025-05-25 22:33:58 | INFO | Property cf1857af-bccc-4547-aad3-047cf7c526e4: 5 units with rent, 0 units missing rent\n", "2025-05-25 22:33:58 | WARNING | No units have sqft data - cannot calculate rent per sqft\n", "2025-05-25 22:33:58 | INFO | Sqft data not available, using median rent estimation\n", "2025-05-25 22:33:58 | INFO | Calculated median rent: $990.00 from 5 units\n", "2025-05-25 22:33:58 | INFO | Individual rents: ['$875.00', '$950.00', '$990.00', '$1030.00', '$1250.00']\n", "2025-05-25 22:33:58 | INFO | Known rent - Unit 3: $950.00\n", "2025-05-25 22:33:58 | INFO | Known rent - Unit 4: $1250.00\n", "2025-05-25 22:33:58 | INFO | Known rent - Unit 2: $1030.00\n", "2025-05-25 22:33:58 | INFO | Known rent - Unit 1: $875.00\n", "2025-05-25 22:33:58 | INFO | Known rent - Unit 5: $990.00\n", "2025-05-25 22:33:58 | INFO | Total estimated rental income for property cf1857af-bccc-4547-aad3-047cf7c526e4: $5095.00/month\n", "2025-05-25 22:33:58 | INFO | Retrieved historical inflation data for projections\n", "2025-05-25 22:33:58 | INFO | FRED client initialized successfully\n", "2025-05-25 22:33:58 | INFO | Fetching FRED data for series: CUUR0000SEHA\n", "2025-05-25 22:33:59 | INFO | Retrieved 76 data points for series CUUR0000SEHA\n", "2025-05-25 22:33:59 | INFO | FRED-based rental growth rates calculated: {2: 0.015, 3: 0.014249999999999999, 4: 0.0135, 5: 0.01275}\n", "2025-05-25 22:33:59 | INFO | Based on 36 months of recent FRED rental price data\n", "2025-05-25 22:33:59 | INFO | Average historical growth rate: 0.49%\n", "2025-05-25 22:33:59 | INFO | Year 2: $62057.10 (growth: 1.5%)\n", "2025-05-25 22:33:59 | INFO | Year 3: $62941.41 (growth: 1.4%)\n", "2025-05-25 22:33:59 | INFO | Year 4: $63791.12 (growth: 1.4%)\n", "2025-05-25 22:33:59 | INFO | Year 5: $64604.46 (growth: 1.3%)\n", "2025-05-25 22:33:59 | INFO | Searching for credit data: tenant default rate ZIP code 08401 rental eviction 2024\n", "2025-05-25 22:34:00 | INFO | Searching for credit data: credit score average ZIP code 08401 creditworthiness demographics\n", "2025-05-25 22:34:01 | INFO | Searching for credit data: bad debt collection rate ZIP code 08401 rental property\n", "2025-05-25 22:34:03 | INFO | Found credit risk data: {'credit_loss_rate': 0.02, 'source': 'tavily_search_composite', 'description': 'Credit risk analysis for ZIP code 08401', 'default_rates_found': 0, 'credit_scores_found': 0, 'bad_debt_rates_found': 0}\n", "2025-05-25 22:34:03 | INFO | Credit loss calculation: $61140.00 * 2.00% = $-1222.80\n", "2025-05-25 22:34:03 | INFO | Credit Loss Year 1: $-1222.80 loss (2.00% of $61140.00)\n", "2025-05-25 22:34:03 | INFO | Credit loss calculation: $62057.10 * 2.00% = $-1241.14\n", "2025-05-25 22:34:03 | INFO | Credit Loss Year 2: $-1241.14 loss (2.00% of $62057.10)\n", "2025-05-25 22:34:03 | INFO | Credit loss calculation: $62941.41 * 2.00% = $-1258.83\n", "2025-05-25 22:34:03 | INFO | Credit Loss Year 3: $-1258.83 loss (2.00% of $62941.41)\n", "2025-05-25 22:34:03 | INFO | Credit loss calculation: $63791.12 * 2.00% = $-1275.82\n", "2025-05-25 22:34:03 | INFO | Credit Loss Year 4: $-1275.82 loss (2.00% of $63791.12)\n", "2025-05-25 22:34:03 | INFO | Credit loss calculation: $64604.46 * 2.00% = $-1292.09\n", "2025-05-25 22:34:03 | INFO | Credit Loss Year 5: $-1292.09 loss (2.00% of $64604.46)\n", "Setting credit_loss for year 1 to -1222.8\n", "Setting credit_loss for year 2 to -1241.1419999999998\n", "Setting credit_loss for year 3 to -1258.8282735\n", "Setting credit_loss for year 4 to -1275.8224551922501\n", "Setting credit_loss for year 5 to -1292.0891914959514\n", "2025-05-25 22:34:24 | INFO | Retrieved current mortgage rate from FRED: 6.86%\n", "2025-05-25 22:34:24 | WARNING | Year 1: DSCR (1.08) below typical lending requirement (1.25x)\n", "2025-05-25 22:34:24 | WARNING | Year 2: DSCR (1.10) below typical lending requirement (1.25x)\n", "2025-05-25 22:34:24 | WARNING | Year 3: DSCR (1.12) below typical lending requirement (1.25x)\n", "2025-05-25 22:34:24 | WARNING | Year 4: DSCR (1.14) below typical lending requirement (1.25x)\n", "2025-05-25 22:34:24 | WARNING | Year 5: DSCR (1.15) below typical lending requirement (1.25x)\n", "2025-05-25 22:34:24 | WARNING | Could not compute IRR - numpy-financial package may not be available: No module named 'numpy_financial'\n", "2025-05-25 22:34:24 | INFO | Financial computation completed for 5 years\n", "2025-05-25 22:34:24 | INFO | Year 1: NOI=$64,219.00, Expenses=$0.00\n", "2025-05-25 22:34:24 | INFO | Year 2: NOI=$65,256.00, Expenses=$0.00\n", "2025-05-25 22:34:24 | INFO | Year 3: NOI=$66,267.00, Expenses=$0.00\n", "2025-05-25 22:34:24 | INFO | Year 4: NOI=$67,250.00, Expenses=$0.00\n", "2025-05-25 22:34:24 | INFO | Year 5: NOI=$68,205.00, Expenses=$0.00\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>1</th>\n", "      <th>2</th>\n", "      <th>3</th>\n", "      <th>4</th>\n", "      <th>5</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>id</th>\n", "      <td>54d93942-caf6-4132-82a0-762d294039d5</td>\n", "      <td>fa4537fb-2d55-4945-80f3-e90ddb424955</td>\n", "      <td>1a29cbd9-b9a1-4a94-bfcb-868fe2046097</td>\n", "      <td>3dfa39f0-300c-4784-a389-19e600654952</td>\n", "      <td>e91542e6-6678-485f-8b17-c6d26336704c</td>\n", "    </tr>\n", "    <tr>\n", "      <th>prop_id</th>\n", "      <td>cf1857af-bccc-4547-aad3-047cf7c526e4</td>\n", "      <td>cf1857af-bccc-4547-aad3-047cf7c526e4</td>\n", "      <td>cf1857af-bccc-4547-aad3-047cf7c526e4</td>\n", "      <td>cf1857af-bccc-4547-aad3-047cf7c526e4</td>\n", "      <td>cf1857af-bccc-4547-aad3-047cf7c526e4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>year</th>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td>3</td>\n", "      <td>4</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>rental_income</th>\n", "      <td>61140.0</td>\n", "      <td>62057.0</td>\n", "      <td>62941.0</td>\n", "      <td>63791.0</td>\n", "      <td>64604.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>long_term_rental</th>\n", "      <td>61140</td>\n", "      <td>62057</td>\n", "      <td>62941</td>\n", "      <td>63791</td>\n", "      <td>64604</td>\n", "    </tr>\n", "    <tr>\n", "      <th>short_term_rental</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>other_income</th>\n", "      <td>7359</td>\n", "      <td>7543</td>\n", "      <td>7732</td>\n", "      <td>7925</td>\n", "      <td>8123</td>\n", "    </tr>\n", "    <tr>\n", "      <th>vacancy_loss</th>\n", "      <td>-3057</td>\n", "      <td>-3103</td>\n", "      <td>-3147</td>\n", "      <td>-3190</td>\n", "      <td>-3230</td>\n", "    </tr>\n", "    <tr>\n", "      <th>credit_loss</th>\n", "      <td>-1223</td>\n", "      <td>-1241</td>\n", "      <td>-1259</td>\n", "      <td>-1276</td>\n", "      <td>-1292</td>\n", "    </tr>\n", "    <tr>\n", "      <th>effective_gross_income</th>\n", "      <td>64219.0</td>\n", "      <td>65256.0</td>\n", "      <td>66267.0</td>\n", "      <td>67250.0</td>\n", "      <td>68205.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>property_tax</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>insurance</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>repairs</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>maintenance</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>professional_fees</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>management_fees</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>leasing_fees</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>legal_fees</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>accounting_fees</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>engineering_fees</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>marketing_fees</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>consulting_fees</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>utilities</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>services</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>reserves</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>total_operating_expenses</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>net_operating_income</th>\n", "      <td>64219.0</td>\n", "      <td>65256.0</td>\n", "      <td>66267.0</td>\n", "      <td>67250.0</td>\n", "      <td>68205.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>annual_debt_service</th>\n", "      <td>59229.59</td>\n", "      <td>59229.59</td>\n", "      <td>59229.59</td>\n", "      <td>59229.59</td>\n", "      <td>59229.59</td>\n", "    </tr>\n", "    <tr>\n", "      <th>dscr</th>\n", "      <td>1.0842</td>\n", "      <td>1.1017</td>\n", "      <td>1.1188</td>\n", "      <td>1.1354</td>\n", "      <td>1.1515</td>\n", "    </tr>\n", "    <tr>\n", "      <th>cash_flow_before_taxes</th>\n", "      <td>4989.41</td>\n", "      <td>6026.41</td>\n", "      <td>7037.41</td>\n", "      <td>8020.41</td>\n", "      <td>8975.41</td>\n", "    </tr>\n", "    <tr>\n", "      <th>cash_flow_after_taxes</th>\n", "      <td>4989.41</td>\n", "      <td>6026.41</td>\n", "      <td>7037.41</td>\n", "      <td>8020.41</td>\n", "      <td>8975.41</td>\n", "    </tr>\n", "    <tr>\n", "      <th>cumulative_cash_flow</th>\n", "      <td>4989.41</td>\n", "      <td>11015.82</td>\n", "      <td>18053.23</td>\n", "      <td>26073.64</td>\n", "      <td>35049.05</td>\n", "    </tr>\n", "    <tr>\n", "      <th>cap_rate</th>\n", "      <td>6.8273</td>\n", "      <td>6.9376</td>\n", "      <td>7.0451</td>\n", "      <td>7.1496</td>\n", "      <td>7.2511</td>\n", "    </tr>\n", "    <tr>\n", "      <th>gross_rent_multiplier</th>\n", "      <td>15.3846</td>\n", "      <td>15.1573</td>\n", "      <td>14.9444</td>\n", "      <td>14.7453</td>\n", "      <td>14.5597</td>\n", "    </tr>\n", "    <tr>\n", "      <th>equity_multiple</th>\n", "      <td>1.0053</td>\n", "      <td>1.0117</td>\n", "      <td>1.0192</td>\n", "      <td>1.0277</td>\n", "      <td>1.0373</td>\n", "    </tr>\n", "    <tr>\n", "      <th>cash_on_cash_return</th>\n", "      <td>0.5304</td>\n", "      <td>0.6407</td>\n", "      <td>0.7482</td>\n", "      <td>0.8527</td>\n", "      <td>0.9542</td>\n", "    </tr>\n", "    <tr>\n", "      <th>total_acquisition_cost</th>\n", "      <td>940615.38</td>\n", "      <td>940615.38</td>\n", "      <td>940615.38</td>\n", "      <td>940615.38</td>\n", "      <td>940615.38</td>\n", "    </tr>\n", "    <tr>\n", "      <th>aggregated_noi</th>\n", "      <td>64219.0</td>\n", "      <td>129475.0</td>\n", "      <td>195742.0</td>\n", "      <td>262992.0</td>\n", "      <td>331197.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>blended_cap_rate</th>\n", "      <td>6.8273</td>\n", "      <td>13.7649</td>\n", "      <td>20.81</td>\n", "      <td>27.9596</td>\n", "      <td>35.2107</td>\n", "    </tr>\n", "    <tr>\n", "      <th>portfolio_irr</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>created_at</th>\n", "      <td>2025-05-24T17:42:43.568000</td>\n", "      <td>2025-05-24T17:42:43.568000</td>\n", "      <td>2025-05-24T17:42:43.568000</td>\n", "      <td>2025-05-24T17:42:43.568000</td>\n", "      <td>2025-05-24T17:42:43.568000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>updated_at</th>\n", "      <td>2025-05-24T17:42:43.568000</td>\n", "      <td>2025-05-24T17:42:43.568000</td>\n", "      <td>2025-05-24T17:42:43.568000</td>\n", "      <td>2025-05-24T17:42:43.568000</td>\n", "      <td>2025-05-24T17:42:43.568000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>metadata</th>\n", "      <td>{'year': 5, 'repairs': [{'sources': [{'data': ...</td>\n", "      <td>{}</td>\n", "      <td>{}</td>\n", "      <td>{}</td>\n", "      <td>{}</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                                                          1  \\\n", "id                                     54d93942-caf6-4132-82a0-762d294039d5   \n", "prop_id                                cf1857af-bccc-4547-aad3-047cf7c526e4   \n", "year                                                                      1   \n", "rental_income                                                       61140.0   \n", "long_term_rental                                                      61140   \n", "short_term_rental                                                             \n", "other_income                                                           7359   \n", "vacancy_loss                                                          -3057   \n", "credit_loss                                                           -1223   \n", "effective_gross_income                                              64219.0   \n", "property_tax                                                                  \n", "insurance                                                                     \n", "repairs                                                                       \n", "maintenance                                                                   \n", "professional_fees                                                       0.0   \n", "management_fees                                                               \n", "leasing_fees                                                                  \n", "legal_fees                                                                    \n", "accounting_fees                                                               \n", "engineering_fees                                                              \n", "marketing_fees                                                                \n", "consulting_fees                                                               \n", "utilities                                                                     \n", "services                                                                      \n", "reserves                                                                      \n", "total_operating_expenses                                                0.0   \n", "net_operating_income                                                64219.0   \n", "annual_debt_service                                                59229.59   \n", "dscr                                                                 1.0842   \n", "cash_flow_before_taxes                                              4989.41   \n", "cash_flow_after_taxes                                               4989.41   \n", "cumulative_cash_flow                                                4989.41   \n", "cap_rate                                                             6.8273   \n", "gross_rent_multiplier                                               15.3846   \n", "equity_multiple                                                      1.0053   \n", "cash_on_cash_return                                                  0.5304   \n", "total_acquisition_cost                                            940615.38   \n", "aggregated_noi                                                      64219.0   \n", "blended_cap_rate                                                     6.8273   \n", "portfolio_irr                                                                 \n", "created_at                                       2025-05-24T17:42:43.568000   \n", "updated_at                                       2025-05-24T17:42:43.568000   \n", "metadata                  {'year': 5, 'repairs': [{'sources': [{'data': ...   \n", "\n", "                                                             2  \\\n", "id                        fa4537fb-2d55-4945-80f3-e90ddb424955   \n", "prop_id                   cf1857af-bccc-4547-aad3-047cf7c526e4   \n", "year                                                         2   \n", "rental_income                                          62057.0   \n", "long_term_rental                                         62057   \n", "short_term_rental                                                \n", "other_income                                              7543   \n", "vacancy_loss                                             -3103   \n", "credit_loss                                              -1241   \n", "effective_gross_income                                 65256.0   \n", "property_tax                                                     \n", "insurance                                                        \n", "repairs                                                          \n", "maintenance                                                      \n", "professional_fees                                          0.0   \n", "management_fees                                                  \n", "leasing_fees                                                     \n", "legal_fees                                                       \n", "accounting_fees                                                  \n", "engineering_fees                                                 \n", "marketing_fees                                                   \n", "consulting_fees                                                  \n", "utilities                                                        \n", "services                                                         \n", "reserves                                                         \n", "total_operating_expenses                                   0.0   \n", "net_operating_income                                   65256.0   \n", "annual_debt_service                                   59229.59   \n", "dscr                                                    1.1017   \n", "cash_flow_before_taxes                                 6026.41   \n", "cash_flow_after_taxes                                  6026.41   \n", "cumulative_cash_flow                                  11015.82   \n", "cap_rate                                                6.9376   \n", "gross_rent_multiplier                                  15.1573   \n", "equity_multiple                                         1.0117   \n", "cash_on_cash_return                                     0.6407   \n", "total_acquisition_cost                               940615.38   \n", "aggregated_noi                                        129475.0   \n", "blended_cap_rate                                       13.7649   \n", "portfolio_irr                                                    \n", "created_at                          2025-05-24T17:42:43.568000   \n", "updated_at                          2025-05-24T17:42:43.568000   \n", "metadata                                                    {}   \n", "\n", "                                                             3  \\\n", "id                        1a29cbd9-b9a1-4a94-bfcb-868fe2046097   \n", "prop_id                   cf1857af-bccc-4547-aad3-047cf7c526e4   \n", "year                                                         3   \n", "rental_income                                          62941.0   \n", "long_term_rental                                         62941   \n", "short_term_rental                                                \n", "other_income                                              7732   \n", "vacancy_loss                                             -3147   \n", "credit_loss                                              -1259   \n", "effective_gross_income                                 66267.0   \n", "property_tax                                                     \n", "insurance                                                        \n", "repairs                                                          \n", "maintenance                                                      \n", "professional_fees                                          0.0   \n", "management_fees                                                  \n", "leasing_fees                                                     \n", "legal_fees                                                       \n", "accounting_fees                                                  \n", "engineering_fees                                                 \n", "marketing_fees                                                   \n", "consulting_fees                                                  \n", "utilities                                                        \n", "services                                                         \n", "reserves                                                         \n", "total_operating_expenses                                   0.0   \n", "net_operating_income                                   66267.0   \n", "annual_debt_service                                   59229.59   \n", "dscr                                                    1.1188   \n", "cash_flow_before_taxes                                 7037.41   \n", "cash_flow_after_taxes                                  7037.41   \n", "cumulative_cash_flow                                  18053.23   \n", "cap_rate                                                7.0451   \n", "gross_rent_multiplier                                  14.9444   \n", "equity_multiple                                         1.0192   \n", "cash_on_cash_return                                     0.7482   \n", "total_acquisition_cost                               940615.38   \n", "aggregated_noi                                        195742.0   \n", "blended_cap_rate                                         20.81   \n", "portfolio_irr                                                    \n", "created_at                          2025-05-24T17:42:43.568000   \n", "updated_at                          2025-05-24T17:42:43.568000   \n", "metadata                                                    {}   \n", "\n", "                                                             4  \\\n", "id                        3dfa39f0-300c-4784-a389-19e600654952   \n", "prop_id                   cf1857af-bccc-4547-aad3-047cf7c526e4   \n", "year                                                         4   \n", "rental_income                                          63791.0   \n", "long_term_rental                                         63791   \n", "short_term_rental                                                \n", "other_income                                              7925   \n", "vacancy_loss                                             -3190   \n", "credit_loss                                              -1276   \n", "effective_gross_income                                 67250.0   \n", "property_tax                                                     \n", "insurance                                                        \n", "repairs                                                          \n", "maintenance                                                      \n", "professional_fees                                          0.0   \n", "management_fees                                                  \n", "leasing_fees                                                     \n", "legal_fees                                                       \n", "accounting_fees                                                  \n", "engineering_fees                                                 \n", "marketing_fees                                                   \n", "consulting_fees                                                  \n", "utilities                                                        \n", "services                                                         \n", "reserves                                                         \n", "total_operating_expenses                                   0.0   \n", "net_operating_income                                   67250.0   \n", "annual_debt_service                                   59229.59   \n", "dscr                                                    1.1354   \n", "cash_flow_before_taxes                                 8020.41   \n", "cash_flow_after_taxes                                  8020.41   \n", "cumulative_cash_flow                                  26073.64   \n", "cap_rate                                                7.1496   \n", "gross_rent_multiplier                                  14.7453   \n", "equity_multiple                                         1.0277   \n", "cash_on_cash_return                                     0.8527   \n", "total_acquisition_cost                               940615.38   \n", "aggregated_noi                                        262992.0   \n", "blended_cap_rate                                       27.9596   \n", "portfolio_irr                                                    \n", "created_at                          2025-05-24T17:42:43.568000   \n", "updated_at                          2025-05-24T17:42:43.568000   \n", "metadata                                                    {}   \n", "\n", "                                                             5  \n", "id                        e91542e6-6678-485f-8b17-c6d26336704c  \n", "prop_id                   cf1857af-bccc-4547-aad3-047cf7c526e4  \n", "year                                                         5  \n", "rental_income                                          64604.0  \n", "long_term_rental                                         64604  \n", "short_term_rental                                               \n", "other_income                                              8123  \n", "vacancy_loss                                             -3230  \n", "credit_loss                                              -1292  \n", "effective_gross_income                                 68205.0  \n", "property_tax                                                    \n", "insurance                                                       \n", "repairs                                                         \n", "maintenance                                                     \n", "professional_fees                                          0.0  \n", "management_fees                                                 \n", "leasing_fees                                                    \n", "legal_fees                                                      \n", "accounting_fees                                                 \n", "engineering_fees                                                \n", "marketing_fees                                                  \n", "consulting_fees                                                 \n", "utilities                                                       \n", "services                                                        \n", "reserves                                                        \n", "total_operating_expenses                                   0.0  \n", "net_operating_income                                   68205.0  \n", "annual_debt_service                                   59229.59  \n", "dscr                                                    1.1515  \n", "cash_flow_before_taxes                                 8975.41  \n", "cash_flow_after_taxes                                  8975.41  \n", "cumulative_cash_flow                                  35049.05  \n", "cap_rate                                                7.2511  \n", "gross_rent_multiplier                                  14.5597  \n", "equity_multiple                                         1.0373  \n", "cash_on_cash_return                                     0.9542  \n", "total_acquisition_cost                               940615.38  \n", "aggregated_noi                                        331197.0  \n", "blended_cap_rate                                       35.2107  \n", "portfolio_irr                                                   \n", "created_at                          2025-05-24T17:42:43.568000  \n", "updated_at                          2025-05-24T17:42:43.568000  \n", "metadata                                                    {}  "]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["# Calculate rental income using financial service agent\n", "credit_loss  = await financial_service.agent.run(\n", "    f\"Calculate credit loss for property {prop_id}\",\n", "    deps=FinancialTaskInput(\n", "        task_type=\"credit_loss\",\n", "        prop_id=prop_id,\n", "        year=1,\n", "        portfolio_id=portfolio_id,\n", "        db=PropertyService(),\n", "        return_projections=True,\n", "        params={}\n", "    )\n", ")\n", "\n", "credit_loss = credit_loss.output.responses[0]\n", "\n", "# Update computed_df rental_income column if the agent returned a valid money_value\n", "if credit_loss.money_value:\n", "    print(f\"Setting credit_loss for year 1 to {credit_loss.money_value}\")\n", "    computed_df.loc['credit_loss', 1] = round(credit_loss.money_value)\n", "\n", "if credit_loss.additional_data:\n", "    for key, value in credit_loss.additional_data.items():\n", "        if value.money_value:\n", "            # Set rental_income for years 2, 3, 4, 5 only\n", "            year = value.year\n", "            if year in [2, 3, 4, 5]:\n", "                print(f\"Setting credit_loss for year {year} to {value.money_value}\")\n", "                computed_df.loc['credit_loss', year] = round(value.money_value)\n", "computed_df = await prop_fin.compute_property_financials(computed_df)\n", "computed_df.head(50)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2025-05-25 22:34:28 | INFO | Calculating rental income for expense calculations\n", "2025-05-25 22:34:28 | INFO | Property cf1857af-bccc-4547-aad3-047cf7c526e4: 5 units with rent, 0 units missing rent\n", "2025-05-25 22:34:28 | WARNING | No units have sqft data - cannot calculate rent per sqft\n", "2025-05-25 22:34:28 | INFO | Sqft data not available, using median rent estimation\n", "2025-05-25 22:34:28 | INFO | Calculated median rent: $990.00 from 5 units\n", "2025-05-25 22:34:28 | INFO | Individual rents: ['$875.00', '$950.00', '$990.00', '$1030.00', '$1250.00']\n", "2025-05-25 22:34:28 | INFO | Known rent - Unit 3: $950.00\n", "2025-05-25 22:34:28 | INFO | Known rent - Unit 4: $1250.00\n", "2025-05-25 22:34:28 | INFO | Known rent - Unit 2: $1030.00\n", "2025-05-25 22:34:28 | INFO | Known rent - Unit 1: $875.00\n", "2025-05-25 22:34:28 | INFO | Known rent - Unit 5: $990.00\n", "2025-05-25 22:34:28 | INFO | Total estimated rental income for property cf1857af-bccc-4547-aad3-047cf7c526e4: $5095.00/month\n", "2025-05-25 22:34:28 | INFO | Starting comprehensive expense calculation for property cf1857af-bccc-4547-aad3-047cf7c526e4\n", "2025-05-25 22:34:28 | INFO | Searching for cap rates: cap rate capitalization rate ZIP code 08401 real estate investment property 2024\n", "2025-05-25 22:34:29 | WARNING | Could not extract specific cap rate from search results for ZIP code 08401\n", "2025-05-25 22:34:29 | INFO | Found cap rates: {'cap_rate': 0.06, 'source': 'estimated_average'}\n", "2025-05-25 22:34:29 | INFO | Property valuation: $61140.00 / 6.00% = $1019000.00\n", "2025-05-25 22:34:29 | INFO | Property value estimate: $1019000.00\n", "2025-05-25 22:34:30 | INFO | Found 8 tax history records for property cf1857af-bccc-4547-aad3-047cf7c526e4\n", "2025-05-25 22:34:30 | INFO | Tax trend analysis: 46.96% annual change, confidence: high\n", "2025-05-25 22:34:30 | INFO | Using historical tax trend for projections\n", "2025-05-25 22:34:30 | INFO | Property Tax Year 1: $7609.53\n", "2025-05-25 22:34:30 | INFO | Property Tax Year 2: $11182.87\n", "2025-05-25 22:34:30 | INFO | Property Tax Year 3: $16434.21\n", "2025-05-25 22:34:30 | INFO | Property Tax Year 4: $24151.51\n", "2025-05-25 22:34:30 | INFO | Property Tax Year 5: $35492.76\n", "2025-05-25 22:34:30 | INFO | Property tax projections calculated: 5 years\n", "2025-05-25 22:34:30 | INFO | Searching for insurance rates: property insurance cost rate ZIP code 08401 landlord insurance rental property 2024\n", "2025-05-25 22:34:31 | WARNING | Could not extract specific insurance rates from search results for ZIP code 08401\n", "2025-05-25 22:34:31 | INFO | Found insurance rates: {'insurance_rate': 0.007, 'source': 'default_fallback'}\n", "2025-05-25 22:34:31 | INFO | Insurance calculation: $1019000.00 * 0.700% = $7133.00\n", "2025-05-25 22:34:31 | INFO | Insurance Year 1: $7133.00\n", "2025-05-25 22:34:31 | INFO | Insurance Year 2: $7418.32\n", "2025-05-25 22:34:31 | INFO | Insurance Year 3: $7715.05\n", "2025-05-25 22:34:31 | INFO | Insurance Year 4: $8023.65\n", "2025-05-25 22:34:31 | INFO | Insurance Year 5: $8344.60\n", "2025-05-25 22:34:31 | INFO | Insurance projections calculated: 5 years\n", "2025-05-25 22:34:31 | WARNING | No square footage data found for utilities calculation, using fallback estimate\n", "2025-05-25 22:34:31 | INFO | Estimated 2911 sqft from rental income of $61140.00/year\n", "2025-05-25 22:34:33 | INFO | Found utility rate data: {'rates_found': 0, 'average_rate': 0, 'sources': []}\n", "2025-05-25 22:34:33 | INFO | Using default utility rate: $2.00/sqft (no local data found)\n", "2025-05-25 22:34:33 | INFO | Utilities Year 1: $5822.86 ($485.24/month)\n", "2025-05-25 22:34:33 | INFO | Utilities Year 2: $6026.66 ($502.22/month)\n", "2025-05-25 22:34:33 | INFO | Utilities Year 3: $6237.59 ($519.80/month)\n", "2025-05-25 22:34:33 | INFO | Utilities Year 4: $6455.91 ($537.99/month)\n", "2025-05-25 22:34:33 | INFO | Utilities Year 5: $6681.86 ($556.82/month)\n", "2025-05-25 22:34:33 | INFO | Searching for professional fees data: property management fees rates ATLANTIC CITY, NJ 2024\n", "2025-05-25 22:34:35 | INFO | Searching for professional fees data: real estate professional services costs ATLANTIC CITY, NJ 2024\n", "2025-05-25 22:34:37 | INFO | Searching for professional fees data: rental property leasing fees ATLANTIC CITY, NJ 2024\n", "2025-05-25 22:34:39 | INFO | Gathered 9 market data points for professional fees\n", "2025-05-25 22:34:49 | INFO | Professional fees analysis completed: 7 fee types analyzed\n", "2025-05-25 22:34:49 | INFO | management_fees: 8.0% = $4891.20 annually\n", "2025-05-25 22:34:49 | INFO | leasing_fees: 2.0% = $1222.80 annually\n", "2025-05-25 22:34:49 | INFO | legal_fees: 0.5% = $305.70 annually\n", "2025-05-25 22:34:49 | INFO | accounting_fees: 1.0% = $611.40 annually\n", "2025-05-25 22:34:49 | INFO | engineering_fees: 0.3% = $183.40 annually\n", "2025-05-25 22:34:49 | INFO | marketing_fees: 0.5% = $305.70 annually\n", "2025-05-25 22:34:49 | INFO | consulting_fees: 0.2% = $122.30 annually\n", "2025-05-25 22:34:49 | INFO | AI-based professional fees calculated: 7 fee types\n", "2025-05-25 22:34:49 | INFO | Property age determined: 95 years (built in 1930)\n", "2025-05-25 22:34:49 | INFO | Repairs Year 1: $15285.00\n", "2025-05-25 22:34:49 | INFO | Repairs Year 2: $16049.25\n", "2025-05-25 22:34:49 | INFO | Repairs Year 3: $16851.71\n", "2025-05-25 22:34:49 | INFO | Repairs Year 4: $17694.30\n", "2025-05-25 22:34:49 | INFO | Repairs Year 5: $18579.01\n", "2025-05-25 22:34:49 | INFO | Comprehensive repairs analysis completed: 5 projections\n", "2025-05-25 22:34:49 | INFO | Maintenance Year 1: $7642.50\n", "2025-05-25 22:34:49 | INFO | Maintenance Year 2: $7871.78\n", "2025-05-25 22:34:49 | INFO | Maintenance Year 3: $8107.93\n", "2025-05-25 22:34:49 | INFO | Maintenance Year 4: $8351.17\n", "2025-05-25 22:34:49 | INFO | Maintenance Year 5: $8601.70\n", "2025-05-25 22:34:49 | INFO | Services Year 1: $3057.00\n", "2025-05-25 22:34:49 | INFO | Services Year 2: $3179.28\n", "2025-05-25 22:34:49 | INFO | Services Year 3: $3306.45\n", "2025-05-25 22:34:49 | INFO | Services Year 4: $3438.71\n", "2025-05-25 22:34:49 | INFO | Services Year 5: $3576.26\n", "2025-05-25 22:34:49 | INFO | Reserves Year 1: $5095.00\n", "2025-05-25 22:34:49 | INFO | Reserves Year 2: $5196.90\n", "2025-05-25 22:34:49 | INFO | Reserves Year 3: $5300.84\n", "2025-05-25 22:34:49 | INFO | Reserves Year 4: $5406.85\n", "2025-05-25 22:34:49 | INFO | Reserves Year 5: $5514.99\n", "2025-05-25 22:34:49 | INFO | Expense calculation completed for property cf1857af-bccc-4547-aad3-047cf7c526e4: 14 expense items\n", "2025-05-25 22:34:49 | INFO | Returning 14 individual expense responses (return_projections=True)\n", "task_type='property_tax' portfolio_id='90db2602-2ed0-4f45-9de7-145f4052409c' prop_id='cf1857af-bccc-4547-aad3-047cf7c526e4' year=1 sources=[Source(source='property_tax_history', description='Historical tax trend analysis with 46.96% annual change', url='database')] value=46.95878507474937 unit='%' money_value=-7609.************ money_unit='USD' description='Projected property tax for year 1 based on historical trend.' additional_data={'year_2': FinancialResponse(task_type='property_tax', portfolio_id='90db2602-2ed0-4f45-9de7-145f4052409c', prop_id='cf1857af-bccc-4547-aad3-047cf7c526e4', year=2, sources=[Source(source='property_tax_history', description='Historical tax trend analysis with 46.96% annual change', url='database')], value=46.95878507474937, unit='%', money_value=-11182.************, money_unit='USD', description='Projected property tax for year 2 based on historical trend.', additional_data=None), 'year_3': FinancialResponse(task_type='property_tax', portfolio_id='90db2602-2ed0-4f45-9de7-145f4052409c', prop_id='cf1857af-bccc-4547-aad3-047cf7c526e4', year=3, sources=[Source(source='property_tax_history', description='Historical tax trend analysis with 46.96% annual change', url='database')], value=46.95878507474937, unit='%', money_value=-16434.************, money_unit='USD', description='Projected property tax for year 3 based on historical trend.', additional_data=None), 'year_4': FinancialResponse(task_type='property_tax', portfolio_id='90db2602-2ed0-4f45-9de7-145f4052409c', prop_id='cf1857af-bccc-4547-aad3-047cf7c526e4', year=4, sources=[Source(source='property_tax_history', description='Historical tax trend analysis with 46.96% annual change', url='database')], value=46.95878507474937, unit='%', money_value=-24151.508276917706, money_unit='USD', description='Projected property tax for year 4 based on historical trend.', additional_data=None), 'year_5': FinancialResponse(task_type='property_tax', portfolio_id='90db2602-2ed0-4f45-9de7-145f4052409c', prop_id='cf1857af-bccc-4547-aad3-047cf7c526e4', year=5, sources=[Source(source='property_tax_history', description='Historical tax trend analysis with 46.96% annual change', url='database')], value=46.95878507474937, unit='%', money_value=-35492.7631409858, money_unit='USD', description='Projected property tax for year 5 based on historical trend.', additional_data=None)}\n", "task_type='insurance' portfolio_id='90db2602-2ed0-4f45-9de7-145f4052409c' prop_id='cf1857af-bccc-4547-aad3-047cf7c526e4' year=1 sources=[Source(source='insurance_rate_analysis', description='Calculated from local insurance rates and property value', url='default_fallback')] value=0.7000000000000001 unit='%' money_value=-7133.0 money_unit='USD' description='Projected property insurance for year 1 at 0.700% of property value.' additional_data={'year_2': FinancialResponse(task_type='insurance', portfolio_id='90db2602-2ed0-4f45-9de7-145f4052409c', prop_id='cf1857af-bccc-4547-aad3-047cf7c526e4', year=2, sources=[Source(source='insurance_rate_analysis', description='Calculated from local insurance rates and property value', url='default_fallback')], value=0.7000000000000001, unit='%', money_value=-7418.320000000001, money_unit='USD', description='Projected property insurance for year 2 at 0.700% of property value.', additional_data=None), 'year_3': FinancialResponse(task_type='insurance', portfolio_id='90db2602-2ed0-4f45-9de7-145f4052409c', prop_id='cf1857af-bccc-4547-aad3-047cf7c526e4', year=3, sources=[Source(source='insurance_rate_analysis', description='Calculated from local insurance rates and property value', url='default_fallback')], value=0.7000000000000001, unit='%', money_value=-7715.0528, money_unit='USD', description='Projected property insurance for year 3 at 0.700% of property value.', additional_data=None), 'year_4': FinancialResponse(task_type='insurance', portfolio_id='90db2602-2ed0-4f45-9de7-145f4052409c', prop_id='cf1857af-bccc-4547-aad3-047cf7c526e4', year=4, sources=[Source(source='insurance_rate_analysis', description='Calculated from local insurance rates and property value', url='default_fallback')], value=0.7000000000000001, unit='%', money_value=-8023.654912000001, money_unit='USD', description='Projected property insurance for year 4 at 0.700% of property value.', additional_data=None), 'year_5': FinancialResponse(task_type='insurance', portfolio_id='90db2602-2ed0-4f45-9de7-145f4052409c', prop_id='cf1857af-bccc-4547-aad3-047cf7c526e4', year=5, sources=[Source(source='insurance_rate_analysis', description='Calculated from local insurance rates and property value', url='default_fallback')], value=0.7000000000000001, unit='%', money_value=-8344.601108480001, money_unit='USD', description='Projected property insurance for year 5 at 0.700% of property value.', additional_data=None)}\n", "task_type='utilities' portfolio_id='90db2602-2ed0-4f45-9de7-145f4052409c' prop_id='cf1857af-bccc-4547-aad3-047cf7c526e4' year=1 sources=[Source(source='national_average_fallback', description='National average utilities cost at $2.00/sqft annually', url='internal_calculation')] value=2.0 unit='$/sqft' money_value=-5822.857142857143 money_unit='USD' description='Utilities expenses for Year 1: water, sewer, trash, common areas (landlord-paid utilities)' additional_data={'year_2': FinancialResponse(task_type='utilities', portfolio_id='90db2602-2ed0-4f45-9de7-145f4052409c', prop_id='cf1857af-bccc-4547-aad3-047cf7c526e4', year=2, sources=[Source(source='national_average_fallback', description='National average utilities cost at $2.00/sqft annually', url='internal_calculation')], value=2.0, unit='$/sqft', money_value=-6026.657142857142, money_unit='USD', description='Utilities expenses for Year 2: water, sewer, trash, common areas (landlord-paid utilities)', additional_data=None), 'year_3': FinancialResponse(task_type='utilities', portfolio_id='90db2602-2ed0-4f45-9de7-145f4052409c', prop_id='cf1857af-bccc-4547-aad3-047cf7c526e4', year=3, sources=[Source(source='national_average_fallback', description='National average utilities cost at $2.00/sqft annually', url='internal_calculation')], value=2.0, unit='$/sqft', money_value=-6237.590142857142, money_unit='USD', description='Utilities expenses for Year 3: water, sewer, trash, common areas (landlord-paid utilities)', additional_data=None), 'year_4': FinancialResponse(task_type='utilities', portfolio_id='90db2602-2ed0-4f45-9de7-145f4052409c', prop_id='cf1857af-bccc-4547-aad3-047cf7c526e4', year=4, sources=[Source(source='national_average_fallback', description='National average utilities cost at $2.00/sqft annually', url='internal_calculation')], value=2.0, unit='$/sqft', money_value=-6455.905797857142, money_unit='USD', description='Utilities expenses for Year 4: water, sewer, trash, common areas (landlord-paid utilities)', additional_data=None), 'year_5': FinancialResponse(task_type='utilities', portfolio_id='90db2602-2ed0-4f45-9de7-145f4052409c', prop_id='cf1857af-bccc-4547-aad3-047cf7c526e4', year=5, sources=[Source(source='national_average_fallback', description='National average utilities cost at $2.00/sqft annually', url='internal_calculation')], value=2.0, unit='$/sqft', money_value=-6681.862500782141, money_unit='USD', description='Utilities expenses for Year 5: water, sewer, trash, common areas (landlord-paid utilities)', additional_data=None)}\n", "task_type='management_fees' portfolio_id='90db2602-2ed0-4f45-9de7-145f4052409c' prop_id='cf1857af-bccc-4547-aad3-047cf7c526e4' year=1 sources=[Source(source='ai_market_analysis', description=\"AI analysis: Based on local market ranges (6-10%) and the property type's potentially moderate complexity.\", url='ai_analysis_with_market_research')] value=8.0 unit='%' money_value=-4891.2 money_unit='USD' description='Covers day-to-day operations, tenant relations, and maintenance coordination.' additional_data={'year_2': FinancialResponse(task_type='management_fees', portfolio_id='90db2602-2ed0-4f45-9de7-145f4052409c', prop_id='cf1857af-bccc-4547-aad3-047cf7c526e4', year=2, sources=[Source(source='ai_market_analysis', description=\"AI analysis: Based on local market ranges (6-10%) and the property type's potentially moderate complexity.\", url='ai_analysis_with_market_research')], value=8.0, unit='%', money_value=-4989.024, money_unit='USD', description='Covers day-to-day operations, tenant relations, and maintenance coordination.', additional_data=None), 'year_3': FinancialResponse(task_type='management_fees', portfolio_id='90db2602-2ed0-4f45-9de7-145f4052409c', prop_id='cf1857af-bccc-4547-aad3-047cf7c526e4', year=3, sources=[Source(source='ai_market_analysis', description=\"AI analysis: Based on local market ranges (6-10%) and the property type's potentially moderate complexity.\", url='ai_analysis_with_market_research')], value=8.0, unit='%', money_value=-5088.80448, money_unit='USD', description='Covers day-to-day operations, tenant relations, and maintenance coordination.', additional_data=None), 'year_4': FinancialResponse(task_type='management_fees', portfolio_id='90db2602-2ed0-4f45-9de7-145f4052409c', prop_id='cf1857af-bccc-4547-aad3-047cf7c526e4', year=4, sources=[Source(source='ai_market_analysis', description=\"AI analysis: Based on local market ranges (6-10%) and the property type's potentially moderate complexity.\", url='ai_analysis_with_market_research')], value=8.0, unit='%', money_value=-5190.580569600001, money_unit='USD', description='Covers day-to-day operations, tenant relations, and maintenance coordination.', additional_data=None), 'year_5': FinancialResponse(task_type='management_fees', portfolio_id='90db2602-2ed0-4f45-9de7-145f4052409c', prop_id='cf1857af-bccc-4547-aad3-047cf7c526e4', year=5, sources=[Source(source='ai_market_analysis', description=\"AI analysis: Based on local market ranges (6-10%) and the property type's potentially moderate complexity.\", url='ai_analysis_with_market_research')], value=8.0, unit='%', money_value=-5294.392180991999, money_unit='USD', description='Covers day-to-day operations, tenant relations, and maintenance coordination.', additional_data=None)}\n", "task_type='leasing_fees' portfolio_id='90db2602-2ed0-4f45-9de7-145f4052409c' prop_id='cf1857af-bccc-4547-aad3-047cf7c526e4' year=1 sources=[Source(source='ai_market_analysis', description='AI analysis: Set at the upper average rate of 1-3% annually, suitable for budget estimation.', url='ai_analysis_with_market_research')] value=2.0 unit='%' money_value=-1222.8 money_unit='USD' description='Finding and placing tenants, lease preparation, and screening.' additional_data={'year_2': FinancialResponse(task_type='leasing_fees', portfolio_id='90db2602-2ed0-4f45-9de7-145f4052409c', prop_id='cf1857af-bccc-4547-aad3-047cf7c526e4', year=2, sources=[Source(source='ai_market_analysis', description='AI analysis: Set at the upper average rate of 1-3% annually, suitable for budget estimation.', url='ai_analysis_with_market_research')], value=2.0, unit='%', money_value=-1247.256, money_unit='USD', description='Finding and placing tenants, lease preparation, and screening.', additional_data=None), 'year_3': FinancialResponse(task_type='leasing_fees', portfolio_id='90db2602-2ed0-4f45-9de7-145f4052409c', prop_id='cf1857af-bccc-4547-aad3-047cf7c526e4', year=3, sources=[Source(source='ai_market_analysis', description='AI analysis: Set at the upper average rate of 1-3% annually, suitable for budget estimation.', url='ai_analysis_with_market_research')], value=2.0, unit='%', money_value=-1272.20112, money_unit='USD', description='Finding and placing tenants, lease preparation, and screening.', additional_data=None), 'year_4': FinancialResponse(task_type='leasing_fees', portfolio_id='90db2602-2ed0-4f45-9de7-145f4052409c', prop_id='cf1857af-bccc-4547-aad3-047cf7c526e4', year=4, sources=[Source(source='ai_market_analysis', description='AI analysis: Set at the upper average rate of 1-3% annually, suitable for budget estimation.', url='ai_analysis_with_market_research')], value=2.0, unit='%', money_value=-1297.6451424000002, money_unit='USD', description='Finding and placing tenants, lease preparation, and screening.', additional_data=None), 'year_5': FinancialResponse(task_type='leasing_fees', portfolio_id='90db2602-2ed0-4f45-9de7-145f4052409c', prop_id='cf1857af-bccc-4547-aad3-047cf7c526e4', year=5, sources=[Source(source='ai_market_analysis', description='AI analysis: Set at the upper average rate of 1-3% annually, suitable for budget estimation.', url='ai_analysis_with_market_research')], value=2.0, unit='%', money_value=-1323.*************, money_unit='USD', description='Finding and placing tenants, lease preparation, and screening.', additional_data=None)}\n"]}], "source": ["# Calculate rental income using financial service agent\n", "overall_expenses  = await financial_service.agent.run(\n", "    f\"Calculate overall expenses for property {prop_id}, which are: property_tax, insurance, repairs, maintenance, management_fees, leasing_fees, legal_fees, accounting_fees, engineering_fees, marketing_fees, consulting_fees, utilities, services, reserves, services, reserves\",\n", "    deps=FinancialTaskInput(\n", "        task_type=\"overall_expenses\",\n", "        prop_id=prop_id,\n", "        year=1,\n", "        portfolio_id=portfolio_id,\n", "        db=PropertyService(),\n", "        return_projections=True,\n", "        params={}\n", "    )\n", ")\n", "\n", "overall_expenses = overall_expenses.output.responses\n", "\n", "for expense in overall_expenses:\n", "    print(expense)\n", "\n"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Setting property_tax for year 1 to -7609.************\n", "Setting property_tax for year 2 to -11182.************\n", "[Source(source='property_tax_history', description='Historical tax trend analysis with 46.96% annual change', url='database')]\n", "Setting property_tax for year 3 to -16434.************\n", "[Source(source='property_tax_history', description='Historical tax trend analysis with 46.96% annual change', url='database')]\n", "Setting property_tax for year 4 to -24151.508276917706\n", "[Source(source='property_tax_history', description='Historical tax trend analysis with 46.96% annual change', url='database')]\n", "Setting property_tax for year 5 to -35492.7631409858\n", "[Source(source='property_tax_history', description='Historical tax trend analysis with 46.96% annual change', url='database')]\n", "Setting insurance for year 1 to -7133.0\n", "Setting insurance for year 2 to -7418.320000000001\n", "[Source(source='insurance_rate_analysis', description='Calculated from local insurance rates and property value', url='default_fallback')]\n", "Setting insurance for year 3 to -7715.0528\n", "[Source(source='insurance_rate_analysis', description='Calculated from local insurance rates and property value', url='default_fallback')]\n", "Setting insurance for year 4 to -8023.654912000001\n", "[Source(source='insurance_rate_analysis', description='Calculated from local insurance rates and property value', url='default_fallback')]\n", "Setting insurance for year 5 to -8344.601108480001\n", "[Source(source='insurance_rate_analysis', description='Calculated from local insurance rates and property value', url='default_fallback')]\n", "Setting utilities for year 1 to -5822.857142857143\n", "Setting utilities for year 2 to -6026.657142857142\n", "[Source(source='national_average_fallback', description='National average utilities cost at $2.00/sqft annually', url='internal_calculation')]\n", "Setting utilities for year 3 to -6237.590142857142\n", "[Source(source='national_average_fallback', description='National average utilities cost at $2.00/sqft annually', url='internal_calculation')]\n", "Setting utilities for year 4 to -6455.905797857142\n", "[Source(source='national_average_fallback', description='National average utilities cost at $2.00/sqft annually', url='internal_calculation')]\n", "Setting utilities for year 5 to -6681.862500782141\n", "[Source(source='national_average_fallback', description='National average utilities cost at $2.00/sqft annually', url='internal_calculation')]\n", "Setting management_fees for year 1 to -4891.2\n", "Setting management_fees for year 2 to -4989.024\n", "[Source(source='ai_market_analysis', description=\"AI analysis: Based on local market ranges (6-10%) and the property type's potentially moderate complexity.\", url='ai_analysis_with_market_research')]\n", "Setting management_fees for year 3 to -5088.80448\n", "[Source(source='ai_market_analysis', description=\"AI analysis: Based on local market ranges (6-10%) and the property type's potentially moderate complexity.\", url='ai_analysis_with_market_research')]\n", "Setting management_fees for year 4 to -5190.580569600001\n", "[Source(source='ai_market_analysis', description=\"AI analysis: Based on local market ranges (6-10%) and the property type's potentially moderate complexity.\", url='ai_analysis_with_market_research')]\n", "Setting management_fees for year 5 to -5294.392180991999\n", "[Source(source='ai_market_analysis', description=\"AI analysis: Based on local market ranges (6-10%) and the property type's potentially moderate complexity.\", url='ai_analysis_with_market_research')]\n", "Setting leasing_fees for year 1 to -1222.8\n", "Setting leasing_fees for year 2 to -1247.256\n", "[Source(source='ai_market_analysis', description='AI analysis: Set at the upper average rate of 1-3% annually, suitable for budget estimation.', url='ai_analysis_with_market_research')]\n", "Setting leasing_fees for year 3 to -1272.20112\n", "[Source(source='ai_market_analysis', description='AI analysis: Set at the upper average rate of 1-3% annually, suitable for budget estimation.', url='ai_analysis_with_market_research')]\n", "Setting leasing_fees for year 4 to -1297.6451424000002\n", "[Source(source='ai_market_analysis', description='AI analysis: Set at the upper average rate of 1-3% annually, suitable for budget estimation.', url='ai_analysis_with_market_research')]\n", "Setting leasing_fees for year 5 to -1323.*************\n", "[Source(source='ai_market_analysis', description='AI analysis: Set at the upper average rate of 1-3% annually, suitable for budget estimation.', url='ai_analysis_with_market_research')]\n", "2025-05-25 22:35:29 | INFO | Retrieved current mortgage rate from FRED: 6.86%\n", "2025-05-25 22:35:29 | WARNING | Year 1: DSCR (0.63) below typical lending requirement (1.25x)\n", "2025-05-25 22:35:29 | INFO | Year 2: Capped property tax increase at 10%: $-8371.00 (was $-11183.00)\n", "2025-05-25 22:35:29 | WARNING | Year 2: DSCR (0.63) below typical lending requirement (1.25x)\n", "2025-05-25 22:35:29 | INFO | Year 3: Capped property tax increase at 10%: $-9208.10 (was $-16434.00)\n", "2025-05-25 22:35:29 | WARNING | Year 3: DSCR (0.62) below typical lending requirement (1.25x)\n", "2025-05-25 22:35:29 | INFO | Year 4: Capped property tax increase at 10%: $-10128.91 (was $-24152.00)\n", "2025-05-25 22:35:29 | WARNING | Year 4: DSCR (0.61) below typical lending requirement (1.25x)\n", "2025-05-25 22:35:29 | INFO | Year 5: Capped property tax increase at 10%: $-11141.80 (was $-35493.00)\n", "2025-05-25 22:35:29 | WARNING | Year 5: DSCR (0.60) below typical lending requirement (1.25x)\n", "2025-05-25 22:35:29 | WARNING | Could not compute IRR - numpy-financial package may not be available: No module named 'numpy_financial'\n", "2025-05-25 22:35:29 | INFO | Financial computation completed for 5 years\n", "2025-05-25 22:35:29 | INFO | Year 1: NOI=$37,539.00, Expenses=$-26,680.00\n", "2025-05-25 22:35:29 | INFO | Year 2: NOI=$37,204.00, Expenses=$-28,052.00\n", "2025-05-25 22:35:29 | INFO | Year 3: NOI=$36,744.90, Expenses=$-29,522.10\n", "2025-05-25 22:35:29 | INFO | Year 4: NOI=$36,152.09, Expenses=$-31,097.91\n", "2025-05-25 22:35:29 | INFO | Year 5: NOI=$35,418.20, Expenses=$-32,786.80\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>1</th>\n", "      <th>2</th>\n", "      <th>3</th>\n", "      <th>4</th>\n", "      <th>5</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>id</th>\n", "      <td>54d93942-caf6-4132-82a0-762d294039d5</td>\n", "      <td>fa4537fb-2d55-4945-80f3-e90ddb424955</td>\n", "      <td>1a29cbd9-b9a1-4a94-bfcb-868fe2046097</td>\n", "      <td>3dfa39f0-300c-4784-a389-19e600654952</td>\n", "      <td>e91542e6-6678-485f-8b17-c6d26336704c</td>\n", "    </tr>\n", "    <tr>\n", "      <th>prop_id</th>\n", "      <td>cf1857af-bccc-4547-aad3-047cf7c526e4</td>\n", "      <td>cf1857af-bccc-4547-aad3-047cf7c526e4</td>\n", "      <td>cf1857af-bccc-4547-aad3-047cf7c526e4</td>\n", "      <td>cf1857af-bccc-4547-aad3-047cf7c526e4</td>\n", "      <td>cf1857af-bccc-4547-aad3-047cf7c526e4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>year</th>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td>3</td>\n", "      <td>4</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>rental_income</th>\n", "      <td>61140.0</td>\n", "      <td>62057.0</td>\n", "      <td>62941.0</td>\n", "      <td>63791.0</td>\n", "      <td>64604.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>long_term_rental</th>\n", "      <td>61140</td>\n", "      <td>62057</td>\n", "      <td>62941</td>\n", "      <td>63791</td>\n", "      <td>64604</td>\n", "    </tr>\n", "    <tr>\n", "      <th>short_term_rental</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>other_income</th>\n", "      <td>7359</td>\n", "      <td>7543</td>\n", "      <td>7732</td>\n", "      <td>7925</td>\n", "      <td>8123</td>\n", "    </tr>\n", "    <tr>\n", "      <th>vacancy_loss</th>\n", "      <td>-3057</td>\n", "      <td>-3103</td>\n", "      <td>-3147</td>\n", "      <td>-3190</td>\n", "      <td>-3230</td>\n", "    </tr>\n", "    <tr>\n", "      <th>credit_loss</th>\n", "      <td>-1223</td>\n", "      <td>-1241</td>\n", "      <td>-1259</td>\n", "      <td>-1276</td>\n", "      <td>-1292</td>\n", "    </tr>\n", "    <tr>\n", "      <th>effective_gross_income</th>\n", "      <td>64219.0</td>\n", "      <td>65256.0</td>\n", "      <td>66267.0</td>\n", "      <td>67250.0</td>\n", "      <td>68205.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>property_tax</th>\n", "      <td>-7610</td>\n", "      <td>-8371.0</td>\n", "      <td>-9208.1</td>\n", "      <td>-10128.91</td>\n", "      <td>-11141.8</td>\n", "    </tr>\n", "    <tr>\n", "      <th>insurance</th>\n", "      <td>-7133</td>\n", "      <td>-7418</td>\n", "      <td>-7715</td>\n", "      <td>-8024</td>\n", "      <td>-8345</td>\n", "    </tr>\n", "    <tr>\n", "      <th>repairs</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>maintenance</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>professional_fees</th>\n", "      <td>-6114.0</td>\n", "      <td>-6236.0</td>\n", "      <td>-6361.0</td>\n", "      <td>-6489.0</td>\n", "      <td>-6618.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>management_fees</th>\n", "      <td>-4891</td>\n", "      <td>-4989</td>\n", "      <td>-5089</td>\n", "      <td>-5191</td>\n", "      <td>-5294</td>\n", "    </tr>\n", "    <tr>\n", "      <th>leasing_fees</th>\n", "      <td>-1223</td>\n", "      <td>-1247</td>\n", "      <td>-1272</td>\n", "      <td>-1298</td>\n", "      <td>-1324</td>\n", "    </tr>\n", "    <tr>\n", "      <th>legal_fees</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>accounting_fees</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>engineering_fees</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>marketing_fees</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>consulting_fees</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>utilities</th>\n", "      <td>-5823</td>\n", "      <td>-6027</td>\n", "      <td>-6238</td>\n", "      <td>-6456</td>\n", "      <td>-6682</td>\n", "    </tr>\n", "    <tr>\n", "      <th>services</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>reserves</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>total_operating_expenses</th>\n", "      <td>-26680.0</td>\n", "      <td>-28052.0</td>\n", "      <td>-29522.1</td>\n", "      <td>-31097.91</td>\n", "      <td>-32786.8</td>\n", "    </tr>\n", "    <tr>\n", "      <th>net_operating_income</th>\n", "      <td>37539.0</td>\n", "      <td>37204.0</td>\n", "      <td>36744.9</td>\n", "      <td>36152.09</td>\n", "      <td>35418.2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>annual_debt_service</th>\n", "      <td>59229.59</td>\n", "      <td>59229.59</td>\n", "      <td>59229.59</td>\n", "      <td>59229.59</td>\n", "      <td>59229.59</td>\n", "    </tr>\n", "    <tr>\n", "      <th>dscr</th>\n", "      <td>0.6338</td>\n", "      <td>0.6281</td>\n", "      <td>0.6204</td>\n", "      <td>0.6104</td>\n", "      <td>0.598</td>\n", "    </tr>\n", "    <tr>\n", "      <th>cash_flow_before_taxes</th>\n", "      <td>-21690.59</td>\n", "      <td>-22025.59</td>\n", "      <td>-22484.69</td>\n", "      <td>-23077.5</td>\n", "      <td>-23811.39</td>\n", "    </tr>\n", "    <tr>\n", "      <th>cash_flow_after_taxes</th>\n", "      <td>-21690.59</td>\n", "      <td>-22025.59</td>\n", "      <td>-22484.69</td>\n", "      <td>-23077.5</td>\n", "      <td>-23811.39</td>\n", "    </tr>\n", "    <tr>\n", "      <th>cumulative_cash_flow</th>\n", "      <td>-21690.59</td>\n", "      <td>-43716.18</td>\n", "      <td>-66200.87</td>\n", "      <td>-89278.37</td>\n", "      <td>-113089.76</td>\n", "    </tr>\n", "    <tr>\n", "      <th>cap_rate</th>\n", "      <td>3.9909</td>\n", "      <td>3.9553</td>\n", "      <td>3.9065</td>\n", "      <td>3.8435</td>\n", "      <td>3.7654</td>\n", "    </tr>\n", "    <tr>\n", "      <th>gross_rent_multiplier</th>\n", "      <td>15.3846</td>\n", "      <td>15.1573</td>\n", "      <td>14.9444</td>\n", "      <td>14.7453</td>\n", "      <td>14.5597</td>\n", "    </tr>\n", "    <tr>\n", "      <th>equity_multiple</th>\n", "      <td>0.9769</td>\n", "      <td>0.9535</td>\n", "      <td>0.9296</td>\n", "      <td>0.9051</td>\n", "      <td>0.8798</td>\n", "    </tr>\n", "    <tr>\n", "      <th>cash_on_cash_return</th>\n", "      <td>-2.306</td>\n", "      <td>-2.3416</td>\n", "      <td>-2.3904</td>\n", "      <td>-2.4534</td>\n", "      <td>-2.5315</td>\n", "    </tr>\n", "    <tr>\n", "      <th>total_acquisition_cost</th>\n", "      <td>940615.38</td>\n", "      <td>940615.38</td>\n", "      <td>940615.38</td>\n", "      <td>940615.38</td>\n", "      <td>940615.38</td>\n", "    </tr>\n", "    <tr>\n", "      <th>aggregated_noi</th>\n", "      <td>37539.0</td>\n", "      <td>74743.0</td>\n", "      <td>111487.9</td>\n", "      <td>147639.99</td>\n", "      <td>183058.19</td>\n", "    </tr>\n", "    <tr>\n", "      <th>blended_cap_rate</th>\n", "      <td>3.9909</td>\n", "      <td>7.9462</td>\n", "      <td>11.8527</td>\n", "      <td>15.6961</td>\n", "      <td>19.4615</td>\n", "    </tr>\n", "    <tr>\n", "      <th>portfolio_irr</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>created_at</th>\n", "      <td>2025-05-24T17:42:43.568000</td>\n", "      <td>2025-05-24T17:42:43.568000</td>\n", "      <td>2025-05-24T17:42:43.568000</td>\n", "      <td>2025-05-24T17:42:43.568000</td>\n", "      <td>2025-05-24T17:42:43.568000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>updated_at</th>\n", "      <td>2025-05-24T17:42:43.568000</td>\n", "      <td>2025-05-24T17:42:43.568000</td>\n", "      <td>2025-05-24T17:42:43.568000</td>\n", "      <td>2025-05-24T17:42:43.568000</td>\n", "      <td>2025-05-24T17:42:43.568000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>metadata</th>\n", "      <td>{'year': 5, 'repairs': [{'sources': [{'data': ...</td>\n", "      <td>{}</td>\n", "      <td>{}</td>\n", "      <td>{}</td>\n", "      <td>{}</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                                                          1  \\\n", "id                                     54d93942-caf6-4132-82a0-762d294039d5   \n", "prop_id                                cf1857af-bccc-4547-aad3-047cf7c526e4   \n", "year                                                                      1   \n", "rental_income                                                       61140.0   \n", "long_term_rental                                                      61140   \n", "short_term_rental                                                             \n", "other_income                                                           7359   \n", "vacancy_loss                                                          -3057   \n", "credit_loss                                                           -1223   \n", "effective_gross_income                                              64219.0   \n", "property_tax                                                          -7610   \n", "insurance                                                             -7133   \n", "repairs                                                                       \n", "maintenance                                                                   \n", "professional_fees                                                   -6114.0   \n", "management_fees                                                       -4891   \n", "leasing_fees                                                          -1223   \n", "legal_fees                                                                    \n", "accounting_fees                                                               \n", "engineering_fees                                                              \n", "marketing_fees                                                                \n", "consulting_fees                                                               \n", "utilities                                                             -5823   \n", "services                                                                      \n", "reserves                                                                      \n", "total_operating_expenses                                           -26680.0   \n", "net_operating_income                                                37539.0   \n", "annual_debt_service                                                59229.59   \n", "dscr                                                                 0.6338   \n", "cash_flow_before_taxes                                            -21690.59   \n", "cash_flow_after_taxes                                             -21690.59   \n", "cumulative_cash_flow                                              -21690.59   \n", "cap_rate                                                             3.9909   \n", "gross_rent_multiplier                                               15.3846   \n", "equity_multiple                                                      0.9769   \n", "cash_on_cash_return                                                  -2.306   \n", "total_acquisition_cost                                            940615.38   \n", "aggregated_noi                                                      37539.0   \n", "blended_cap_rate                                                     3.9909   \n", "portfolio_irr                                                                 \n", "created_at                                       2025-05-24T17:42:43.568000   \n", "updated_at                                       2025-05-24T17:42:43.568000   \n", "metadata                  {'year': 5, 'repairs': [{'sources': [{'data': ...   \n", "\n", "                                                             2  \\\n", "id                        fa4537fb-2d55-4945-80f3-e90ddb424955   \n", "prop_id                   cf1857af-bccc-4547-aad3-047cf7c526e4   \n", "year                                                         2   \n", "rental_income                                          62057.0   \n", "long_term_rental                                         62057   \n", "short_term_rental                                                \n", "other_income                                              7543   \n", "vacancy_loss                                             -3103   \n", "credit_loss                                              -1241   \n", "effective_gross_income                                 65256.0   \n", "property_tax                                           -8371.0   \n", "insurance                                                -7418   \n", "repairs                                                          \n", "maintenance                                                      \n", "professional_fees                                      -6236.0   \n", "management_fees                                          -4989   \n", "leasing_fees                                             -1247   \n", "legal_fees                                                       \n", "accounting_fees                                                  \n", "engineering_fees                                                 \n", "marketing_fees                                                   \n", "consulting_fees                                                  \n", "utilities                                                -6027   \n", "services                                                         \n", "reserves                                                         \n", "total_operating_expenses                              -28052.0   \n", "net_operating_income                                   37204.0   \n", "annual_debt_service                                   59229.59   \n", "dscr                                                    0.6281   \n", "cash_flow_before_taxes                               -22025.59   \n", "cash_flow_after_taxes                                -22025.59   \n", "cumulative_cash_flow                                 -43716.18   \n", "cap_rate                                                3.9553   \n", "gross_rent_multiplier                                  15.1573   \n", "equity_multiple                                         0.9535   \n", "cash_on_cash_return                                    -2.3416   \n", "total_acquisition_cost                               940615.38   \n", "aggregated_noi                                         74743.0   \n", "blended_cap_rate                                        7.9462   \n", "portfolio_irr                                                    \n", "created_at                          2025-05-24T17:42:43.568000   \n", "updated_at                          2025-05-24T17:42:43.568000   \n", "metadata                                                    {}   \n", "\n", "                                                             3  \\\n", "id                        1a29cbd9-b9a1-4a94-bfcb-868fe2046097   \n", "prop_id                   cf1857af-bccc-4547-aad3-047cf7c526e4   \n", "year                                                         3   \n", "rental_income                                          62941.0   \n", "long_term_rental                                         62941   \n", "short_term_rental                                                \n", "other_income                                              7732   \n", "vacancy_loss                                             -3147   \n", "credit_loss                                              -1259   \n", "effective_gross_income                                 66267.0   \n", "property_tax                                           -9208.1   \n", "insurance                                                -7715   \n", "repairs                                                          \n", "maintenance                                                      \n", "professional_fees                                      -6361.0   \n", "management_fees                                          -5089   \n", "leasing_fees                                             -1272   \n", "legal_fees                                                       \n", "accounting_fees                                                  \n", "engineering_fees                                                 \n", "marketing_fees                                                   \n", "consulting_fees                                                  \n", "utilities                                                -6238   \n", "services                                                         \n", "reserves                                                         \n", "total_operating_expenses                              -29522.1   \n", "net_operating_income                                   36744.9   \n", "annual_debt_service                                   59229.59   \n", "dscr                                                    0.6204   \n", "cash_flow_before_taxes                               -22484.69   \n", "cash_flow_after_taxes                                -22484.69   \n", "cumulative_cash_flow                                 -66200.87   \n", "cap_rate                                                3.9065   \n", "gross_rent_multiplier                                  14.9444   \n", "equity_multiple                                         0.9296   \n", "cash_on_cash_return                                    -2.3904   \n", "total_acquisition_cost                               940615.38   \n", "aggregated_noi                                        111487.9   \n", "blended_cap_rate                                       11.8527   \n", "portfolio_irr                                                    \n", "created_at                          2025-05-24T17:42:43.568000   \n", "updated_at                          2025-05-24T17:42:43.568000   \n", "metadata                                                    {}   \n", "\n", "                                                             4  \\\n", "id                        3dfa39f0-300c-4784-a389-19e600654952   \n", "prop_id                   cf1857af-bccc-4547-aad3-047cf7c526e4   \n", "year                                                         4   \n", "rental_income                                          63791.0   \n", "long_term_rental                                         63791   \n", "short_term_rental                                                \n", "other_income                                              7925   \n", "vacancy_loss                                             -3190   \n", "credit_loss                                              -1276   \n", "effective_gross_income                                 67250.0   \n", "property_tax                                         -10128.91   \n", "insurance                                                -8024   \n", "repairs                                                          \n", "maintenance                                                      \n", "professional_fees                                      -6489.0   \n", "management_fees                                          -5191   \n", "leasing_fees                                             -1298   \n", "legal_fees                                                       \n", "accounting_fees                                                  \n", "engineering_fees                                                 \n", "marketing_fees                                                   \n", "consulting_fees                                                  \n", "utilities                                                -6456   \n", "services                                                         \n", "reserves                                                         \n", "total_operating_expenses                             -31097.91   \n", "net_operating_income                                  36152.09   \n", "annual_debt_service                                   59229.59   \n", "dscr                                                    0.6104   \n", "cash_flow_before_taxes                                -23077.5   \n", "cash_flow_after_taxes                                 -23077.5   \n", "cumulative_cash_flow                                 -89278.37   \n", "cap_rate                                                3.8435   \n", "gross_rent_multiplier                                  14.7453   \n", "equity_multiple                                         0.9051   \n", "cash_on_cash_return                                    -2.4534   \n", "total_acquisition_cost                               940615.38   \n", "aggregated_noi                                       147639.99   \n", "blended_cap_rate                                       15.6961   \n", "portfolio_irr                                                    \n", "created_at                          2025-05-24T17:42:43.568000   \n", "updated_at                          2025-05-24T17:42:43.568000   \n", "metadata                                                    {}   \n", "\n", "                                                             5  \n", "id                        e91542e6-6678-485f-8b17-c6d26336704c  \n", "prop_id                   cf1857af-bccc-4547-aad3-047cf7c526e4  \n", "year                                                         5  \n", "rental_income                                          64604.0  \n", "long_term_rental                                         64604  \n", "short_term_rental                                               \n", "other_income                                              8123  \n", "vacancy_loss                                             -3230  \n", "credit_loss                                              -1292  \n", "effective_gross_income                                 68205.0  \n", "property_tax                                          -11141.8  \n", "insurance                                                -8345  \n", "repairs                                                         \n", "maintenance                                                     \n", "professional_fees                                      -6618.0  \n", "management_fees                                          -5294  \n", "leasing_fees                                             -1324  \n", "legal_fees                                                      \n", "accounting_fees                                                 \n", "engineering_fees                                                \n", "marketing_fees                                                  \n", "consulting_fees                                                 \n", "utilities                                                -6682  \n", "services                                                        \n", "reserves                                                        \n", "total_operating_expenses                              -32786.8  \n", "net_operating_income                                   35418.2  \n", "annual_debt_service                                   59229.59  \n", "dscr                                                     0.598  \n", "cash_flow_before_taxes                               -23811.39  \n", "cash_flow_after_taxes                                -23811.39  \n", "cumulative_cash_flow                                -113089.76  \n", "cap_rate                                                3.7654  \n", "gross_rent_multiplier                                  14.5597  \n", "equity_multiple                                         0.8798  \n", "cash_on_cash_return                                    -2.5315  \n", "total_acquisition_cost                               940615.38  \n", "aggregated_noi                                       183058.19  \n", "blended_cap_rate                                       19.4615  \n", "portfolio_irr                                                   \n", "created_at                          2025-05-24T17:42:43.568000  \n", "updated_at                          2025-05-24T17:42:43.568000  \n", "metadata                                                    {}  "]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["# Update computed_df rental_income column if the agent returned a valid money_value\n", "for expense in overall_expenses:\n", "    if expense.money_value:\n", "        print(f\"Setting {expense.task_type} for year 1 to {expense.money_value}\")\n", "        computed_df.loc[expense.task_type, 1] = round(expense.money_value)\n", "\n", "    if expense.additional_data:\n", "        for key, value in expense.additional_data.items():\n", "            if value.money_value:\n", "                # Set rental_income for years 2, 3, 4, 5 only\n", "                year = value.year\n", "                if year in [2, 3, 4, 5]:\n", "                    print(f\"Setting {expense.task_type} for year {year} to {value.money_value}\")\n", "                    computed_df.loc[expense.task_type, year] = round(value.money_value)\n", "                    print(value.sources)\n", "                    \n", "computed_df = await prop_fin.compute_property_financials(computed_df)\n", "computed_df.head(50)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}}, "nbformat": 4, "nbformat_minor": 2}