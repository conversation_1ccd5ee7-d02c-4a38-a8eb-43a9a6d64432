"""
Property Service - Main agent for property analysis tasks.

This service acts as the primary orchestrator for property analysis tasks,
delegating work to specialized sub-agents.
"""
from typing import List

from pydantic import BaseModel, Field

from pydantic_ai import Agent
from pydantic_ai.models.openai import OpenAIModel
from pydantic_ai.providers.openai import OpenAIProvider

from app.utils.env_cache import get_env
from app.utils.logger import get_logger

logger = get_logger(__name__)

class Unit(BaseModel):
    """Unit model."""
    id: str = Field(..., description="ID of the unit")
    address: str = Field(..., description="Address of the unit")
    unit: str = Field(..., description="Unit of the unit")

class UnitList(BaseModel):
    """List of units."""
    units: List[Unit] = Field(..., description="List of units")

class UnitMatch(BaseModel):
    """List of units to process."""
    units_unit_id: str = Field(..., description="EchoedID of the property unit")
    units_address: str = Field(..., description="Echoed address of the unit")
    market_data_unit: str = Field(..., description="Cleaned up unit of the market data that matched the Unit Address")
    market_data_unit_id: str = Field(..., description="Echoed ID of the market unit that matched the Unit Address")

class UnitMatchList(BaseModel):
    """List of units to process."""
    units: List[UnitMatch] = Field(..., description="List of units to process")

class UnitInput(BaseModel):
    """Input for unit matching task."""
    id: str = Field(..., description="ID of the property unit")
    data: str = Field(..., description="Data of the property unit; either an address or a unit")

class UnitMatchInputList(BaseModel):
    """List of unit matching tasks."""
    units: List[UnitInput] = Field(..., description="List of unit matching tasks")
    market_data: List[UnitInput] = Field(..., description="List of market data to match against")

class PropertyServiceAgent:
    """
    Main agent for financial analysis tasks.
    
    This agent orchestrates various financial analysis workflows by delegating to
    specialized sub-agents for specific tasks.
    """

    @classmethod
    async def extract_unit_from_address(cls, property_unit_data: List[UnitInput]) -> str:
        """Extract the unit from the address."""
        # Initialize prompts
        opeai_model = OpenAIModel("gpt-4.1", provider=OpenAIProvider(api_key=get_env('OPENAI_API_KEY')))

        agent = Agent(
            opeai_model,
            output_type=UnitList,
            system_prompt=(
                "You are a real estate investment analysis AI. Your sole task is to look at the address and extract the unit from the address."
            ),
        )   

        prompt = f"Please extract the unit from the address:\n\n{property_unit_data}" 

        result = await agent.run(prompt)

        return result.output


    @classmethod
    async def generate_unit_match_list(cls, property_unit_data: UnitMatchInputList) -> UnitMatchList:
        """Initialize the financial service agent."""
        # Initialize prompts
        opeai_model = OpenAIModel("gpt-4.1", provider=OpenAIProvider(api_key=get_env('OPENAI_API_KEY')))

        # Main orchestration agent
        agent = Agent(
            opeai_model,
            output_type=UnitMatchList,
            system_prompt=(
                "You are a real estate investment analysis AI. Your sole task is to look at the units in the property and match them to the market data units like a human would do. "
                "The units data has id and address. The market data has id and unit. "
                "The goal is to find matching units from market data to the units in the property based on the address, and to extract the unit from the units data based on the address. "
                "Return a structured UnitMatchList with confidence scores for each match. "
                "Be careful to avoid mismatches such as matching 'units_unit': '513X' with 'market_data_unit': 'Apt 517', or 'units_unit': '715X' with 'market_data_unit': 'Apt 716'. "
                "Only match units that are highly likely to be the same, and avoid matches where the unit numbers are similar but not exact (e.g., 513X vs 517, 715X vs 716). "
                "Also, avoid errors such as matching 'units_unit': 'APT 1213X' with 'market_data_unit': 'Ph 10'. Do not match units with very different formats or where the unit numbers/labels are not clearly related."
            ),
        )

        prompt = f"Please analyze this property data and provide a structured summary:\n\n{property_unit_data}" 

        result = await agent.run(prompt)

        return result.output
