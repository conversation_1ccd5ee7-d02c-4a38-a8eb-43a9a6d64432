"""
Financial Service - Main agent for financial analysis tasks.

This service acts as the primary orchestrator for financial analysis tasks,
delegating work to specialized sub-agents.
"""

import asyncio
import pandas as pd
from datetime import datetime             
from typing import Dict, Any, List, Optional, Union
from dataclasses import dataclass, field

from pydantic import BaseModel, Field

from pydantic_ai import Agent, RunContext, Tool
from pydantic_ai.models.openai import OpenAIModel
from pydantic_ai.providers.openai import OpenAIProvider

from app.utils.logger import get_logger
from app.utils.env_cache import get_env

from app.services.financials_service import PropertFinancialMetadataService
from app.repository.cache import redis_cache

from app.services.property_service import PropertyService
from app.services.rental_income_service import RentalIncomeService
from app.services.projection_service import ProjectionService, ProjectionContext
from app.services.solar_income_service import SolarIncomeService, SolarIncomeContext
from app.services.vacancy_analysis_service import VacancyAnalysisService, VacancyAnalysisContext
from app.services.credit_risk_service import CreditRiskService, CreditRiskContext
from app.services.expense_calculation_service import ExpenseCalculationService, ExpenseCalculationContext

from app.models.prompts import AgentPrompts
from app.models.ai import FinancialResponse, Source, FinancialResponsesList

logger = get_logger(__name__)

@dataclass
class FinancialTaskInput:
    task_type: str
    portfolio_id: str
    prop_id: str 
    year: int
    db: PropertyService
    return_projections: bool = False
    params: Dict[str, Any] = field(default_factory=dict)
    task_token: str = field(default=None)  # For progress tracking

class FinancialServiceAgent:
    """
    Main agent for financial analysis tasks.
    
    This agent orchestrates various financial analysis workflows by delegating to
    specialized sub-agents for specific tasks.
    """
    
    def __init__(self, model_name: str = 'gpt-4o-mini'):
        """Initialize the financial service agent."""
        # Initialize prompts
        self.prompts = AgentPrompts()

        self.model = OpenAIModel(model_name, provider=OpenAIProvider(api_key=get_env('OPENAI_API_KEY')))

        # Initialize services
        self.projection_service = ProjectionService(model_name)
        self.solar_income_service = SolarIncomeService()
        self.vacancy_service = VacancyAnalysisService()
        self.credit_risk_service = CreditRiskService()
        self.expense_calculation_service = ExpenseCalculationService()

        # Main orchestration agent
        self.agent = Agent(
            self.model,
            deps_type=FinancialTaskInput,
            output_type=FinancialResponsesList,
            tools=[
                self.calculate_rental_income,
                self.calculate_other_income,
                self.calculate_vacancy_loss,
                self.calculate_credit_loss,
                self.calculate_property_tax,
                self.calculate_property_insurance,
                self.calculate_overall_expense,
                self.calculate_repairs_analysis
            ]
        )

        # Set system prompt dynamically
        @self.agent.system_prompt
        async def agent_system_prompt(ctx: RunContext[FinancialTaskInput]) -> str:
            system_prompt = self.prompts.financial_analysis_prompt(prompt_name="agent_prompt")
            return system_prompt.format(task=ctx.deps.task_type, prop_id=ctx.deps.prop_id, year=ctx.deps.year)

    async def _is_field_human_edited(self, prop_id: str, year: int, field_name: str) -> bool:
        """
        Check if a specific field has human edits and should not be overwritten.
        Uses the centralized method from PropertFinancialService.

        Args:
            prop_id: Property ID to check
            year: Year to check for human edits
            field_name: Specific field name to check (e.g., 'property_tax', 'repairs')

        Returns:
            Boolean indicating if the field has human edits
        """
        try:
            # Use the centralized method from PropertFinancialService
            from app.services.financials_service import PropertFinancialService
            is_human_edited = await PropertFinancialService.is_field_human_edited(prop_id, year, field_name)

            if is_human_edited:
                logger.info(f"✋ Preserving human-edited field {field_name} in year {year}")

            return is_human_edited

        except Exception as e:
            logger.error(f"Error checking if field {field_name} is human-edited for property {prop_id} year {year}: {str(e)}")
            # Return False to ensure AI computation continues
            return False

    async def process_financial_requests(self, task_type: str, prop_id: str, year: int, portfolio_id: str, prop_fin_df: pd.DataFrame, return_projections: bool = True, params: Dict[str, Any] = {}, task_token: str = None):
        """Process financial requests for a property."""

        if prop_fin_df is None or prop_fin_df.empty or not prop_id:
            return None

        prompts = {
            "long_term_rental": self.prompts.financial_analysis_prompt(prompt_name="long_term_rental_prompt"),
            "other_income": self.prompts.financial_analysis_prompt(prompt_name="other_income_prompt"),
            "vacancy_loss": self.prompts.financial_analysis_prompt(prompt_name="vacancy_loss_prompt"),
            "credit_loss": self.prompts.financial_analysis_prompt(prompt_name="credit_loss_prompt"),
            "property_tax": self.prompts.financial_analysis_prompt(prompt_name="property_tax_prompt"),
            "overall_expense": self.prompts.financial_analysis_prompt(prompt_name="overall_expense_prompt")
        }

        if task_type == "all":
            # Report progress: Starting rental analysis
            if task_token:
                await redis_cache.set_task_queue(
                    stage="calculation",
                    token=task_token,
                    status="processing",
                    data={"progress": {"operation": "Analyzing rental income..."}}
                )

            # Run long_term_rental first to establish base rental income
            rental_prompt = prompts["long_term_rental"]
            rental_result = await self.agent.run(
                rental_prompt.format(prop_id=prop_id),
                deps=FinancialTaskInput(
                    task_type="long_term_rental",
                    prop_id=prop_id,
                    year=year,
                    portfolio_id=portfolio_id,
                    db=PropertyService(),
                    return_projections=return_projections,
                    params=params,
                    task_token=task_token
                )
            )
            
            # Store rental result for processing with other results
            rental_task_result = ("long_term_rental", rental_result)

            # Report progress: Starting concurrent tasks
            if task_token:
                await redis_cache.set_task_queue(
                    stage="calculation",
                    token=task_token,
                    status="processing",
                    data={"progress": {"operation": "Processing 5 financial analysis tasks..."}}
                )

            # Run all other tasks concurrently
            other_tasks = ["other_income", "vacancy_loss", "credit_loss", "property_tax", "overall_expense"]

            # Create tasks for concurrent execution
            concurrent_tasks = []
            for other_task in other_tasks:
                task_prompt = prompts[other_task]
                task_coroutine = self.agent.run(
                    task_prompt.format(prop_id=prop_id),
                    deps=FinancialTaskInput(
                        task_type=other_task,
                        prop_id=prop_id,
                        year=year,
                        portfolio_id=portfolio_id,
                        db=PropertyService(),
                        return_projections=return_projections,
                        params=params,
                        task_token=task_token
                    )
                )
                concurrent_tasks.append((other_task, task_coroutine))
            
            # Execute all other tasks concurrently and collect results
            task_results = await asyncio.gather(*[task[1] for task in concurrent_tasks], return_exceptions=True)

            # Report progress: Processing results
            if task_token:
                await redis_cache.set_task_queue(
                    stage="calculation",
                    token=task_token,
                    status="processing",
                    data={"progress": {"operation": "Processing financial analysis results..."}}
                )

            # Add rental result to the beginning of results for processing
            all_task_results = [rental_task_result] + [(concurrent_tasks[i][0], task_results[i]) for i in range(len(task_results))]

            # EXPLICIT DATA RECOMBINATION - Process each task result individually
            logger.info(f"Starting explicit data recombination for {len(all_task_results)} total tasks (including rental)")

            # Collect all metadata updates for batch processing
            all_metadata_updates = []

            for task_name, result in all_task_results:
                logger.info(f"Processing result from task: {task_name}")
                
                if isinstance(result, Exception):
                    logger.error(f"Task {task_name} failed with error: {str(result)}")
                    continue
                
                # Extract responses from successful results
                try:
                    result_obj: Any = result
                    if hasattr(result_obj, 'output') and hasattr(result_obj.output, 'responses'):
                        responses = result_obj.output.responses
                        logger.info(f"Task {task_name} returned {len(responses)} responses")
                        
                        # EXPLICIT LOOP: Process each response and save to DataFrame
                        for response_idx, response in enumerate(responses):
                            logger.info(f"  Processing response {response_idx + 1}/{len(responses)} from {task_name}")
                            
                            # Handle special case where overall_expense returns List[FinancialResponse]
                            if task_name == "overall_expense" and isinstance(response, list):
                                logger.info(f"  Task {task_name} returned a list of {len(response)} expense responses")
                                # Flatten the list and process each expense response
                                for expense_idx, expense_response in enumerate(response):
                                    logger.info(f"    Processing expense response {expense_idx + 1}/{len(response)}: {getattr(expense_response, 'task_type', 'unknown')}")
                                    
                                    if hasattr(expense_response, 'money_value') and hasattr(expense_response, 'task_type') and expense_response.money_value and expense_response.task_type:
                                        try:
                                            # Safe access to get old value
                                            try:
                                                old_value = prop_fin_df.loc[expense_response.task_type, 1]
                                            except (KeyError, IndexError):
                                                old_value = "empty"
                                            
                                            # Safe write to DataFrame
                                            prop_fin_df.loc[expense_response.task_type, 1] = round(expense_response.money_value)
                                            logger.info(f"      Saved {task_name}.{expense_response.task_type} Year 1: ${expense_response.money_value:,.2f} (was: {old_value})")
                                            
                                            # Handle projections for expense responses
                                            if hasattr(expense_response, 'additional_data') and expense_response.additional_data:
                                                for year_key, value in expense_response.additional_data.items():
                                                    if value and hasattr(value, 'money_value') and hasattr(value, 'year') and value.money_value and value.year and value.year in [2, 3, 4, 5]:
                                                        try:
                                                            try:
                                                                old_proj_value = prop_fin_df.loc[expense_response.task_type, value.year]
                                                            except (KeyError, IndexError):
                                                                old_proj_value = "empty"
                                                            
                                                            prop_fin_df.loc[expense_response.task_type, value.year] = round(value.money_value)
                                                            logger.info(f"      Saved {task_name}.{expense_response.task_type} Year {value.year}: ${value.money_value:,.2f} (was: {old_proj_value})")
                                                        except Exception as proj_error:
                                                            logger.error(f"      Error saving projection for {task_name}.{expense_response.task_type} Year {value.year}: {str(proj_error)}")
                                        except Exception as expense_error:
                                            logger.error(f"    Error processing expense response {expense_response.task_type}: {str(expense_error)}")
                            else:
                                # Normal single response processing
                                if hasattr(response, 'money_value') and hasattr(response, 'task_type') and response.money_value and response.task_type:
                                    try:
                                        # EXPLICIT SAVE: Write Year 1 data to DataFrame
                                        # Safe access to get old value
                                        try:
                                            old_value = prop_fin_df.loc[response.task_type, 1]
                                        except (KeyError, IndexError):
                                            old_value = "empty"
                                        
                                        # Safe write to DataFrame (will create row if it doesn't exist)
                                        prop_fin_df.loc[response.task_type, 1] = round(response.money_value)
                                        logger.info(f"    Saved {task_name}.{response.task_type} Year 1: ${response.money_value:,.2f} (was: {old_value})")
                                        
                                        # EXPLICIT SAVE: Write Years 2-5 projection data if available
                                        if hasattr(response, 'additional_data') and response.additional_data:
                                            for year_key, value in response.additional_data.items():
                                                if value and hasattr(value, 'money_value') and hasattr(value, 'year') and value.money_value and value.year and value.year in [2, 3, 4, 5]:
                                                    try:
                                                        # Safe access to get old projection value
                                                        try:
                                                            old_proj_value = prop_fin_df.loc[response.task_type, value.year]
                                                        except (KeyError, IndexError):
                                                            old_proj_value = "empty"
                                                        
                                                        # Safe write to DataFrame
                                                        prop_fin_df.loc[response.task_type, value.year] = round(value.money_value)
                                                        logger.info(f"    Saved {task_name}.{response.task_type} Year {value.year}: ${value.money_value:,.2f} (was: {old_proj_value})")
                                                    except Exception as proj_error:
                                                        logger.error(f"    Error saving projection for {task_name}.{response.task_type} Year {value.year}: {str(proj_error)}")
                                        
                                        # Collect metadata for batch processing
                                        if hasattr(response, 'sources') and response.sources:
                                            try:
                                                sources_metadata = {
                                                    "task_type": response.task_type,
                                                    "sources": [
                                                        {
                                                            "date": datetime.now().isoformat(),
                                                            "data": source.model_dump() if hasattr(source, 'model_dump') else source.__dict__
                                                        }
                                                        for source in response.sources
                                                    ]
                                                }
                                                all_metadata_updates.append(sources_metadata)
                                                logger.info(f"    Collected metadata for {task_name}.{response.task_type}")
                                            except Exception as meta_error:
                                                logger.error(f"    Error collecting metadata for {task_name}.{response.task_type}: {str(meta_error)}")
                                    
                                    except Exception as save_error:
                                        logger.error(f"    Error saving response from {task_name}: {str(save_error)}")
                                else:
                                    logger.warning(f"    Skipping response from {task_name}: missing money_value ({getattr(response, 'money_value', 'None')}) or task_type ({getattr(response, 'task_type', 'None')})")
                        
                        logger.info(f"Completed processing all responses from {task_name}")
                    else:
                        logger.error(f"Task {task_name} returned unexpected result structure: {type(result_obj)}")
                        
                except Exception as e:
                    logger.error(f"Error processing result from {task_name}: {str(e)}")

            # Batch update all collected metadata at once to avoid blocking the event loop
            if all_metadata_updates:
                logger.info(f"Performing batch metadata update for {len(all_metadata_updates)} entries")
                await self._batch_update_metadata_by_years(all_metadata_updates, prop_id, year)

            logger.info("Explicit data recombination completed - returning updated DataFrame")
            return prop_fin_df
        else:
            # Single task execution (existing logic)
            if task_token:
                await redis_cache.set_task_queue(
                    stage="calculation",
                    token=task_token,
                    status="processing",
                    data={"progress": {"operation": f"Analyzing {task_type}..."}}
                )

            prompt = prompts[task_type]

            result = await self.agent.run(
                prompt.format(prop_id=prop_id),
                deps=FinancialTaskInput(
                    task_type=task_type,
                    prop_id=prop_id,
                    year=year,
                    portfolio_id=portfolio_id,
                    db=PropertyService(),
                    return_projections=return_projections,
                    params=params,
                    task_token=task_token
                )
            )

            await self._process_financial_response(result.output.responses, prop_fin_df, prop_id, year)
            return prop_fin_df

    async def _process_financial_response(self, responses: List[FinancialResponse], prop_fin_df: pd.DataFrame, prop_id: str, year: int):
        """Process financial responses and update dataframe with metadata saving, preserving human-edited fields."""
        # Collect all metadata updates to batch them
        metadata_updates = []

        for response in responses:
            if response.money_value:
                if response.task_type:
                    # Check if this field has human edits before updating
                    is_human_edited = await self._is_field_human_edited(prop_id, year, response.task_type)

                    if not is_human_edited:
                        prop_fin_df.loc[response.task_type, 1] = round(response.money_value)
                        logger.debug(f"Updated AI field {response.task_type} = ${response.money_value:,.2f}")
                    else:
                        logger.info(f"Skipped updating human-edited field {response.task_type}")
                        continue  # Skip metadata updates for human-edited fields

                # Collect sources metadata for batch update (only for AI-updated fields)
                if response.sources and not is_human_edited:
                    sources_metadata = {
                        "task_type": response.task_type,
                        "sources": [
                            {
                                "date": datetime.now().isoformat(),
                                "data": source.model_dump() if hasattr(source, 'model_dump') else source.__dict__
                            }
                            for source in response.sources
                        ]
                    }
                    metadata_updates.append(sources_metadata)

                if response.additional_data and not is_human_edited:
                    for year_key, value in response.additional_data.items():
                        if value.money_value and value.year:
                            response_year = value.year
                            if response_year in [2, 3, 4, 5] and response.task_type:
                                # Check if this specific year field has human edits
                                year_field_human_edited = await self._is_field_human_edited(prop_id, response_year, response.task_type)

                                if not year_field_human_edited:
                                    prop_fin_df.loc[response.task_type, response_year] = round(value.money_value)
                                    logger.debug(f"Updated AI field {response.task_type} year {response_year} = ${value.money_value:,.2f}")
                                else:
                                    logger.info(f"Skipped updating human-edited field {response.task_type} for year {response_year}")
                                    continue  # Skip metadata updates for this year's human-edited field

                                # Collect additional data sources metadata for batch update (only for AI-updated fields)
                                if value.sources and not year_field_human_edited:
                                    additional_sources_metadata = {
                                        "task_type": response.task_type,
                                        "year": response_year,
                                        "sources": [
                                            {
                                                "date": datetime.now().isoformat(),
                                                "data": source.model_dump() if hasattr(source, 'model_dump') else source.__dict__
                                            }
                                            for source in value.sources
                                        ]
                                    }
                                    metadata_updates.append(additional_sources_metadata)

        # Group metadata updates by year and batch update each year separately
        if metadata_updates:
            await self._batch_update_metadata_by_years(metadata_updates, prop_id, year)

    async def _batch_update_metadata(self, metadata_updates: List[Dict[str, Any]], prop_id: str, year: int):
        """Batch update metadata to avoid blocking the event loop with multiple database calls."""
        try:
            # Get existing metadata once
            existing_data = await PropertFinancialMetadataService.get_property_metadata(prop_id, year)
            updated_metadata = existing_data.get("metadata", {}) if existing_data else {}

            # Process all metadata updates in memory
            for metadata in metadata_updates:
                task_type = metadata.get("task_type")
                if task_type:
                    # Initialize the task_type list if it doesn't exist
                    if task_type not in updated_metadata:
                        updated_metadata[task_type] = []

                    # Append the new metadata to the task_type list
                    updated_metadata[task_type].append(metadata)
                else:
                    # If no task_type, just merge the metadata
                    updated_metadata.update(metadata)

            # Single database update with all metadata changes
            await PropertFinancialMetadataService.set_property_metadata(prop_id, year, updated_metadata)
            logger.debug(f"Batch updated {len(metadata_updates)} metadata entries for prop_id={prop_id}, year={year}")

        except Exception as e:
            logger.error(f"Error in batch metadata update: {str(e)}")
            # Fallback to individual updates if batch fails
            logger.info("Falling back to individual metadata updates")
            for metadata in metadata_updates:
                try:
                    await PropertFinancialMetadataService.append_property_metadata(
                        metadata=metadata,
                        prop_id=prop_id,
                        year=year
                    )
                except Exception as individual_error:
                    logger.error(f"Error in individual metadata update: {str(individual_error)}")

    async def _batch_update_metadata_by_years(self, metadata_updates: List[Dict[str, Any]], prop_id: str, base_year: int):
        """Group metadata updates by year and batch update each year separately."""
        try:
            # Group metadata by year
            metadata_by_year = {}

            for metadata in metadata_updates:
                # Check if metadata has a specific year, otherwise use base_year
                target_year = metadata.get("year", base_year)

                if target_year not in metadata_by_year:
                    metadata_by_year[target_year] = []

                metadata_by_year[target_year].append(metadata)

            # Batch update each year separately
            for year, year_metadata in metadata_by_year.items():
                logger.debug(f"Batch updating {len(year_metadata)} metadata entries for year {year}")
                await self._batch_update_metadata(year_metadata, prop_id, year)

        except Exception as e:
            logger.error(f"Error in batch metadata update by years: {str(e)}")
            # Fallback to individual updates if batch fails
            logger.info("Falling back to individual metadata updates")
            for metadata in metadata_updates:
                try:
                    await PropertFinancialMetadataService.append_property_metadata(
                        metadata=metadata,
                        prop_id=prop_id,
                        year=metadata.get("year", base_year)
                    )
                except Exception as individual_error:
                    logger.error(f"Error in individual metadata update: {str(individual_error)}")

    async def _get_property_location_info(self, prop_id: str) -> Dict[str, Optional[str]]:
        """Get property location information for analysis."""
        try:
            # Use PropertyService class method
            property_data = await PropertyService.get_property(prop_id)
            
            if not property_data:
                logger.warning(f"No property found for {prop_id}")
                return {"zipcode": None, "city": None, "state": None}
            
            # Get property address
            if property_data.address_id:
                address = await PropertyService.get_property_address(str(property_data.address_id))
                if address:
                    return {
                        "zipcode": getattr(address, 'zip', None),
                        "city": getattr(address, 'city', None),
                        "state": getattr(address, 'state', None)
                    }
            
            logger.warning(f"No address found for property {prop_id}")
            return {"zipcode": None, "city": None, "state": None}
            
        except Exception as e:
            logger.error(f"Error getting property location info: {str(e)}")
            return {"zipcode": None, "city": None, "state": None}

    async def calculate_rental_income(self, ctx: RunContext[FinancialTaskInput]) -> FinancialResponse:
        """Calculate the rental income for a property: long_term_rental request"""
        try:
            # Report progress if task_token available
            if ctx.deps.task_token:
                await redis_cache.set_task_queue(
                    stage="calculation",
                    token=ctx.deps.task_token,
                    status="processing",
                    data={"progress": {"operation": "Calculating rental income projections..."}}
                )

            # Use the new rental income service to calculate total rental income
            rental_service = RentalIncomeService()
            result = await rental_service.calculate_rental_income(ctx.deps.prop_id)
            
            # Return the total rental income as annual amount (monthly * 12)
            annual_rental_income = result.total_rental_income * 12
            
            # Create base FinancialResponse for Year 1
            year_1_response = FinancialResponse(
                task_type="long_term_rental",
                portfolio_id=ctx.deps.portfolio_id,
                prop_id=ctx.deps.prop_id,
                year=ctx.deps.year,
                sources=[
                    Source(
                        source="rental_income_analysis",
                        description=f"Rental income calculation: {len(result.units_with_rent)} units with rent, {len(result.estimated_rents)} estimated",
                        url="internal_calculation"
                    )
                ],
                value=annual_rental_income,
                unit="USD",
                money_value=annual_rental_income,
                money_unit="USD",
                description=f"Annual rental income for Year {ctx.deps.year}: ${annual_rental_income:.2f} (${result.total_rental_income:.2f}/month)",
                additional_data=None
            )
            
            # Add projections if requested
            if ctx.deps.return_projections:
                logger.info("Calculating rental income projections for years 2-5")
                
                # Create projection context
                projection_context = ProjectionContext(
                    prop_id=ctx.deps.prop_id,
                    portfolio_id=ctx.deps.portfolio_id,
                    base_annual_income=annual_rental_income
                )
                
                # Calculate projections using the projection service
                projections = await self.projection_service.calculate_rental_projections(projection_context)
                
                # Convert dict to only include years 2-5 in additional_data
                if projections:
                    filtered_projections = {}
                    for year_key, response in projections.items():
                        if year_key != 'year_1' and response:  # Exclude year 1, include years 2-5
                            filtered_projections[year_key] = response
                    year_1_response.additional_data = filtered_projections if filtered_projections else None
                else:
                    year_1_response.additional_data = None
            
            return year_1_response
            
        except Exception as e:
            logger.error(f"Error calculating rental income: {str(e)}")
            # Return error response
            return FinancialResponse(
                task_type="rental_income",
                portfolio_id=ctx.deps.portfolio_id,
                prop_id=ctx.deps.prop_id,
                year=ctx.deps.year,
                sources=[Source(source="error", description=f"Error calculating rental income: {str(e)}", url="internal")],
                value=0.0,
                unit="USD",
                money_value=0.0,
                money_unit="USD",
                description=f"Error calculating rental income: {str(e)}",
                additional_data=None
            )

    async def calculate_other_income(self, ctx: RunContext[FinancialTaskInput]) -> FinancialResponse:
        """Calculate other income projections for a property: other_income request"""
        try:
            # Get property location info
            location_info = await self._get_property_location_info(ctx.deps.prop_id)
            
            # Create other income context
            solar_context = SolarIncomeContext(
                prop_id=ctx.deps.prop_id,
                portfolio_id=ctx.deps.portfolio_id,
                zipcode=location_info.get('zipcode'),
                city=location_info.get('city'),
                state=location_info.get('state')
            )
            
            # Calculate other income projections
            projections = await self.solar_income_service.calculate_solar_income_projections(solar_context)
            
            # Get year 1 data or create default
            year_1_projection = projections.get('year_1') if projections else None
            
            if not year_1_projection:
                # Create default year 1 response if no projections
                year_1_projection = FinancialResponse(
                    task_type="other_income",
                    portfolio_id=ctx.deps.portfolio_id,
                    prop_id=ctx.deps.prop_id,
                    year=ctx.deps.year,
                    sources=[Source(source="other_income_analysis", description=f"Other income analysis for {location_info.get('city', 'unknown location')}", url="internal_calculation")],
                    value=0.0,
                    unit="USD",
                    money_value=0.0,
                    money_unit="USD",
                    description=f"Other income for Year {ctx.deps.year}: solar, laundry, parking, etc.",
                    additional_data=None
                )
            
            # Add projections if requested
            if ctx.deps.return_projections and projections:
                # Filter out year 1 and include years 2-5 in additional_data
                filtered_projections = {}
                for year_key, response in projections.items():
                    if year_key != 'year_1' and response:
                        filtered_projections[year_key] = response
                year_1_projection.additional_data = filtered_projections if filtered_projections else None
            else:
                year_1_projection.additional_data = None
            
            return year_1_projection
            
        except Exception as e:
            logger.error(f"Error calculating other income: {str(e)}")
            return FinancialResponse(
                task_type="other_income",
                portfolio_id=ctx.deps.portfolio_id,
                prop_id=ctx.deps.prop_id,
                year=ctx.deps.year,
                sources=[Source(source="error", description=f"Error calculating other income: {str(e)}", url="internal")],
                value=0.0,
                unit="USD",
                money_value=0.0,
                money_unit="USD",
                description=f"Error calculating other income: {str(e)}",
                additional_data=None
            )

    async def calculate_vacancy_loss(self, ctx: RunContext[FinancialTaskInput]) -> FinancialResponse:
        """Calculate vacancy loss projections for a property: vacancy_loss request"""
        try:
            # Report progress if task_token available
            if ctx.deps.task_token:
                await redis_cache.set_task_queue(
                    stage="calculation",
                    token=ctx.deps.task_token,
                    status="processing",
                    data={"progress": {"operation": "Analyzing vacancy loss patterns..."}}
                )

            # Get property location info
            location_info = await self._get_property_location_info(ctx.deps.prop_id)
            
            # Get rental projections - either from params or generate if needed
            rental_projections = ctx.deps.params.get('rental_projections')
            
            # If return_projections is True and no rental projections provided, generate them
            if ctx.deps.return_projections and not rental_projections:
                logger.info("Generating rental projections for vacancy loss calculation")
                
                # First get current rental income
                rental_service = RentalIncomeService()
                rental_result = await rental_service.calculate_rental_income(ctx.deps.prop_id)
                annual_rental_income = rental_result.total_rental_income * 12
                
                # Generate rental projections for years 1-5
                projection_context = ProjectionContext(
                    prop_id=ctx.deps.prop_id,
                    portfolio_id=ctx.deps.portfolio_id,
                    base_annual_income=annual_rental_income
                )
                
                # Get projections for years 2-5
                future_projections = await self.projection_service.calculate_rental_projections(projection_context)
                
                # Create year 1 projection manually
                year_1_projection = FinancialResponse(
                    task_type="rental_income",
                    portfolio_id=ctx.deps.portfolio_id,
                    prop_id=ctx.deps.prop_id,
                    year=1,
                    sources=[Source(source="current_rental_analysis", description="Current rental income calculation", url="internal")],
                    value=0,  # Growth rate for year 1 is 0%
                    unit="%",
                    money_value=annual_rental_income,
                    money_unit="USD",
                    description="Current annual rental income for year 1",
                    additional_data=None
                )
                
                # Combine all projections
                rental_projections = {
                    "year_1": year_1_projection
                }
                rental_projections.update(future_projections)
            
            # Create vacancy analysis context
            vacancy_context = VacancyAnalysisContext(
                prop_id=ctx.deps.prop_id,
                portfolio_id=ctx.deps.portfolio_id,
                zipcode=location_info.get('zipcode'),
                city=location_info.get('city'),
                state=location_info.get('state'),
                rental_projections=rental_projections
            )
            
            # Calculate vacancy loss projections
            projections = await self.vacancy_service.calculate_vacancy_loss_projections(vacancy_context)
            
            # Get year 1 data or create default
            year_1_projection = projections.get('year_1') if projections else None
            
            if not year_1_projection:
                # Create default year 1 response
                year_1_projection = FinancialResponse(
                    task_type="vacancy_loss",
                    portfolio_id=ctx.deps.portfolio_id,
                    prop_id=ctx.deps.prop_id,
                    year=ctx.deps.year,
                    sources=[Source(source="vacancy_analysis", description=f"Vacancy loss analysis for {location_info.get('city', 'unknown location')}", url="internal_calculation")],
                    value=0.0,
                    unit="%",
                    money_value=0.0,
                    money_unit="USD",
                    description=f"Vacancy loss for Year {ctx.deps.year}",
                    additional_data=None
                )
            
            # Add projections if requested
            if ctx.deps.return_projections and projections:
                # Filter out year 1 and include years 2-5 in additional_data
                filtered_projections = {}
                for year_key, response in projections.items():
                    if year_key != 'year_1' and response:
                        filtered_projections[year_key] = response
                year_1_projection.additional_data = filtered_projections if filtered_projections else None
            else:
                year_1_projection.additional_data = None
            
            return year_1_projection
            
        except Exception as e:
            logger.error(f"Error calculating vacancy loss: {str(e)}")
            return FinancialResponse(
                task_type="vacancy_loss",
                portfolio_id=ctx.deps.portfolio_id,
                prop_id=ctx.deps.prop_id,
                year=ctx.deps.year,
                sources=[Source(source="error", description=f"Error calculating vacancy loss: {str(e)}", url="internal")],
                value=0.0,
                unit="%",
                money_value=0.0,
                money_unit="USD",
                description=f"Error calculating vacancy loss: {str(e)}",
                additional_data=None
            )

    async def calculate_credit_loss(self, ctx: RunContext[FinancialTaskInput]) -> FinancialResponse:
        """Calculate credit loss projections for a property: credit_loss request"""
        try:
            # Get property location info
            location_info = await self._get_property_location_info(ctx.deps.prop_id)
            
            # Get rental projections - either from params or generate if needed
            rental_projections = ctx.deps.params.get('rental_projections')
            
            # If return_projections is True and no rental projections provided, generate them
            if ctx.deps.return_projections and not rental_projections:
                logger.info("Generating rental projections for credit loss calculation")
                
                # First get current rental income
                rental_service = RentalIncomeService()
                rental_result = await rental_service.calculate_rental_income(ctx.deps.prop_id)
                annual_rental_income = rental_result.total_rental_income * 12
                
                # Generate rental projections for years 1-5
                projection_context = ProjectionContext(
                    prop_id=ctx.deps.prop_id,
                    portfolio_id=ctx.deps.portfolio_id,
                    base_annual_income=annual_rental_income
                )
                
                # Get projections for years 2-5
                future_projections = await self.projection_service.calculate_rental_projections(projection_context)
                
                # Create year 1 projection manually
                year_1_projection = FinancialResponse(
                    task_type="rental_income",
                    portfolio_id=ctx.deps.portfolio_id,
                    prop_id=ctx.deps.prop_id,
                    year=1,
                    sources=[Source(source="current_rental_analysis", description="Current rental income calculation", url="internal")],
                    value=0,  # Growth rate for year 1 is 0%
                    unit="%",
                    money_value=annual_rental_income,
                    money_unit="USD",
                    description="Current annual rental income for year 1",
                    additional_data=None
                )
                
                # Combine all projections
                rental_projections = {
                    "year_1": year_1_projection
                }
                rental_projections.update(future_projections)
            
            # Create credit risk context
            credit_context = CreditRiskContext(
                prop_id=ctx.deps.prop_id,
                portfolio_id=ctx.deps.portfolio_id,
                zipcode=location_info.get('zipcode'),
                city=location_info.get('city'),
                state=location_info.get('state'),
                rental_projections=rental_projections
            )
            
            # Calculate credit loss projections
            projections = await self.credit_risk_service.calculate_credit_loss_projections(credit_context)
            
            # Get year 1 data or create default
            year_1_projection = projections.get('year_1') if projections else None
            
            if not year_1_projection:
                # Create default year 1 response
                year_1_projection = FinancialResponse(
                    task_type="credit_loss",
                    portfolio_id=ctx.deps.portfolio_id,
                    prop_id=ctx.deps.prop_id,
                    year=ctx.deps.year,
                    sources=[Source(source="credit_risk_analysis", description=f"Credit loss analysis for {location_info.get('city', 'unknown location')}", url="internal_calculation")],
                    value=0.0,
                    unit="%",
                    money_value=0.0,
                    money_unit="USD",
                    description=f"Credit loss for Year {ctx.deps.year}",
                    additional_data=None
                )
            
            # Add projections if requested
            if ctx.deps.return_projections and projections:
                # Filter out year 1 and include years 2-5 in additional_data
                filtered_projections = {}
                for year_key, response in projections.items():
                    if year_key != 'year_1' and response:
                        filtered_projections[year_key] = response
                year_1_projection.additional_data = filtered_projections if filtered_projections else None
            else:
                year_1_projection.additional_data = None
            
            return year_1_projection
            
        except Exception as e:
            logger.error(f"Error calculating credit loss: {str(e)}")
            return FinancialResponse(
                task_type="credit_loss",
                portfolio_id=ctx.deps.portfolio_id,
                prop_id=ctx.deps.prop_id,
                year=ctx.deps.year,
                sources=[Source(source="error", description=f"Error calculating credit loss: {str(e)}", url="internal")],
                value=0.0,
                unit="%",
                money_value=0.0,
                money_unit="USD",
                description=f"Error calculating credit loss: {str(e)}",
                additional_data=None
            )

    async def calculate_property_tax(self, ctx: RunContext[FinancialTaskInput]) -> FinancialResponse:
        """Calculate property tax for a property: property_tax request"""
        try:
            # Report progress if task_token available
            if ctx.deps.task_token:
                await redis_cache.set_task_queue(
                    stage="calculation",
                    token=ctx.deps.task_token,
                    status="processing",
                    data={"progress": {"operation": "Calculating property tax estimates..."}}
                )

            # Get property location info
            location_info = await self._get_property_location_info(ctx.deps.prop_id)
            
            # Get effective gross income if available (rental + other income)
            effective_gross_income = ctx.deps.params.get('effective_gross_income')
            rental_income = ctx.deps.params.get('rental_income')
            
            # Create expense calculation context
            expense_context = ExpenseCalculationContext(
                prop_id=ctx.deps.prop_id,
                portfolio_id=ctx.deps.portfolio_id,
                zipcode=location_info.get('zipcode'),
                city=location_info.get('city'),
                state=location_info.get('state'),
                effective_gross_income=effective_gross_income,
                rental_income=rental_income
            )
            
            # First estimate property value
            valuation = await self.expense_calculation_service.calculate_property_value_estimate(expense_context)
            estimated_value = valuation.get('estimated_value', 0)
            
            # Calculate property tax expenses
            tax_projections = await self.expense_calculation_service.calculate_property_tax_expenses(expense_context, estimated_value)
            
            # Get year 1 data or create default
            year_1_projection = tax_projections.get('year_1') if tax_projections else None
            
            if not year_1_projection:
                # Create default year 1 response
                year_1_projection = FinancialResponse(
                    task_type="property_tax",
                    portfolio_id=ctx.deps.portfolio_id,
                    prop_id=ctx.deps.prop_id,
                    year=ctx.deps.year,
                    sources=[Source(source="property_tax_analysis", description=f"Property tax analysis for {location_info.get('city', 'unknown location')}", url="internal_calculation")],
                    value=0.0,
                    unit="USD",
                    money_value=0.0,
                    money_unit="USD",
                    description=f"Property tax for Year {ctx.deps.year}",
                    additional_data=None
                )
            
            # Add projections if requested
            if ctx.deps.return_projections and tax_projections:
                # Filter out year 1 and include years 2-5 in additional_data
                filtered_projections = {}
                for year_key, response in tax_projections.items():
                    if year_key != 'year_1' and response:
                        filtered_projections[year_key] = response
                year_1_projection.additional_data = filtered_projections if filtered_projections else None
            else:
                year_1_projection.additional_data = None
            
            return year_1_projection
            
        except Exception as e:
            logger.error(f"Error calculating property tax: {str(e)}")
            return FinancialResponse(
                task_type="property_tax",
                portfolio_id=ctx.deps.portfolio_id,
                prop_id=ctx.deps.prop_id,
                year=ctx.deps.year,
                sources=[Source(source="error", description=f"Error calculating property tax: {str(e)}", url="internal")],
                value=0.0,
                unit="USD",
                money_value=0.0,
                money_unit="USD",
                description=f"Error calculating property tax: {str(e)}",
                additional_data=None
            )

    async def calculate_property_insurance(self, ctx: RunContext[FinancialTaskInput]) -> FinancialResponse:
        """Calculate property insurance for a property: property_insurance request"""
        try:
            # Get property location info
            location_info = await self._get_property_location_info(ctx.deps.prop_id)
            
            # Get effective gross income if available
            effective_gross_income = ctx.deps.params.get('effective_gross_income')
            rental_income = ctx.deps.params.get('rental_income')
            
            # Create expense calculation context
            expense_context = ExpenseCalculationContext(
                prop_id=ctx.deps.prop_id,
                portfolio_id=ctx.deps.portfolio_id,
                zipcode=location_info.get('zipcode'),
                city=location_info.get('city'),
                state=location_info.get('state'),
                effective_gross_income=effective_gross_income,
                rental_income=rental_income
            )
            
            # First estimate property value
            valuation = await self.expense_calculation_service.calculate_property_value_estimate(expense_context)
            estimated_value = valuation.get('estimated_value', 0)
            
            # Calculate insurance expenses
            insurance_projections = await self.expense_calculation_service.calculate_insurance_expenses(expense_context, estimated_value)
            
            # Get year 1 data or create default
            year_1_projection = insurance_projections.get('year_1') if insurance_projections else None
            
            if not year_1_projection:
                # Create default year 1 response
                year_1_projection = FinancialResponse(
                    task_type="insurance",
                    portfolio_id=ctx.deps.portfolio_id,
                    prop_id=ctx.deps.prop_id,
                    year=ctx.deps.year,
                    sources=[Source(source="property_insurance_analysis", description=f"Property insurance analysis for {location_info.get('city', 'unknown location')}", url="internal_calculation")],
                    value=0.0,
                    unit="USD",
                    money_value=0.0,
                    money_unit="USD",
                    description=f"Property insurance for Year {ctx.deps.year}",
                    additional_data=None
                )
            
            # Add projections if requested
            if ctx.deps.return_projections and insurance_projections:
                # Filter out year 1 and include years 2-5 in additional_data
                filtered_projections = {}
                for year_key, response in insurance_projections.items():
                    if year_key != 'year_1' and response:
                        filtered_projections[year_key] = response
                year_1_projection.additional_data = filtered_projections if filtered_projections else None
            else:
                year_1_projection.additional_data = None
            
            return year_1_projection
            
        except Exception as e:
            logger.error(f"Error calculating property insurance: {str(e)}")
            return FinancialResponse(
                task_type="insurance",
                portfolio_id=ctx.deps.portfolio_id,
                prop_id=ctx.deps.prop_id,
                year=ctx.deps.year,
                sources=[Source(source="error", description=f"Error calculating property insurance: {str(e)}", url="internal")],
                value=0.0,
                unit="USD",
                money_value=0.0,
                money_unit="USD",
                description=f"Error calculating property insurance: {str(e)}",
                additional_data=None
            )

    async def calculate_overall_expense(self, ctx: RunContext[FinancialTaskInput]) -> List[FinancialResponse]:
        """Calculate overall expense for a property: overall_expense request"""
        try:
            # Report progress if task_token available
            if ctx.deps.task_token:
                await redis_cache.set_task_queue(
                    stage="calculation",
                    token=ctx.deps.task_token,
                    status="processing",
                    data={"progress": {"operation": "Calculating property expenses..."}}
                )

            # Get property location info
            location_info = await self._get_property_location_info(ctx.deps.prop_id)
            
            # Get income data if available
            effective_gross_income = ctx.deps.params.get('effective_gross_income')
            rental_income = ctx.deps.params.get('rental_income')
            
            # If no income provided and needed, calculate it
            if not rental_income:
                logger.info("Calculating rental income for expense calculations")
                rental_service = RentalIncomeService()
                rental_result = await rental_service.calculate_rental_income(ctx.deps.prop_id)
                rental_income = rental_result.total_rental_income * 12  # Annual
            
            # Create expense calculation context
            expense_context = ExpenseCalculationContext(
                prop_id=ctx.deps.prop_id,
                portfolio_id=ctx.deps.portfolio_id,
                zipcode=location_info.get('zipcode'),
                city=location_info.get('city'),
                state=location_info.get('state'),
                effective_gross_income=effective_gross_income,
                rental_income=rental_income
            )
            
            # Calculate all expenses with projection flag
            expense_responses = await self.expense_calculation_service.calculate_all_expenses(
                expense_context, 
                return_projections=ctx.deps.return_projections
            )
            
            logger.info(f"Returning {len(expense_responses)} individual expense responses (return_projections={ctx.deps.return_projections})")
            
            # Return the list of FinancialResponse objects directly
            return expense_responses
            
        except Exception as e:
            logger.error(f"Error calculating overall expense: {str(e)}")
            return []

    async def calculate_repairs_analysis(self, ctx: RunContext[FinancialTaskInput]) -> FinancialResponse:
        """Calculate repairs analysis for a property: repairs_analysis request"""
        try:
            # Import the repairs analysis service
            from app.services.repairs_analysis_service import RepairsAnalysisService, RepairsAnalysisContext
            
            # Get property location info
            location_info = await self._get_property_location_info(ctx.deps.prop_id)
            
            # Get income data for property valuation
            effective_gross_income = ctx.deps.params.get('effective_gross_income')
            rental_income = ctx.deps.params.get('rental_income')
            
            # If no income provided, calculate it
            if not rental_income:
                logger.info("Calculating rental income for repairs analysis")
                rental_service = RentalIncomeService()
                rental_result = await rental_service.calculate_rental_income(ctx.deps.prop_id)
                rental_income = rental_result.total_rental_income * 12  # Annual
            
            # First estimate property value for context
            expense_context = ExpenseCalculationContext(
                prop_id=ctx.deps.prop_id,
                portfolio_id=ctx.deps.portfolio_id,
                zipcode=location_info.get('zipcode'),
                city=location_info.get('city'),
                state=location_info.get('state'),
                effective_gross_income=effective_gross_income,
                rental_income=rental_income
            )
            
            valuation = await self.expense_calculation_service.calculate_property_value_estimate(expense_context)
            estimated_value = valuation.get('estimated_value', 0)
            
            # Initialize repairs analysis service
            repairs_service = RepairsAnalysisService()
            
            # Get property details for analysis
            property_data = await PropertyService.get_property(ctx.deps.prop_id)
            units = await PropertyService.get_property_units(ctx.deps.prop_id)
            
            # Calculate total square footage
            total_sqft = 0
            for unit in units:
                if hasattr(unit, 'sqft') and unit.sqft:
                    total_sqft += unit.sqft
            
            # Determine property age
            property_age = None
            try:
                from app.repository.db import get_db_client
                client = await get_db_client()
                details_response = client.table("prop_details").select("year_built").eq("prop_id", ctx.deps.prop_id).execute()
                
                if details_response.data and len(details_response.data) > 0:
                    year_built = details_response.data[0].get('year_built')
                    if year_built:
                        from datetime import datetime
                        current_year = datetime.now().year
                        property_age = current_year - int(year_built)
            except Exception as e:
                logger.warning(f"Could not determine property age: {str(e)}")
            
            # Create repairs analysis context
            repairs_context = RepairsAnalysisContext(
                prop_id=ctx.deps.prop_id,
                portfolio_id=ctx.deps.portfolio_id,
                estimated_property_value=estimated_value,
                property_age=property_age,
                sqft=total_sqft,
                property_type=getattr(property_data, 'property_type', None) if property_data else None
            )
            
            # Calculate repairs projections
            repairs_projections = await repairs_service.calculate_repairs_projections(repairs_context)
            
            # Get year 1 data or create default
            year_1_projection = repairs_projections.get('year_1') if repairs_projections else None
            
            if not year_1_projection:
                # Create default year 1 response
                year_1_projection = FinancialResponse(
                    task_type="repairs",
                    portfolio_id=ctx.deps.portfolio_id,
                    prop_id=ctx.deps.prop_id,
                    year=ctx.deps.year,
                    sources=[Source(source="repairs_analysis", description=f"Repairs analysis for {total_sqft} sqft property, age {property_age} years", url="internal_calculation")],
                    value=0.0,
                    unit="USD",
                    money_value=0.0,
                    money_unit="USD",
                    description=f"Repairs and maintenance for Year {ctx.deps.year}",
                    additional_data=None
                )
            
            # Add projections if requested
            if ctx.deps.return_projections and repairs_projections:
                # Filter out year 1 and include years 2-5 in additional_data
                filtered_projections = {}
                for year_key, response in repairs_projections.items():
                    if year_key != 'year_1' and response:
                        filtered_projections[year_key] = response
                year_1_projection.additional_data = filtered_projections if filtered_projections else None
            else:
                year_1_projection.additional_data = None
            
            return year_1_projection
            
        except Exception as e:
            logger.error(f"Error calculating repairs analysis: {str(e)}")
            return FinancialResponse(
                task_type="repairs",
                portfolio_id=ctx.deps.portfolio_id,
                prop_id=ctx.deps.prop_id,
                year=ctx.deps.year,
                sources=[Source(source="error", description=f"Error calculating repairs analysis: {str(e)}", url="internal")],
                value=0.0,
                unit="USD",
                money_value=0.0,
                money_unit="USD",
                description=f"Error calculating repairs analysis: {str(e)}",
                additional_data=None
            )

# Initialize singleton instance
financial_service = FinancialServiceAgent()
