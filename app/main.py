"""
Main application module for FastAPI initialization and configuration.
"""
# Configure Logfire first - before any app imports
import json
import base64
import uvicorn
import secrets
import logfire
from datetime import datetime, date

# Import env_cache first since we need it for Logfire configuration
from app.utils.env_cache import get_env

# Initialize Logfire before importing any app modules
logfire.configure(
    token=get_env('LOGFIRE_WRITE_TOKEN', None),
    environment=get_env('LOGFIRE_ENVIRONMENT'),
    service_name='relm-intel',  
)

# Instrument Pydantic to trace model validations
logfire.instrument_pydantic(
    record='all',
    include={'openai', 'pydantic_ai'}  # Include third-party pydantic_ai package
)

logfire.instrument_pydantic_ai()

# Now import app modules after Logfire is configured
from fastapi.responses import JSONResponse
from fastapi.openapi.utils import get_openapi
from fastapi.middleware.cors import CORSMiddleware
from fastapi import FastAP<PERSON>, Depends, HTTPException, status, Header
from fastapi.openapi.docs import get_swagger_ui_html, get_redoc_html
from fastapi.security import HTTPBasi<PERSON>, HTTPBasicCredentials, HTTPBearer

from app.utils.logger import get_logger, configure_root_logger
from app.api import router as api_router
from app.repository.cache import redis_cache
from app.repository.db import connection_manager, init_db
from app.utils.auth import get_token_from_header
from app.utils.middleware import add_auth_middleware

configure_root_logger()

# Setup logger
logger = get_logger(__name__)

# Security for docs
security = HTTPBasic()
api_key_header = HTTPBearer()

class CustomJSONEncoder(json.JSONEncoder):
    """Custom JSON encoder that handles datetime objects."""
    def default(self, obj):
        if isinstance(obj, (datetime, date)):
            return obj.isoformat()
        return super().default(obj)

def get_current_username(credentials: HTTPBasicCredentials = Depends(security)) -> str:
    """
    Validate credentials for docs access.
    
    Args:
        credentials: HTTP Basic credentials
        
    Returns:
        str: Username if authenticated
        
    Raises:
        HTTPException: If authentication fails
    """
    # Use cached getter
    correct_username_val = get_env("DOCS_USERNAME", "relm-admin")
    correct_password_val = get_env("DOCS_PASSWORD", "password123")
    
    if correct_username_val is None or correct_password_val is None:
        # This case should technically not happen due to defaults, but handles None type
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Basic"},
        )
        
    current_username_bytes = credentials.username.encode("utf8")
    correct_username_bytes = correct_username_val.encode("utf8")
    
    is_correct_username = secrets.compare_digest(current_username_bytes, correct_username_bytes)
    # Use correct_password_val directly for comparison as compare_digest needs string
    is_correct_password = secrets.compare_digest(credentials.password, correct_password_val)
    
    if not (is_correct_username and is_correct_password):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Basic"},
        )
    return credentials.username

def create_application() -> FastAPI:
    """
    Create and configure the FastAPI application.
    
    Returns:
        FastAPI: Configured FastAPI application
    """
    application = FastAPI(
        title="Relm Intelligence AI",
        description="WebSocket-based chatbot API with AI service integration",
        version="0.1.0",
        docs_url=None,  # Disable default docs
        redoc_url=None,  # Disable default redoc
        json_encoder=CustomJSONEncoder
    )
    
    # Configure CORS
    application.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],  # In production, specify the exact origins
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # Add authentication middleware with configuration
    # Auth middleware is added first to protect all routes
    public_paths = [
        "/health", 
        "/docs", 
        "/redoc", 
        "/openapi.json", 
        "/debug-token", 
        "/test-auth", 
        "/data/prop-ai-summary-short",
        "/finance/calculate",
        "/cache/prop-data",
        "/subscription/webhook"
        ]
    
    # Add the auth middleware
    add_auth_middleware(
        application, 
        public_paths=public_paths,
    )
    
    # Include API routes
    application.include_router(api_router)
    
    # Health check endpoint
    @application.get("/health", tags=["System"], summary="Health check", description="Verify API is running")
    async def health_check():
        """
        Health check endpoint to verify the API is running.
        Used by container orchestration systems like Kubernetes/ECS.
        """
        # Check basic service health
        health_data = {
            "status": "healthy",
            "version": application.version,
            "timestamp": datetime.utcnow().isoformat(),
            "services": {}
        }
        
        # Check Redis connectivity
        try:
            redis_ping = await redis_cache.redis.ping()
            health_data["services"]["redis"] = "connected" if redis_ping else "error"
        except Exception as e:
            health_data["services"]["redis"] = f"error: {str(e)}"
            # Don't fail the health check just because Redis has issues
            logger.warning(f"Redis health check failed: {str(e)}")
            
        # Check Supabase connectivity 
        try:
            db_connected = await connection_manager.verify_connection()
            health_data["services"]["supabase"] = "connected" if db_connected else "error"
        except Exception as e:
            health_data["services"]["supabase"] = f"error: {str(e)}"
            # Don't fail the health check just because DB has issues
            logger.warning(f"Supabase health check failed: {str(e)}")
        
        # Return success even if dependent services have issues
        # This ensures the container stays running so we can debug
        return health_data
    
    @application.get("/debug-token", tags=["System"], summary="Debug JWT token", include_in_schema=False)
    async def debug_token(authorization: str = Header(None)):
        """
        Debug endpoint to inspect JWT tokens without validation.
        
        Args:
            authorization: Authorization header with Bearer token
            
        Returns:
            Dict with token information or error
        """
        if not authorization or not authorization.startswith("Bearer "):
            return JSONResponse({
                "error": "No Bearer token provided",
                "tip": "Please include Authorization: Bearer <token> header"
            })
            
        token = authorization.replace("Bearer ", "")
        jwt_secret = get_env("SUPABASE_JWT_SECRET", "")
        
        # Just extract the parts without validation
        parts = token.split(".")
        if len(parts) != 3:
            return JSONResponse({
                "error": "Invalid token format",
                "token_parts": len(parts)
            })
            
        try:
            # Function to decode base64 parts
            def decode_part(part):
                # Add padding if needed
                padded = part + "=" * (4 - len(part) % 4) if len(part) % 4 != 0 else part
                # Convert to standard base64
                standard_base64 = padded.replace("-", "+").replace("_", "/")
                # Decode
                return json.loads(base64.b64decode(standard_base64).decode("utf-8"))
                
            header = decode_part(parts[0])
            payload = decode_part(parts[1])
            
            # Check if we have the correct secret to validate
            return JSONResponse({
                "header": header,
                "payload": payload,
                # Ensure jwt_secret is a string before len()
                "jwt_secret_length": len(jwt_secret) if jwt_secret is not None else 0,
                "jwt_secret_preview": jwt_secret[:5] + "..." if jwt_secret else "not set",
                "message": "Token decoded without validation. Use the JWT payload information to help debug issues."
            })
        except Exception as e:
            return JSONResponse({
                "error": f"Error decoding token: {str(e)}",
                "token": token[:10] + "..." # Just show the beginning of the token
            })
    
    @application.get("/test-auth", tags=["System"], summary="Test authentication")
    async def test_auth(token_data = Depends(get_token_from_header)):
        """
        Test endpoint to validate token authentication.
        
        Args:
            token_data: Validated token data from the authorization header
            
        Returns:
            Dict with user data
        """
        return {
            "authenticated": True,
            "user_id": token_data.get("sub"),
            "email": token_data.get("email"),
            "role": token_data.get("role"),
        }
    
    # Custom OpenAPI and Swagger UI endpoints with authentication
    @application.get("/docs", include_in_schema=False)
    async def custom_swagger_ui_html(username: str = Depends(get_current_username)):
        """
        Custom Swagger UI endpoint with additional configuration.
        """
        return get_swagger_ui_html(
            openapi_url=application.openapi_url or "/openapi.json",
            title=f"{application.title} - Swagger UI",
            oauth2_redirect_url=application.swagger_ui_oauth2_redirect_url,
            swagger_js_url="https://cdn.jsdelivr.net/npm/swagger-ui-dist@5/swagger-ui-bundle.js",
            swagger_css_url="https://cdn.jsdelivr.net/npm/swagger-ui-dist@5/swagger-ui.css",
            swagger_favicon_url="https://fastapi.tiangolo.com/img/favicon.png",
        )
    
    @application.get("/redoc", include_in_schema=False)
    async def custom_redoc_html(username: str = Depends(get_current_username)):
        """
        Custom ReDoc endpoint with additional configuration.
        """
        return get_redoc_html(
            openapi_url=application.openapi_url or "/openapi.json",
            title=f"{application.title} - ReDoc",
            redoc_js_url="https://cdn.jsdelivr.net/npm/redoc@next/bundles/redoc.standalone.js",
            redoc_favicon_url="https://fastapi.tiangolo.com/img/favicon.png",
        )
    
    @application.get("/openapi.json", include_in_schema=False)
    async def get_open_api_endpoint(username: str = Depends(get_current_username)):
        """
        Custom OpenAPI JSON endpoint with additional metadata.
        """
        openapi_schema = get_openapi(
            title=application.title,
            version=application.version,
            description=application.description,
            routes=application.routes,
        )
        
        # Add security scheme to OpenAPI schema for Swagger UI authorization button
        openapi_schema["components"] = {
            "securitySchemes": {
                "bearerAuth": {
                    "type": "http",
                    "scheme": "bearer",
                    "bearerFormat": "JWT",
                    "description": "Enter JWT token from your Supabase authentication service"
                }
            }
        }
        
        # Apply security globally to all endpoints except for docs
        openapi_schema["security"] = [{"bearerAuth": []}]
        
        return openapi_schema
    
    @application.on_event("startup")
    async def startup_event():
        """Initialize connections on startup."""
        try:
            # This will set up the global 'db' for backward compatibility
            await init_db()
        except Exception as e:
            # Log error but don't crash
            logger.error(f"Failed to initialize database connection: {str(e)}")
            # We don't re-raise the exception to allow the app to start even with DB issues
            
        # Validate Redis connection
        try:
            # Redis cache is initialized on module import
            await redis_cache.redis.ping()
            logger.info("Redis connection verified")
        except Exception as e:
            logger.error(f"Failed to connect to Redis: {str(e)}")
            # Don't crash on Redis connection failure
    
    @application.on_event("shutdown")
    async def shutdown_event():
        """Close connections on shutdown."""
        # Close Redis connection
        await redis_cache.close()
    
    return application

app = create_application()

if __name__ == "__main__":
    logger.info("Starting server")
    uvicorn.run("app.main:app", host="0.0.0.0", port=8000, reload=True) 