"""
Economic Data Utility - Fetches and provides economic indicators.

This utility provides access to economic data like inflation rates,
interest rates, and other economic indicators useful for financial projections.
"""
from typing import Dict
from app.utils.logger import get_logger

logger = get_logger(__name__)

class EconomicDataUtil:
    """Utility class for fetching economic data."""
    
    @staticmethod
    def get_historical_inflation_data() -> str:
        """Get historical inflation data for the last 5 years."""
        try:
            # Historical inflation data - this could be replaced with API calls
            inflation_info = """
            Based on recent economic data:
            - 2019: 1.8% inflation
            - 2020: 1.2% inflation (pandemic impact)
            - 2021: 4.7% inflation (recovery surge)
            - 2022: 8.0% inflation (peak)
            - 2023: 4.1% inflation (cooling down)
            - 2024: 3.2% inflation (estimated target approach)
            
            Recent trends show inflation moderating from 2022 peaks but remaining above historical 2% target.
            Rental markets typically track inflation with some correlation to housing costs.
            """
            
            logger.info("Retrieved historical inflation data for projections")
            return inflation_info
            
        except Exception as e:
            logger.error(f"Error fetching inflation data: {str(e)}")
            return "Unable to fetch current inflation data. Using default assumptions."
    
    @staticmethod
    def get_inflation_rates() -> Dict[int, float]:
        """Get historical inflation rates as dictionary."""
        return {
            2019: 1.8,
            2020: 1.2,
            2021: 4.7,
            2022: 8.0,
            2023: 4.1,
            2024: 3.2
        } 