#!/usr/bin/env python3
"""
HTML to PDF converter using WeasyPrint.
This module provides a utility for converting HTML documents to PDF files
with customizable styling, headers, footers, and table of contents.
"""

import os
import re
import time
import random
import tempfile
import traceback
import concurrent.futures

from datetime import datetime
from weasyprint import HTML, CSS
from weasyprint.text.fonts import FontConfiguration

from app.utils.logger import get_logger

logger = get_logger(__name__)

# Default CSS for PDF styling
DEFAULT_CSS = """
@page {
    size: landscape;
    margin: 1cm;
    @top-left {
        content: "Relm Portfolios";
        font-size: 9pt;
        color: #666;
        padding-top: 0.5cm;
    }
    @bottom-right {
        content: counter(page);
        font-size: 9pt;
    }
}

@page :first {
    @top-right { content: none; }
    @top-left { content: none; }
    @bottom-right { content: none; }
}

@font-face {
    font-family: 'Inter';
    src: url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');
}

html {
    font-size: 12pt;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
}

body {
    margin: 0;
    padding: 0;
}

h1, h2, h3, h4, h5, h6 {
    margin-top: 1em;
    margin-bottom: 0.5em;
    page-break-after: avoid;
    page-break-inside: avoid;
    font-weight: 600;
}

h1 {
    string-set: title content();
    font-size: 2.2em;
    padding-bottom: 0.3em;
    text-align: center;
    color: #111;
    border-bottom: none;
}

h2 {
    font-size: 1.8em;
    color: #222;
    border-bottom: none;
}

h3 { font-size: 1.5em; color: #333; }
h4 { font-size: 1.3em; color: #444; }

p, ul, ol, dl, table, pre {
    margin: 0.8em 0;
}

a {
    color: #0070f3;
    text-decoration: none;
}

blockquote {
    margin: 1em 0;
    padding: 0 1em;
    color: #777;
    border-left: 4px solid #ddd;
}

hr {
    height: 1px;
    padding: 0;
    margin: 1em 0;
    background-color: #e7e7e7;
    border: 0;
    page-break-after: always;
}

table {
    border-collapse: collapse;
    width: 100%;
    overflow: auto;
    page-break-inside: avoid;
    margin-top: 0.8em;
    margin-bottom: 1.2em;
}

table th, table td {
    padding: 6px 13px;
    border: 1px solid #ddd;
}

table th {
    background-color: #f0f0f0;
    font-weight: 600;
}

table tr {
    background-color: #fff;
    border-top: 1px solid #ccc;
}

table tr:nth-child(2n) {
    background-color: #f8f8f8;
}

img {
    max-width: 100%;
    height: auto;
}

/* Cover page */
.cover-page {
    text-align: center;
    height: 100vh;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    page-break-after: always !important;
}

.cover-title {
    font-size: 3.2em;
    margin-bottom: 0.5em;
    color: #000;
    font-weight: 700;
}

.cover-subtitle {
    font-size: 2em;
    color: #444;
    margin-bottom: 1.5em;
    font-weight: 500;
}

.cover-for-user {
    font-size: 1.5em;
    color: #666;
    margin-bottom: 1.5em;
}

.cover-date {
    font-size: 1.2em;
    color: #777;
    margin-top: 1em;
}

/* Table of contents */
.table-of-contents {
    page-break-after: always !important;
    padding: 0 2em;
}

.table-of-contents h2 {
    font-size: 2em;
    margin-bottom: 1em;
    text-align: center;
    color: #000;
}

.table-of-contents ul {
    list-style-type: disc;
    padding-left: 1.5em;
}

.table-of-contents ul ul {
    list-style-type: circle;
    padding-left: 2em;
}

.table-of-contents a {
    text-decoration: none;
    color: #0070f3;
}

.table-of-contents li {
    margin-bottom: 0.5em;
    border-bottom: none;
    padding-bottom: 0.3em;
}

/* For slide-like presentation */
.slide {
    page-break-after: always !important;
    page-break-before: always !important;
    margin-bottom: 0;
    min-height: 90vh;
    display: flex;
    flex-direction: column;
    padding: 0.5cm;
    border: none;
}

.slide:first-child {
    page-break-before: auto !important;
}

.slide:last-child {
    page-break-after: avoid !important;
}

.slide h1 {
    text-align: center;
    margin-top: 0.5em;
    border-bottom: none;
}

.slide h2 {
    margin-top: 0.8em;
    border-bottom: none;
}

.slide-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: 0 1em;
}

/* Two-column layout */
.columns {
    display: flex;
    justify-content: space-between;
    gap: 2em;
    width: 100%;
    margin-top: 1em;
}

.column {
    width: 48%;
    overflow-wrap: break-word;
    word-wrap: break-word;
    hyphens: auto;
    padding-right: 0.5em;
}

/* Property details formatting */
.property-details .columns {
    margin-top: 0.5em;
}

.property-details h2 {
    margin-top: 0.5em;
    margin-bottom: 0.3em;
}

.property-pros-cons {
    display: flex;
    justify-content: space-between;
    gap: 2em;
    margin-top: 1em;
}

.property-pros, .property-cons {
    width: 48%;
    padding: 0.5em;
    border-radius: 8px;
}

.property-pros {
    background-color: rgba(220, 252, 231, 0.3); /* Light green */
}

.property-cons {
    background-color: rgba(254, 226, 226, 0.3); /* Light red */
}

.property-pros h3, .property-cons h3 {
    margin-top: 0;
    margin-bottom: 0.5em;
    text-align: center;
}

.property-pros ul, .property-cons ul {
    margin-top: 0.3em;
}

/* Fix for bullet points in columns */
.column ul {
    padding-left: 1.2em;
    margin-top: 0;
}

.column li {
    margin-bottom: 0.4em;
}

/* Final slide */
.final-slide {
    text-align: center;
    height: 90vh;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    page-break-after: avoid !important;
}

.final-title {
    font-size: 2.5em;
    margin-bottom: 0.8em;
    color: #000;
}

.final-subtitle {
    font-size: 1.5em;
    color: #444;
    margin-bottom: 2em;
}

.final-website {
    font-size: 1.2em;
    color: #0070f3;
    margin-top: 2em;
}

.final-portfolio {
    font-size: 1.2em;
    color: #666;
}
"""

class HtmlToPdfConverter:
    """
    Converts HTML content to PDF using WeasyPrint with customizable styling.
    """
    
    def __init__(self, custom_css=None):
        """
        Initialize the converter with optional custom CSS.
        
        Args:
            custom_css: Optional additional CSS to apply to the PDF
        """
        self.default_css = DEFAULT_CSS
        self.custom_css = custom_css
    
    def create_cover_page(self, title, subtitle=None):
        """
        Create a cover page for the PDF.
        
        Args:
            title: Document title
            subtitle: Optional subtitle
        Returns:
            HTML for the cover page
        """
        today = datetime.now().strftime('%B %d, %Y')
        
        cover_html = '<div class="cover-page">\n'
        cover_html += f'<div class="cover-title">{title}</div>\n'
        
        if subtitle:
            cover_html += f'<div class="cover-subtitle">{subtitle}</div>\n'
        
        cover_html += f'<div class="cover-date">Generated on {today}</div>\n'
        cover_html += '</div>\n'
        
        return cover_html

    def sanitize_html_content(self, html_content):
        """
        Sanitize and fix HTML content to ensure proper slide separation.
        
        Args:
            html_content: Raw HTML content to sanitize
            
        Returns:
            Sanitized HTML content
        """
        # Remove any text outside of proper HTML elements
        html_content = re.sub(r'(?<=>)([^<]+)(?=<div class="slide")', '', html_content)
        
        # Fix nested slide divs
        html_content = re.sub(r'<div class="slide">\s*<div class="slide">', '<div class="slide">', html_content)
        
        # Ensure each slide has an h1 tag
        slide_parts = re.split(r'(<div class="slide">|</div>\s*<div class="slide">)', html_content)
        new_parts = []
        
        in_slide = False
        for part in slide_parts:
            if part == '<div class="slide">':
                in_slide = True
                new_parts.append(part)
            elif in_slide and not re.search(r'<h1', part) and part.strip() and not part.startswith('</div>'):
                # Add a default heading if missing
                new_parts.append('<h1>Slide</h1>\n' + part)
            else:
                new_parts.append(part)
        
        html_content = ''.join(new_parts)
        
        # Make sure content sections have the proper class
        html_content = re.sub(r'(<div class="slide">.*?<h1>.*?</h1>)(?!\s*<div class="slide-content">)',
                             r'\1\n<div class="slide-content">', html_content, flags=re.DOTALL)
        
        # Close any unclosed slide-content divs
        html_content = re.sub(r'(<div class="slide-content">.*?)(?=<div class="slide">|$)',
                             r'\1</div>', html_content, flags=re.DOTALL)
        
        # Add explicit page break after each slide
        html_content = html_content.replace('</div>\n<div class="slide">', 
                                           '</div>\n<div style="page-break-after: always;"></div>\n<div class="slide">')
        
        return html_content
    
    def convert_html_to_pdf(self, html_content, output_path=None, title=None, 
                            subtitle=None, include_cover=True):
        """
        Convert HTML content to PDF.
        
        Args:
            html_content: Raw HTML text
            output_path: Optional path to save the PDF (if None, a temporary file is created)
            title: Optional document title
            subtitle: Optional document subtitle
            include_cover: Whether to include a cover page
            
        Returns:
            Path to the generated PDF file
        """
        temp_dir = None
        try:
            # Create a temporary directory if no output path is provided
            if not output_path:
                temp_dir = tempfile.mkdtemp(prefix="html_pdf_")
                
                # Generate a safe filename
                random_digits = str(random.randint(1000, 9999))
                safe_title = "document"
                
                if title:
                    # Convert title to a safe filename
                    safe_title = "".join(c for c in title if c.isalnum() or c in (' ', '_', '-')).strip()
                    safe_title = safe_title.replace(' ', '_')
                    # Limit filename length to avoid path length issues
                    safe_title = safe_title[:50]
                
                if not safe_title:
                    safe_title = f"document_{datetime.now().strftime('%Y%m%d')}_{random_digits}"
                
                output_path = os.path.join(temp_dir, f"{safe_title}.pdf")
            
            logger.info(f"Output path: {output_path}")
            
            # Sanitize HTML content to fix slide-related issues
            html_content = self.sanitize_html_content(html_content)
            
            # Add custom CSS for slide page breaks
            slide_css = """
            @page {
                margin: 1cm;
                size: landscape;
            }
            
            .slide {
                page-break-after: always !important;
                page-break-before: always !important;
                margin-bottom: 0;
                min-height: 92vh;
                display: flex;
                flex-direction: column;
                padding: 0.5cm;
                border: none;
            }
            
            /* First slide should not have a page break before it */
            body > .slide:first-of-type {
                page-break-before: avoid !important;
            }
            
            /* Last slide should not force a page break after it */
            .slide:last-of-type {
                page-break-after: avoid !important;
            }
            
            .slide-content {
                flex: 1;
                display: flex;
                flex-direction: column;
                justify-content: center;
                padding: 0 1em;
            }
            """
            
            if self.custom_css:
                self.custom_css += slide_css
            else:
                self.custom_css = slide_css
            
            # Create a complete HTML document with optimized structure
            full_html = [
                '<!DOCTYPE html>',
                '<html>',
                '<head>',
                f'<title>{title if title else "Document"}</title>',
                '<meta charset="UTF-8">',
                '</head>',
                '<body>'
            ]
            
            # Add cover page if requested
            if include_cover and title:
                full_html.append(self.create_cover_page(title, subtitle))
            
            # Add the main content
            full_html.append(html_content)
            full_html.append('</body></html>')
            
            # Join all HTML parts (more memory efficient than concatenation)
            full_html_str = '\n'.join(full_html)
            
            # Save the HTML to a temporary file for debugging if needed
            debug_html_path = os.path.join(os.path.dirname(output_path), "debug_output.html")
            with open(debug_html_path, 'w') as f:
                f.write(full_html_str)
            logger.info(f"Debug HTML saved to: {debug_html_path}")
            
            # Configure WeasyPrint
            font_config = FontConfiguration()
            css = CSS(string=self.default_css, font_config=font_config)
            
            logger.info("configured css")
            
            if self.custom_css:
                custom_css = CSS(string=self.custom_css, font_config=font_config)
                css_list = [css, custom_css]
            else:
                css_list = [css]
            
            # Generate PDF with optimized settings
            html = HTML(string=full_html_str)
            html.write_pdf(
                output_path, 
                stylesheets=css_list, 
                font_config=font_config,
                # Add optimization hints for WeasyPrint
                optimize_size=('fonts', 'images'),
                # Lower resolution for images if size is a concern
                resolution=96
            )
            
            # Clean up memory explicitly
            full_html_str = None
            html_content = None
            full_html = None
            
            logger.info("cleaned up memory")
            
            # Verify PDF was created
            if not os.path.exists(output_path) or os.path.getsize(output_path) == 0:
                raise RuntimeError("PDF file was not created or is empty")
            
            return output_path
            
        except Exception as e:
            error_msg = f"Error converting HTML to PDF: {str(e)}"
            logger.error(error_msg)
            logger.info("Traceback:", traceback.format_exc())
            raise RuntimeError(error_msg) from e
            
        finally:
            # Make sure temporary files are always cleaned up if an exception occurs
            # but only if we're the ones who created the temp directory
            if temp_dir and output_path and output_path.startswith(temp_dir) and os.path.exists(output_path):
                try:
                    # Make sure we don't delete the PDF file that was requested
                    temp_files = [f for f in os.listdir(temp_dir) 
                                 if os.path.join(temp_dir, f) != output_path]
                    
                    for f in temp_files:
                        os.remove(os.path.join(temp_dir, f))
                        
                    logger.info(f"Cleaned up {len(temp_files)} temporary files")
                except Exception as cleanup_e:
                    logger.warning(f"Error cleaning up temporary files: {str(cleanup_e)}")
                    # We don't want cleanup issues to affect the main process

# Convenience function to use the converter without instantiating the class
def convert_html_to_pdf(html_content, output_path=None, title=None, 
                        subtitle=None, include_cover=True, custom_css=None):
    """
    Convert HTML content to PDF using the HtmlToPdfConverter.
    
    Args:
        html_content: Raw HTML text
        output_path: Optional path to save the PDF (if None, a temporary file is created)
        title: Optional document title
        subtitle: Optional document subtitle
        include_cover: Whether to include a cover page
        custom_css: Optional additional CSS to apply to the PDF
        
    Returns:
        Path to the generated PDF file
    """
    # Start timer for performance tracking
    start_time = time.time()
    
    # Basic validation
    if not html_content:
        raise ValueError("HTML content cannot be empty")
    
    # Create converter and check available fonts
    converter = HtmlToPdfConverter(custom_css=custom_css)
    
    # Set up logging for the conversion process
    logger.info(f"Starting PDF conversion of {len(html_content)} bytes of HTML")
    
    try:
        # Use concurrent.futures to apply a timeout
        with concurrent.futures.ThreadPoolExecutor() as executor:
            future = executor.submit(
                converter.convert_html_to_pdf,
                html_content=html_content,
                output_path=output_path,
                title=title,
                subtitle=subtitle,
                include_cover=include_cover
            )
            
            logger.info("got future")
            
            # Wait for the result with a timeout
            result = future.result()
            
            logger.info("got result")
            
            # Log completion time
            elapsed_time = time.time() - start_time
            logger.info(f"PDF conversion completed in {elapsed_time:.2f} seconds")
            
            return result
            
    except concurrent.futures.TimeoutError:
        logger.error("PDF conversion timed out")
        raise TimeoutError("PDF conversion timed out")
    except Exception as e:
        logger.error(f"Error in PDF conversion: {str(e)}")
        raise 