"""
Middleware for request/response processing.
"""
from typing import Callable, List
from starlette.types import ASGIApp
from fastapi import Fast<PERSON><PERSON>, Request, Response, status
from starlette.middleware.base import BaseHTTPMiddleware

from app.utils.logger import get_logger
from app.utils.auth import supabase_auth

logger = get_logger(__name__)

class AuthMiddleware(BaseHTTPMiddleware):
    """
    Middleware to enforce authentication on all protected endpoints.
    """
    def __init__(
        self, 
        app: ASGIApp, 
        public_paths: List[str] = None, 
        public_path_prefixes: List[str] = None
    ):
        """
        Initialize the authentication middleware.
        
        Args:
            app: The ASGI app
            public_paths: List of exact paths that should be public
            public_path_prefixes: List of path prefixes that should be public
        """
        super().__init__(app)
        self.public_paths = public_paths or []
        self.public_path_prefixes = public_path_prefixes or []
        
        # Always make these paths public
        self.public_paths.extend([
            "/health",
            "/docs",
            "/redoc",
            "/openapi.json",
            "/swagger-ui-bundle.js",
            "/swagger-ui.css",
            "/oauth2-redirect.html",
            "/debug-token"
        ])
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """
        Dispatch the request and enforce authentication if needed.
        
        Args:
            request: The incoming request
            call_next: The next middleware/route handler
            
        Returns:
            The response
        """
        # Skip authentication for public paths
        path = request.url.path
        
        logger.debug(f"Request path: {path}, method: {request.method}")
        
        # Check if the path is public
        if path in self.public_paths:
            logger.debug(f"Public path: {path}")
            return await call_next(request)
            
        # Check if the path starts with any public prefix
        for prefix in self.public_path_prefixes:
            if path.startswith(prefix):
                logger.debug(f"Public prefix match: {prefix}")
                return await call_next(request)
        
        # OPTIONS requests should be allowed for CORS preflight
        if request.method == "OPTIONS":
            logger.debug("CORS preflight request")
            return await call_next(request)
        
        # Enforce authentication for all other paths
        try:
            # Extract token from header for debugging
            auth_header = request.headers.get("authorization")
            if auth_header:
                logger.debug(f"Auth header found: {auth_header[:15]}...")
            else:
                logger.warning("No authorization header found")
            
            # Validate the JWT token
            user = await supabase_auth(request)
            
            # Add the user to the request state for access in route handlers
            request.state.user = user
            
            # Continue with the request
            return await call_next(request)
            
        except Exception as e:
            # Log the error
            logger.warning(f"Authentication failed: {str(e)}")
            
            # Return 401 Unauthorized
            return Response(
                status_code=status.HTTP_401_UNAUTHORIZED,
                content=str(e),
                media_type="text/plain",
                headers={"WWW-Authenticate": "Bearer"}
            )

def add_auth_middleware(
    app: FastAPI, 
    public_paths: List[str] = None,
    public_path_prefixes: List[str] = None
) -> None:
    """
    Add the authentication middleware to the FastAPI app.
    
    Args:
        app: The FastAPI application
        public_paths: List of exact paths that should be public
        public_path_prefixes: List of path prefixes that should be public
    """
    app.add_middleware(
        AuthMiddleware,
        public_paths=public_paths,
        public_path_prefixes=public_path_prefixes
    )
    logger.info("Added authentication middleware to protect API endpoints") 