"""
Environment variable caching utility.

This module provides functions to cache environment variables using LRU cache
to avoid repeated calls to os.getenv(). It also loads variables from a .env file.
"""
import os
import functools
from typing import Optional, Any, Dict
from dotenv import load_dotenv

# Load .env file into environment variables
load_dotenv()


# Cache for environment variables
@functools.lru_cache(maxsize=128)
def get_env(key: str, default: Any = None) -> Optional[str]:
    """
    Get environment variable with LRU caching.
    
    Args:
        key: Environment variable name
        default: Default value if variable is not set
        
    Returns:
        The environment variable value or default
    """
    return os.getenv(key, default)


# For cases where we need to clear the cache (e.g. in tests)
def clear_env_cache() -> None:
    """
    Clear the environment variable cache.
    Useful for testing or when environment variables change at runtime.
    """
    get_env.cache_clear()


# Dictionary-style access to environment variables
class EnvCache:
    """
    Class that provides dictionary-style access to cached environment variables.
    
    Usage:
        env = EnvCache()
        api_key = env["API_KEY"]
    """
    _cache: Dict[str, Optional[str]] = {}
    _max_size: int = 128
    
    def __getitem__(self, key: str) -> Optional[str]:
        """Get environment variable with caching."""
        if key not in self._cache:
            # If cache is full, remove least recently used item
            if len(self._cache) >= self._max_size:
                # Remove first item (LRU)
                self._cache.pop(next(iter(self._cache)))
            
            # Add new item
            self._cache[key] = os.getenv(key)
            
        # Return cached value
        return self._cache[key]
    
    def get(self, key: str, default: Any = None) -> Any:
        """Get environment variable with default value."""
        value = self[key]
        return default if value is None else value
    
    def clear(self) -> None:
        """Clear the cache."""
        self._cache.clear()


# Create a singleton instance
env = EnvCache() 