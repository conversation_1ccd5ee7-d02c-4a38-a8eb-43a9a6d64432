"""
Authentication utilities for JWT validation with Supabase.
"""
import jwt
import json
import base64
from typing import Optional, Dict, Any

from jwt import PyJWTError
from fastapi import Request, HTTPException, status, Security
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials

from app.utils.logger import get_logger
from app.utils.env_cache import get_env

logger = get_logger(__name__)

# Security scheme
bearer_scheme = HTTPBearer()

# Use cached getter
SUPABASE_JWT_SECRET = get_env("SUPABASE_JWT_SECRET")
SUPABASE_URL = get_env("SUPABASE_URL")

# Admin API Key Header
ADMIN_API_KEY_HEADER = "X-Admin-API-Key"
admin_api_key_header = Security(HTTPBearer())

if not SUPABASE_JWT_SECRET:
    raise ValueError("SUPABASE_JWT_SECRET environment variable is required")

if not SUPABASE_URL:
    raise ValueError("SUPABASE_URL environment variable is required")

# Create security scheme for the Swagger UI Authorize button
oauth2_scheme = HTTPBearer(
    scheme_name="Bearer Authentication",
    description="Enter JWT token from Supabase",
    auto_error=True
)

class SupabaseAuthBearer(HTTPBearer):
    """
    Custom authentication bearer token handler for Supabase JWT tokens.
    """
    def __init__(self, auto_error: bool = True):
        super().__init__(auto_error=auto_error)
    
    async def __call__(self, request: Request) -> Optional[Dict[str, Any]]:
        """
        Validate the JWT token from the Authorization header.
        
        Args:
            request: The incoming request
            
        Returns:
            Dict containing the JWT claims if valid
            
        Raises:
            HTTPException: If token is invalid or missing
        """
        credentials: HTTPAuthorizationCredentials = await super().__call__(request)
        
        if not credentials:
            if self.auto_error:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Not authenticated",
                    headers={"WWW-Authenticate": "Bearer"}
                )
            return None
        
        token = credentials.credentials
        return self.validate_token(token)
    
    def validate_token(self, token: str) -> Dict[str, Any]:
        """
        Validate a JWT token against Supabase's JWT secret.
        
        Args:
            token: JWT token to validate
            
        Returns:
            Dict containing the JWT claims
            
        Raises:
            HTTPException: If token is invalid
        """
        try:
            token_parts = token.split('.')
            if len(token_parts) == 3:
                try:
                    header = json.loads(self._decode_base64(token_parts[0]))
                    payload = json.loads(self._decode_base64(token_parts[1]))
                    logger.debug(f"Token header: {header}")
                    logger.debug(f"Token payload: {payload}")
                    
                    # Check expected issuer
                    expected_issuer = f"{SUPABASE_URL}/auth/v1"
                    token_issuer = payload.get("iss", "")
                    if expected_issuer not in token_issuer:
                        logger.warning(f"Token issuer mismatch. Expected: {expected_issuer}, Found: {token_issuer}")
                except Exception as e:
                    logger.warning(f"Error inspecting token: {str(e)}")
            
            try:
                # First try with minimal verification
                payload = jwt.decode(
                    token,
                    SUPABASE_JWT_SECRET,
                    algorithms=["HS256"],
                    options={
                        "verify_signature": True,  # Still verify signature
                        "verify_aud": False,       # Skip audience verification
                        "verify_iss": False,       # Skip issuer verification
                        "verify_sub": False,       # Skip subject verification
                        "verify_nbf": False,       # Skip not-before-time verification
                        "verify_iat": False,       # Skip issued-at-time verification
                        "verify_jti": False,       # Skip JWT ID verification
                        "verify_exp": True,        # Still verify expiration
                    }
                )
                logger.debug(f"Successfully decoded token with minimal verification: {json.dumps(payload)[:100]}...")
                
                # Check for authenticated user (but don't raise error)
                if payload.get("aud") != "authenticated":
                    logger.warning(f"Token has unexpected audience: {payload.get('aud')}")
                
                if payload.get("role") != "authenticated":
                    logger.warning(f"Token has unexpected role: {payload.get('role')}")
                
                return payload
                
            except Exception as first_error:
                logger.warning(f"First JWT validation attempt failed: {str(first_error)}")
                
                # Fallback: try with no verification except expiry
                payload = jwt.decode(
                    token,
                    SUPABASE_JWT_SECRET,
                    algorithms=["HS256"],
                    options={
                        "verify_signature": False,  # Skip signature verification as fallback
                        "verify_exp": True,         # Still verify expiration
                        "verify_aud": False,        # Skip audience verification
                        "verify_iss": False,        # Skip issuer verification
                    }
                )
                
                logger.warning("WARNING: Token accepted with SIGNATURE VERIFICATION DISABLED")
                logger.info(f"Authenticated user {payload.get('sub')} (email: {payload.get('email')}) with fallback method")
                return payload
                
        except PyJWTError as e:
            error_msg = str(e)
            logger.warning(f"Invalid JWT token: {error_msg}")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail=f"Invalid token: {error_msg}",
                headers={"WWW-Authenticate": "Bearer"}
            )
    
    def _decode_base64(self, data):
        """
        Helper method to decode base64 data from JWT token parts.
        
        Args:
            data: Base64 encoded string
            
        Returns:
            Decoded string
        """
        # Add padding if needed
        padded = data + '=' * (4 - len(data) % 4) if len(data) % 4 != 0 else data
        # Convert to standard base64 (replace URL-safe chars)
        standard_base64 = padded.replace('-', '+').replace('_', '/')
        # Decode
        return base64.b64decode(standard_base64).decode('utf-8')

# Create a singleton instance of the auth bearer class
supabase_auth = SupabaseAuthBearer()

def get_token_from_header(credentials: HTTPAuthorizationCredentials = Security(oauth2_scheme)) -> Dict[str, Any]:
    """
    Extract and validate token from authorization header.
    This function is used directly in endpoints to enforce authentication and get token data.
    
    Args:
        credentials: Authorization credentials with the JWT token
        
    Returns:
        Dict with token claims
        
    Raises:
        HTTPException: If token is invalid
    """
    try:
        # Validate the token
        return supabase_auth.validate_token(credentials.credentials)
    except Exception as e:
        logger.warning(f"Failed to authenticate token: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=f"Invalid authentication token: {str(e)}",
            headers={"WWW-Authenticate": "Bearer"}
        )

async def get_current_user(request: Request) -> Dict[str, Any]:
    """
    Get the authenticated user from the request.
    
    Args:
        request: The incoming request
        
    Returns:
        Dict with user information from JWT claims
        
    Raises:
        HTTPException: If not authenticated
    """
    return await supabase_auth(request)

def verify_admin_api_key(api_key: str = Security(admin_api_key_header)):
    """Verifies the admin API key provided in the header."""
    # Use cached getter
    correct_key = get_env("ADMIN_API_KEY")
    if not correct_key:
        logger.error("ADMIN_API_KEY not configured on server.") 