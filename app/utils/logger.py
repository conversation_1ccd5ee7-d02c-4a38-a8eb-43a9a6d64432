"""
Custom logging configuration module.
"""
import sys
import logging
from pathlib import Path
from typing import Optional, Union

# Configure default logging format
DEFAULT_FORMAT = "%(asctime)s | %(levelname)s | %(message)s"
DEFAULT_DATE_FORMAT = "%Y-%m-%d %H:%M:%S"
DEFAULT_LOG_LEVEL = logging.INFO


class Logger:
    """
    Custom logger wrapper class providing enhanced logging functionality.
    """
    
    def __init__(
        self,
        name: str,
        level: Union[int, str] = DEFAULT_LOG_LEVEL,
        log_format: str = DEFAULT_FORMAT,
        date_format: str = DEFAULT_DATE_FORMAT,
        log_file: Optional[Union[str, Path]] = None,
    ):
        """
        Initialize a new logger instance.
        
        Args:
            name: Logger name (typically __name__)
            level: Logging level (default: INFO)
            log_format: Log message format string
            date_format: Date format for log timestamps
            log_file: Optional path to log file
        """
        self.logger = logging.getLogger(name)
        self.logger.setLevel(level)
        self.logger.propagate = False
        
        # Create formatter
        formatter = logging.Formatter(log_format, date_format)
        
        # Create console handler
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setFormatter(formatter)
        self.logger.addHandler(console_handler)
        
        # Create file handler if log_file is provided
        if log_file:
            file_handler = logging.FileHandler(log_file)
            file_handler.setFormatter(formatter)
            self.logger.addHandler(file_handler)
    
    def debug(self, message: str, *args, **kwargs) -> None:
        """Log debug message."""
        self.logger.debug(message, *args, **kwargs)
    
    def info(self, message: str, *args, **kwargs) -> None:
        """Log info message."""
        self.logger.info(message, *args, **kwargs)
    
    def warning(self, message: str, *args, **kwargs) -> None:
        """Log warning message."""
        self.logger.warning(message, *args, **kwargs)
    
    def error(self, message: str, *args, **kwargs) -> None:
        """Log error message."""
        self.logger.error(message, *args, **kwargs)
    
    def critical(self, message: str, *args, **kwargs) -> None:
        """Log critical message."""
        self.logger.critical(message, *args, **kwargs)
    
    def exception(self, message: str, *args, **kwargs) -> None:
        """Log exception message with traceback."""
        self.logger.exception(message, *args, **kwargs)


def get_logger(
    name: str,
    level: Union[int, str] = DEFAULT_LOG_LEVEL,
    log_format: str = DEFAULT_FORMAT,
    date_format: str = DEFAULT_DATE_FORMAT,
    log_file: Optional[Union[str, Path]] = None,
) -> Logger:
    """
    Get a configured logger instance.
    
    Args:
        name: Logger name (typically __name__)
        level: Logging level
        log_format: Log message format string
        date_format: Date format for log timestamps
        log_file: Optional path to log file
        
    Returns:
        Configured Logger instance
    """
    return Logger(name, level, log_format, date_format, log_file)

def configure_root_logger(
    level: Union[int, str] = DEFAULT_LOG_LEVEL,
    log_format: str = DEFAULT_FORMAT,
    date_format: str = DEFAULT_DATE_FORMAT
) -> None:
    """
    Configure the root logger to ensure consistent formatting across all logs.
    
    Args:
        level: Logging level
        log_format: Log message format string
        date_format: Date format for log timestamps
    """
    # Configure the root logger
    root_logger = logging.getLogger()
    
    # Remove any existing handlers
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    
    # Set log level
    root_logger.setLevel(level)
    
    # Create formatter
    formatter = logging.Formatter(log_format, date_format)
    
    # Create console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(formatter)
    root_logger.addHandler(console_handler)
    
    # Set uvicorn and httpx loggers to use the same formatting
    for logger_name in ['uvicorn', 'uvicorn.error', 'uvicorn.access', 'httpx', 'logfire', 'logfire.client', 'logfire-api']:
        logger = logging.getLogger(logger_name)
        # Remove default handlers
        logger.handlers = []
        # Ensure it propagates to the root logger
        logger.propagate = True
    
    
    verbose_loggers = [
        # WeasyPrint and its components
        'weasyprint',
        'weasyprint.text',
        'weasyprint.text.fonts',
        'weasyprint.css',
        'weasyprint.html',
        'weasyprint.pdf',
        
        # Font processing (very verbose)
        'fontTools',
        'fontTools.ttLib',
        'fontTools.subset',
        'fontTools.subset.woff2',
        'fontTools.tfmLib',
        'fontTools.varLib',
        'fontTools.pens',
        'fontTools.otlLib',
        'fontTools.cffLib',
        
        # HTML/CSS processing
        'cssselect2',
        'tinycss2',
        'html5lib',
        'cairocffi',
        
        # Other libraries
        'markdown',
        'fpdf2',
        'PIL',
    ]

    for logger_name in verbose_loggers:
        logger = logging.getLogger(logger_name)
        logger.setLevel(logging.ERROR)
        logger.handlers = []
        logger.propagate = True
