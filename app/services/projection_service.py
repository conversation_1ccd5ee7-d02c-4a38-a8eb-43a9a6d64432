"""
Projection Service - Handles financial projections for properties.

This service provides rental income projections using AI-powered predictions
based on economic data and market trends.
"""
from typing import Dict
from dataclasses import dataclass

from pydantic_ai.models.openai import OpenAIModel
from pydantic_ai.providers.openai import OpenAIProvider

from app.utils.logger import get_logger
from app.utils.env_cache import get_env
from app.utils.economic_data import EconomicDataUtil
from app.models.ai import FinancialResponse, Source
from app.datasources.fred import get_fred_rental_data

logger = get_logger(__name__)

@dataclass
class ProjectionContext:
    """Context for projection calculations."""
    prop_id: str
    portfolio_id: str
    base_annual_income: float

class ProjectionService:
    """Service for generating financial projections."""
    
    def __init__(self, model_name: str = 'o4-mini'):
        """Initialize the projection service."""
        self.model = OpenAIModel(model_name, provider=OpenAIProvider(api_key=get_env('OPENAI_API_KEY')))
        self.economic_data = EconomicDataUtil()
    
    async def predict_rental_growth_rates(self, inflation_data: str) -> Dict[int, float]:
        """Use FRED rental price index data to predict rental growth rates for years 2-10."""
        try:
            # Get FRED rental price index data (CUUR0000SEHA - CPI for Rent of Primary Residence)
            fred_rental_data = get_fred_rental_data(
                series_id="CUUR0000SEHA",
                start_date="2019-01-01",
                end_date=None  # Current date
            )
            
            if not fred_rental_data:
                logger.warning("No FRED rental data available, using fallback rates")
                return {2: 0.035, 3: 0.032, 4: 0.030, 5: 0.028, 6: 0.026, 7: 0.025, 8: 0.024, 9: 0.023, 10: 0.022}
            
            # Calculate year-over-year growth rates from FRED data
            growth_rates_historical = []
            for i in range(1, len(fred_rental_data)):
                current_value = fred_rental_data[i]['value']
                previous_value = fred_rental_data[i-1]['value']
                if previous_value and current_value:
                    growth_rate = (current_value - previous_value) / previous_value
                    growth_rates_historical.append(growth_rate)
            
            if not growth_rates_historical:
                logger.warning("Could not calculate historical growth rates, using defaults")
                return {2: 0.035, 3: 0.032, 4: 0.030, 5: 0.028, 6: 0.026, 7: 0.025, 8: 0.024, 9: 0.023, 10: 0.022}
            
            # Calculate average growth rate from recent years (last 3-5 years)
            recent_growth = growth_rates_historical[-36:] if len(growth_rates_historical) >= 36 else growth_rates_historical  # Last 3 years of monthly data
            avg_growth_rate = sum(recent_growth) / len(recent_growth) if recent_growth else 0.03
            
            # Apply some variation and trend analysis
            # Assume slight decline in growth rates over time (conservative approach)
            base_rate = max(0.015, min(0.06, avg_growth_rate))  # Cap between 1.5% and 6%
            
            growth_rates = {
                2: base_rate,
                3: base_rate * 0.95,  # Slight decline
                4: base_rate * 0.90,  # Further decline
                5: base_rate * 0.85,  # Conservative long-term
                6: base_rate * 0.80,  # Year 6
                7: base_rate * 0.75,  # Year 7
                8: base_rate * 0.70,  # Year 8
                9: base_rate * 0.65,  # Year 9
                10: base_rate * 0.60  # Year 10 - most conservative
            }
            
            logger.info(f"FRED-based rental growth rates calculated: {growth_rates}")
            logger.info(f"Based on {len(recent_growth)} months of recent FRED rental price data")
            logger.info(f"Average historical growth rate: {avg_growth_rate*100:.2f}%")
            
            return growth_rates
                
        except Exception as e:
            logger.error(f"Error calculating FRED-based rental growth rates: {str(e)}")
            # Fallback to conservative defaults
            return {2: 0.035, 3: 0.032, 4: 0.030, 5: 0.028, 6: 0.026, 7: 0.025, 8: 0.024, 9: 0.023, 10: 0.022}

    async def calculate_rental_projections(self, context: ProjectionContext) -> Dict[str, FinancialResponse]:
        """Calculate rental income projections for years 2-10."""
        try:
            # Get inflation data
            inflation_data = self.economic_data.get_historical_inflation_data()
            
            # Get AI predictions for growth rates
            growth_rates = await self.predict_rental_growth_rates(inflation_data)
            
            projections = {}
            current_income = context.base_annual_income
            
            for year in range(2, 11):  # Years 2-10
                # Apply growth rate
                growth_rate = growth_rates.get(year, 0.03)  # Default 3%
                projected_income = current_income * (1 + growth_rate)
                
                # Create FinancialResponse for this year
                projection = FinancialResponse(
                    portfolio_id=context.portfolio_id,
                    prop_id=context.prop_id,
                    year=year,
                    sources=[
                        Source(
                            source="AI_prediction",
                            description="Projected based on historical inflation data and AI analysis",
                            url="https://api.openai.com"
                        )
                    ],
                    value=growth_rate * 100,  # Growth rate as percentage
                    unit="%",
                    money_value=projected_income,
                    money_unit="USD",
                    description=f"Projected annual rental income for year {year} with {growth_rate*100:.1f}% growth.",
                    additional_data=None  # No additional data for this projection
                )
                
                projections[f"year_{year}"] = projection
                current_income = projected_income
                
                logger.info(f"Year {year}: ${projected_income:.2f} (growth: {growth_rate*100:.1f}%)")
            
            return projections
            
        except Exception as e:
            logger.error(f"Error calculating rental projections: {str(e)}")
            return {} 