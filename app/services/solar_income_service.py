"""
Solar Income Service - Calculates potential income from solar installations.

This service analyzes property solar potential data and electricity rates
to calculate potential annual income from solar panels.
"""
import httpx
from dataclasses import dataclass
from typing import Dict, List, Optional, Any

from tavily import TavilyClient

from app.utils.logger import get_logger
from app.utils.env_cache import get_env
from app.models.ai import FinancialResponse, Source
from app.services.property_service import PropertyService

logger = get_logger(__name__)

@dataclass
class SolarIncomeContext:
    """Context for solar income calculations."""
    prop_id: str
    portfolio_id: str
    zipcode: Optional[str] = None
    city: Optional[str] = None
    state: Optional[str] = None

class SolarIncomeService:
    """Service for calculating solar income projections."""
    
    def __init__(self):
        """Initialize the solar income service."""
        self.tavily_client = TavilyClient(api_key=get_env('TAVILY_API_KEY'))
        self.property_service = PropertyService()
        self.google_maps_api_key = get_env('GOOGLE_MAPS_API_KEY')
    
    async def get_property_coordinates(self, prop_id: str) -> Optional[Dict[str, float]]:
        """Get property coordinates for Google Solar API."""
        try:
            # Try to geocode the address since we don't have direct coordinates
            property_data = await PropertyService.get_property(prop_id)
            if not property_data or not property_data.address_id:
                logger.warning(f"No property or address found for {prop_id}")
                return None
            
            # Get property address for geocoding
            address = await PropertyService.get_property_address(str(property_data.address_id))
            if not address:
                logger.warning(f"No address found for property {prop_id}")
                return None
            
            # Try to geocode the address
            full_address = f"{getattr(address, 'address', '')} {getattr(address, 'city', '')} {getattr(address, 'state', '')} {getattr(address, 'zip', '')}"
            coordinates = await self._geocode_address(full_address.strip())
            
            return coordinates
            
        except Exception as e:
            logger.error(f"Error getting property coordinates: {str(e)}")
            return None
    
    async def _geocode_address(self, address: str) -> Optional[Dict[str, float]]:
        """Geocode an address using Google Maps API."""
        try:
            if not self.google_maps_api_key:
                logger.warning("No Google Maps API key available for geocoding")
                return None
            
            async with httpx.AsyncClient() as client:
                response = await client.get(
                    "https://maps.googleapis.com/maps/api/geocode/json",
                    params={
                        "address": address,
                        "key": self.google_maps_api_key
                    }
                )
                
                if response.status_code == 200:
                    data = response.json()
                    if data.get('status') == 'OK' and data.get('results'):
                        location = data['results'][0]['geometry']['location']
                        return {
                            "lat": location['lat'],
                            "lng": location['lng']
                        }
                
                logger.warning(f"Geocoding failed for address: {address}")
                return None
                
        except Exception as e:
            logger.error(f"Error geocoding address: {str(e)}")
            return None
    
    async def get_google_solar_potential(self, prop_id: str) -> Optional[Dict[str, Any]]:
        """Get solar potential data from Google Solar API."""
        try:
            if not self.google_maps_api_key:
                logger.warning("No Google Maps API key available for Solar API")
                return None
            
            # Get property coordinates
            coordinates = await self.get_property_coordinates(prop_id)
            if not coordinates:
                logger.warning(f"Could not get coordinates for property {prop_id}")
                return None
            
            lat = coordinates['lat']
            lng = coordinates['lng']
            
            logger.info(f"Fetching solar potential from Google Solar API for coordinates: {lat}, {lng}")
            
            async with httpx.AsyncClient() as client:
                response = await client.get(
                    "https://solar.googleapis.com/v1/buildingInsights:findClosest",
                    params={
                        "location.latitude": lat,
                        "location.longitude": lng,
                        "requiredQuality": "HIGH",
                        "key": self.google_maps_api_key
                    }
                )
                
                if response.status_code == 200:
                    data = response.json()
                    logger.info(f"Google Solar API response received for property {prop_id}")
                    return data
                else:
                    logger.warning(f"Google Solar API request failed with status {response.status_code}")
                    return None
                    
        except Exception as e:
            logger.error(f"Error fetching Google Solar API data: {str(e)}")
            return None
    
    async def estimate_roof_area_from_property(self, prop_id: str) -> Optional[float]:
        """Estimate roof area from property data."""
        try:
            # Get property units to estimate building size
            units = await PropertyService.get_property_units(prop_id)
            if not units:
                logger.warning(f"No units found for property {prop_id}")
                return None
            
            # Calculate total square footage from units
            total_sqft = 0
            for unit in units:
                if hasattr(unit, 'sqft') and unit.sqft:
                    total_sqft += float(unit.sqft)
            
            if total_sqft > 0:
                # For multi-unit buildings, assume units stack vertically
                # Estimate roof area as roughly 1/2 to 1/3 of total unit area
                # This is a conservative estimate for multi-story buildings
                estimated_stories = max(1, len(units) // 4)  # Rough estimate of stories
                roof_area_sqft = total_sqft / max(1, estimated_stories)
                
                # Convert to square meters
                roof_area_m2 = roof_area_sqft * 0.092903  # 1 sqft = 0.092903 m2
                
                logger.info(f"Estimated roof area: {roof_area_m2:.2f} m2 from {len(units)} units, {total_sqft} total sqft, est. {estimated_stories} stories")
                return roof_area_m2
            
            # Fallback: use a default estimate based on number of units
            if len(units) > 0:
                # Assume average unit size of 1000 sqft if no data available
                estimated_total_sqft = len(units) * 1000
                estimated_stories = max(1, len(units) // 4)
                roof_area_sqft = estimated_total_sqft / estimated_stories
                roof_area_m2 = roof_area_sqft * 0.092903
                
                logger.info(f"Fallback estimation: {roof_area_m2:.2f} m2 from {len(units)} units (assumed 1000 sqft/unit)")
                return roof_area_m2
            
            logger.warning(f"Could not estimate building area for property {prop_id}")
            return None
            
        except Exception as e:
            logger.error(f"Error estimating roof area: {str(e)}")
            return None
    
    async def get_electricity_rates(self, zipcode: Optional[str], city: Optional[str], state: Optional[str]) -> Dict[str, Any]:
        """Get electricity rates for a location using Tavily search."""
        try:
            # Create search query
            location = ""
            if zipcode:
                location = f"ZIP code {zipcode}"
            elif city and state:
                location = f"{city}, {state}"
            else:
                logger.warning("No location information provided for electricity rates search")
                return {"rate_per_kwh": 0.12, "source": "default"}  # Default US average
            
            query = f"electricity rates kwh cost {location} utility company residential 2024"
            
            logger.info(f"Searching for electricity rates: {query}")
            
            # Search using Tavily
            response = self.tavily_client.search(
                query=query,
                search_depth="advanced",
                max_results=5
            )
            
            if not response or 'results' not in response:
                logger.warning("No electricity rate data found")
                return {"rate_per_kwh": 0.12, "source": "default"}
            
            # Extract electricity rate information
            rate_data = self._extract_electricity_rates(response['results'], location)
            logger.info(f"Found electricity rates: {rate_data}")
            
            return rate_data
            
        except Exception as e:
            logger.error(f"Error searching for electricity rates: {str(e)}")
            return {"rate_per_kwh": 0.12, "source": "default"}
    
    def _extract_electricity_rates(self, search_results: List[Dict], location: str) -> Dict[str, Any]:
        """Extract electricity rate information from search results."""
        try:
            # Look for rate information in search results
            for result in search_results:
                content = result.get('content', '').lower()
                
                # Common patterns for electricity rates
                import re
                
                # Look for patterns like "$0.12/kwh", "12 cents per kwh", "0.12 per kilowatt hour"
                patterns = [
                    r'\$?(\d+\.?\d*)\s*(?:cents?|¢)\s*(?:per\s*)?(?:kwh|kilowatt\s*hour)',
                    r'\$(\d+\.?\d*)\s*(?:per\s*)?(?:kwh|kilowatt\s*hour)',
                    r'(\d+\.?\d*)\s*cents?\s*(?:per\s*)?(?:kwh|kilowatt\s*hour)'
                ]
                
                for pattern in patterns:
                    matches = re.findall(pattern, content)
                    if matches:
                        try:
                            rate = float(matches[0])
                            # If rate is in cents, convert to dollars
                            if 'cent' in content.lower() or '¢' in content:
                                rate = rate / 100
                            
                            # Sanity check - typical US rates are between $0.05 and $0.40 per kWh
                            if 0.05 <= rate <= 0.40:
                                return {
                                    "rate_per_kwh": rate,
                                    "source": result.get('url', 'search_result'),
                                    "description": f"Electricity rate for {location}",
                                    "content_snippet": content[:200]
                                }
                        except ValueError:
                            continue
            
            # If no specific rate found, return regional average
            logger.warning(f"Could not extract specific rate from search results for {location}")
            return {"rate_per_kwh": 0.12, "source": "estimated_average"}
            
        except Exception as e:
            logger.error(f"Error extracting electricity rates: {str(e)}")
            return {"rate_per_kwh": 0.12, "source": "default"}
    
    async def get_solar_potential_data(self, prop_id: str) -> Optional[Dict[str, Any]]:
        """Get solar potential data for a property."""
        try:
            # First try: Get property solar potential from database
            solar_data = await self.property_service.get_property_solar_potential(prop_id)
            
            if solar_data:
                # Convert PropertySolarPotential model to dict
                if hasattr(solar_data, 'data'):
                    db_data = solar_data.data
                elif hasattr(solar_data, 'model_dump'):
                    db_data = solar_data.model_dump()
                else:
                    db_data = dict(solar_data)
                
                # Check if we have usable solar generation data
                if self._has_usable_solar_data(db_data):
                    logger.info(f"Using database solar potential data for property {prop_id}")
                    return db_data
            
            # Second try: Get solar potential data from Google Solar API
            logger.info(f"No usable database solar data for property {prop_id}, trying Google Solar API")
            google_solar_data = await self.get_google_solar_potential(prop_id)
            if google_solar_data:
                logger.info(f"Using Google Solar API data for property {prop_id}")
                return google_solar_data
            
            # Third try: Estimate based on property area and regional solar irradiance
            logger.info(f"No Google Solar API data available, estimating from property area for {prop_id}")
            estimated_data = await self._estimate_solar_potential_from_property(prop_id)
            if estimated_data:
                logger.info(f"Using estimated solar potential for property {prop_id}")
                return estimated_data
            
            logger.warning(f"No solar potential data could be obtained for property {prop_id}")
            return None
                
        except Exception as e:
            logger.error(f"Error getting solar potential data: {str(e)}")
            return None
    
    def _has_usable_solar_data(self, solar_data: Dict[str, Any]) -> bool:
        """Check if solar data contains usable generation information."""
        if not solar_data:
            return False
        
        # Check for direct generation data
        if any(key in solar_data for key in ['annual_generation', 'yearly_energy_dc_kwh']):
            return True
        
        # Check for area and irradiance data that can be used to calculate generation
        if 'max_array_area_m2' in solar_data and 'solar_irradiance_kwh_per_m2_per_year' in solar_data:
            return True
        
        return False
    
    async def _estimate_solar_potential_from_property(self, prop_id: str) -> Optional[Dict[str, Any]]:
        """Estimate solar potential based on property area and regional averages."""
        try:
            # Get estimated roof area
            roof_area_m2 = await self.estimate_roof_area_from_property(prop_id)
            if not roof_area_m2:
                return None
            
            # Use conservative estimates for solar potential
            # Average solar irradiance in US is about 1,200-1,800 kWh/m2/year
            # We'll use 1,400 kWh/m2/year as a conservative estimate
            solar_irradiance = 1400  # kWh/m2/year
            
            # Assume only 60% of roof area is usable for solar panels
            usable_roof_area = roof_area_m2 * 0.6
            
            return {
                "max_array_area_m2": usable_roof_area,
                "solar_irradiance_kwh_per_m2_per_year": solar_irradiance,
                "method": "property_area_estimation",
                "estimated": True
            }
            
        except Exception as e:
            logger.error(f"Error estimating solar potential from property: {str(e)}")
            return None
    
    def calculate_annual_solar_income(
        self, 
        solar_data: Dict[str, Any], 
        electricity_rate: float
    ) -> Dict[str, Any]:
        """Calculate annual income from solar installation."""
        try:
            # Extract relevant solar data
            annual_generation_kwh = 0
            method = "unknown"
            
            # Try to extract solar generation data from different sources
            if 'annual_generation' in solar_data:
                annual_generation_kwh = solar_data['annual_generation']
                method = "database_annual_generation"
            elif 'yearly_energy_dc_kwh' in solar_data:
                annual_generation_kwh = solar_data['yearly_energy_dc_kwh']
                method = "database_yearly_energy"
            elif 'solarPotential' in solar_data:
                # Google Solar API format
                solar_potential = solar_data['solarPotential']
                if 'yearlyEnergyDcKwh' in solar_potential:
                    annual_generation_kwh = solar_potential['yearlyEnergyDcKwh']
                    method = "google_solar_api"
                elif 'maxArrayAreaMeters2' in solar_potential and 'carbonOffsetFactorKgPerMwh' in solar_potential:
                    # Estimate from Google Solar API area data
                    panel_efficiency = 0.20  # 20% typical solar panel efficiency
                    area_m2 = solar_potential['maxArrayAreaMeters2']
                    # Use conservative solar irradiance estimate
                    irradiance = 1400  # kWh/m2/year conservative estimate
                    annual_generation_kwh = area_m2 * irradiance * panel_efficiency
                    method = "google_solar_api_estimated"
            elif 'max_array_area_m2' in solar_data and 'solar_irradiance_kwh_per_m2_per_year' in solar_data:
                # Estimate based on area and irradiance
                panel_efficiency = 0.20  # 20% typical solar panel efficiency
                area_m2 = solar_data['max_array_area_m2']
                irradiance = solar_data['solar_irradiance_kwh_per_m2_per_year']
                annual_generation_kwh = area_m2 * irradiance * panel_efficiency
                method = solar_data.get('method', 'area_irradiance_calculation')
            
            if annual_generation_kwh == 0:
                logger.warning("Could not determine solar generation potential from any data source")
                return {"annual_income": 0, "annual_generation_kwh": 0, "method": "insufficient_data"}
            
            # Calculate annual income
            # Assume net metering - sell back at retail rate
            annual_income = annual_generation_kwh * electricity_rate
            
            logger.info(f"Solar calculation ({method}): {annual_generation_kwh:.0f} kWh/year * ${electricity_rate:.3f}/kWh = ${annual_income:.2f}/year")
            
            return {
                "annual_income": annual_income,
                "annual_generation_kwh": annual_generation_kwh,
                "electricity_rate": electricity_rate,
                "method": method
            }
            
        except Exception as e:
            logger.error(f"Error calculating solar income: {str(e)}")
            return {"annual_income": 0, "annual_generation_kwh": 0, "method": "error"}
    
    async def calculate_solar_income_projections(self, context: SolarIncomeContext) -> Dict[str, FinancialResponse]:
        """Calculate solar income projections for years 1-5."""
        try:
            # Get solar potential data
            solar_data = await self.get_solar_potential_data(context.prop_id)
            if not solar_data:
                logger.warning(f"No solar data available for property {context.prop_id}")
                return {}
            
            # Get electricity rates
            rate_data = await self.get_electricity_rates(context.zipcode, context.city, context.state)
            electricity_rate = rate_data.get('rate_per_kwh', 0.12)
            
            # Calculate base solar income
            income_calc = self.calculate_annual_solar_income(solar_data, electricity_rate)
            base_income = income_calc.get('annual_income', 0)
            
            if base_income <= 0:
                logger.warning("No solar income potential calculated")
                return {}
            
            # Project for years 1-5 with electricity rate inflation
            projections = {}
            current_income = base_income
            
            # Assume 2-3% annual increase in electricity rates
            annual_rate_increase = 0.025
            
            for year in range(1, 6):  # Years 1-5
                if year > 1:
                    current_income = current_income * (1 + annual_rate_increase)
                
                projection = FinancialResponse(
                    portfolio_id=context.portfolio_id,
                    prop_id=context.prop_id,
                    year=year,
                    sources=[
                        Source(
                            source="solar_potential_analysis",
                            description="Calculated from property solar potential data and local electricity rates",
                            url=rate_data.get('source', 'tavily_search')
                        )
                    ],
                    value=current_income,
                    unit="USD",
                    money_value=current_income,
                    money_unit="USD",
                    description=f"Projected annual solar income for year {year} from solar panel installation.",
                    additional_data=None  # Additional metadata stored in description
                )
                
                projections[f"year_{year}"] = projection
                
                logger.info(f"Solar Year {year}: ${current_income:.2f}")
            
            return projections
            
        except Exception as e:
            logger.error(f"Error calculating solar income projections: {str(e)}")
            return {} 