"""
Repairs Analysis Service - Analyzes property condition and estimates repair costs.

This service uses property details, images, and market data to estimate
repair and maintenance costs for rental properties.
"""
import json
from dataclasses import dataclass
from typing import Dict, List, Optional, Any

from pydantic_ai import Agent, RunContext
from pydantic_ai.models.openai import OpenAIModel
from pydantic_ai.providers.openai import OpenAIProvider

from app.utils.logger import get_logger
from app.utils.env_cache import get_env
from app.models.ai import FinancialResponse, Source

logger = get_logger(__name__)

@dataclass
class RepairsAnalysisContext:
    """Context for repairs analysis calculations."""
    prop_id: str
    portfolio_id: str
    estimated_property_value: Optional[float] = None
    property_age: Optional[int] = None
    sqft: Optional[float] = None
    property_type: Optional[str] = None
    property_details: Optional[Dict[str, Any]] = None
    property_images: Optional[List[str]] = None

class RepairsAnalysisService:
    """Service for repairs and maintenance analysis."""
    
    def __init__(self, model_name: str = 'gpt-4o-mini'):
        """Initialize the repairs analysis service."""
        self.model = OpenAIModel(model_name, provider=OpenAIProvider(api_key=get_env('OPENAI_API_KEY')))
        
        # Create an agent for image analysis
        self.image_analysis_agent = Agent(
            self.model,
            deps_type=str,  # Property ID
        )

        @self.image_analysis_agent.system_prompt
        async def image_analysis_system_prompt(ctx: RunContext[str]) -> str:
            return """You are a professional property inspector and contractor with 20+ years experience.
            
            Analyze the provided property images and assess:
            1. Overall property condition (Excellent, Good, Fair, Poor)
            2. Visible repair needs (roof, siding, windows, foundation, etc.)
            3. Estimated severity of any issues (Minor, Moderate, Major)
            4. Approximate repair costs for identified issues
            5. Maintenance schedule recommendations
            
            Be conservative but realistic in your assessments. Focus on:
            - Structural elements
            - Exterior condition
            - Interior visible issues
            - HVAC, plumbing, electrical (if visible)
            - Flooring and finishes
            
            Return your analysis in JSON format with specific cost estimates and timelines."""
    
    async def get_property_details_and_images(self, prop_id: str) -> Dict[str, Any]:
        """Get property details and images from the database."""
        try:
            # Import here to avoid circular imports
            from app.services.property_service import PropertyService
            
            # Get property details
            property_data = await PropertyService.get_property(prop_id)
            
            if not property_data:
                logger.warning(f"No property found for {prop_id}")
                return {"details": None, "images": []}
            
            # Get property market data for images
            market_data = None
            if hasattr(property_data, 'market_data') and property_data.market_data:
                try:
                    market_data = json.loads(property_data.market_data) if isinstance(property_data.market_data, str) else property_data.market_data
                except (json.JSONDecodeError, TypeError):
                    logger.warning(f"Could not parse market data for property {prop_id}")
            
            # Extract images from market data
            images = []
            if market_data and isinstance(market_data, dict):
                images = market_data.get('images', [])
            
            # Get property units for additional details
            units = await PropertyService.get_property_units(prop_id)
            
            return {
                "details": {
                    "property_data": property_data,
                    "units": units,
                    "market_data": market_data
                },
                "images": images[:10]  # Limit to first 10 images for analysis
            }
            
        except Exception as e:
            logger.error(f"Error getting property details and images: {str(e)}")
            return {"details": None, "images": []}
    
    async def analyze_property_condition_from_images(self, images: List[str], prop_id: str) -> Dict[str, Any]:
        """Analyze property condition using AI image analysis."""
        try:
            if not images:
                logger.warning("No images available for property condition analysis")
                return {"condition_score": 3, "repair_items": [], "confidence": "low"}
            
            # Create prompt for image analysis
            analysis_prompt = f"""
            Analyze these property images for Property ID: {prop_id}
            
            Images to analyze: {len(images)} images
            
            Please provide a detailed assessment including:
            1. Overall condition rating (1-5, where 5 is excellent)
            2. Specific repair items needed with estimated costs
            3. Urgency level for each repair (Immediate, Short-term, Long-term)
            4. Property age estimate based on visual cues
            5. Overall maintenance requirements
            
            Format your response as JSON with the following structure:
            {{
                "condition_score": 1-5,
                "estimated_age": years,
                "repair_items": [
                    {{
                        "item": "description",
                        "cost_estimate": 0,
                        "urgency": "Immediate/Short-term/Long-term",
                        "category": "structural/cosmetic/mechanical"
                    }}
                ],
                "annual_maintenance_estimate": 0,
                "confidence": "high/medium/low"
            }}
            """
            analysis_prompt = analysis_prompt + ""
            
            # Run image analysis (Note: In a real implementation, you'd pass the actual images)
            # For now, we'll simulate the analysis
            logger.info(f"Analyzing {len(images)} images for property {prop_id}")
            
            # Simulate image analysis results
            # In reality, this would use the OpenAI Vision API or similar
            simulated_analysis = {
                "condition_score": 3,  # Fair condition
                "estimated_age": 25,
                "repair_items": [
                    {
                        "item": "Exterior paint touch-up",
                        "cost_estimate": 2500,
                        "urgency": "Short-term",
                        "category": "cosmetic"
                    },
                    {
                        "item": "Roof inspection and minor repairs",
                        "cost_estimate": 1500,
                        "urgency": "Short-term", 
                        "category": "structural"
                    },
                    {
                        "item": "HVAC maintenance",
                        "cost_estimate": 800,
                        "urgency": "Immediate",
                        "category": "mechanical"
                    }
                ],
                "annual_maintenance_estimate": 3000,
                "confidence": "medium"
            }
            
            logger.info(f"Image analysis completed: condition score {simulated_analysis['condition_score']}")
            return simulated_analysis
            
        except Exception as e:
            logger.error(f"Error analyzing property condition from images: {str(e)}")
            return {"condition_score": 3, "repair_items": [], "confidence": "low"}
    
    def calculate_age_based_repairs(self, property_age: int, property_value: float, sqft: float) -> Dict[str, Any]:
        """Calculate repair estimates based on property age and standard replacement cycles."""
        try:
            repair_schedule = {
                "roof": {"lifespan": 25, "cost_per_sqft": 8.0},
                "hvac": {"lifespan": 15, "cost_per_sqft": 12.0},
                "flooring": {"lifespan": 12, "cost_per_sqft": 6.0},
                "paint_exterior": {"lifespan": 8, "cost_per_sqft": 2.5},
                "paint_interior": {"lifespan": 6, "cost_per_sqft": 2.0},
                "appliances": {"lifespan": 10, "cost_per_sqft": 3.0},
                "windows": {"lifespan": 20, "cost_per_sqft": 15.0},
                "plumbing": {"lifespan": 30, "cost_per_sqft": 8.0},
                "electrical": {"lifespan": 30, "cost_per_sqft": 5.0}
            }
            
            upcoming_repairs = []
            annual_reserves_needed = 0
            
            for item, specs in repair_schedule.items():
                years_remaining = specs["lifespan"] - (property_age % specs["lifespan"])
                total_cost = sqft * specs["cost_per_sqft"]
                annual_reserve = total_cost / specs["lifespan"]
                annual_reserves_needed += annual_reserve
                
                if years_remaining <= 5:  # Within 5 years
                    upcoming_repairs.append({
                        "item": item,
                        "years_remaining": years_remaining,
                        "estimated_cost": total_cost,
                        "urgency": "Immediate" if years_remaining <= 1 else "Short-term" if years_remaining <= 3 else "Long-term"
                    })
            
            return {
                "upcoming_repairs": upcoming_repairs,
                "annual_reserves_needed": annual_reserves_needed,
                "total_upcoming_cost": sum(r["estimated_cost"] for r in upcoming_repairs),
                "property_age_factor": min(1.0 + (property_age - 20) * 0.02, 1.5) if property_age > 20 else 1.0
            }
            
        except Exception as e:
            logger.error(f"Error calculating age-based repairs: {str(e)}")
            return {"upcoming_repairs": [], "annual_reserves_needed": 0, "total_upcoming_cost": 0}
    
    async def calculate_repairs_projections(self, context: RepairsAnalysisContext) -> Dict[str, FinancialResponse]:
        """Calculate repair cost projections for years 1-10."""
        try:
            projections = {}
            
            # Get property details and images
            property_info = await self.get_property_details_and_images(context.prop_id)
            
            # Extract property characteristics
            total_sqft = context.sqft or 0
            if total_sqft == 0 and property_info["details"]:
                # Calculate from units
                for unit in property_info["details"].get("units", []):
                    if hasattr(unit, 'sqft') and unit.sqft:
                        total_sqft += unit.sqft
            
            property_age = context.property_age or 30  # Default assumption
            property_value = context.estimated_property_value or 0
            
            # Analyze images if available
            image_analysis = {}
            if property_info["images"]:
                image_analysis = await self.analyze_property_condition_from_images(
                    property_info["images"], 
                    context.prop_id
                )
            
            # Calculate age-based repairs if we have property data
            age_based_repairs = {}
            if total_sqft > 0:
                age_based_repairs = self.calculate_age_based_repairs(property_age, property_value, total_sqft)
            
            # Determine base repair costs
            base_repair_cost = 0
            if image_analysis:
                # Use image analysis results
                base_repair_cost = image_analysis.get("annual_maintenance_estimate", 0)
                # Add immediate repair items
                for item in image_analysis.get("repair_items", []):
                    if item.get("urgency") == "Immediate":
                        base_repair_cost += item.get("cost_estimate", 0)
            elif age_based_repairs:
                # Use age-based calculations
                base_repair_cost = age_based_repairs.get("annual_reserves_needed", 0)
            elif property_value > 0:
                # Fallback: Use industry standard (1-2% of property value)
                base_repair_cost = property_value * 0.015  # 1.5%
            
            if base_repair_cost <= 0:
                logger.warning("Could not determine repair costs for property")
                return {}
            
            # Create projections for years 1-10
            for year in range(1, 11):
                # Repairs typically increase over time
                annual_increase = 0.05  # 5% annual increase due to aging
                projected_repairs = base_repair_cost * ((1 + annual_increase) ** (year - 1))
                
                # Add major repairs for older properties
                if year == 3 and age_based_repairs:  # Year 3 gets larger repairs
                    projected_repairs += age_based_repairs.get("total_upcoming_cost", 0) * 0.3
                
                projection = FinancialResponse(
                    task_type="repairs",
                    portfolio_id=context.portfolio_id,
                    prop_id=context.prop_id,
                    year=year,
                    sources=[
                        Source(
                            source="ai_repairs_analysis",
                            description="AI-based repairs analysis considering property age, condition, and market factors",
                            url="ai_analysis"
                        )
                    ],
                    value=1.5 if property_value > 0 else 0,  # % of property value
                    unit="%",
                    money_value=-projected_repairs,  # Make negative since this is an expense
                    money_unit="USD",
                    description=f"Projected repairs for year {year}: {projected_repairs:.2f}",
                    additional_data=None
                )
                
                projections[f"year_{year}"] = projection
                logger.info(f"Repairs Year {year}: ${projected_repairs:.2f}")
            
            return projections
            
        except Exception as e:
            logger.error(f"Error calculating repairs projections: {str(e)}")
            return {} 