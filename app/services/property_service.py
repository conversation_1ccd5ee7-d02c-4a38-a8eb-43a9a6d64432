"""
Property service module for managing property-related operations.
"""
import re
import json
import uuid
import asyncio  
from datetime import datetime
from difflib import Se<PERSON><PERSON><PERSON><PERSON>
from typing import List, Optional, Dict, Any

from app.utils.logger import get_logger
from app.repository.db import get_db_client
from app.repository.cache import redis_cache
from app.agents.property import (
    UnitInput,
    UnitMatchInputList
)
from app.models.database import (
    Property, PropertyAddress, PropertyUnit, PropertyMarketData,
    PropertyEnvironmentalRisk, PropertySolarPotential, PropertyAirQuality,
    PointsOfInterest, PropertyFinancials, ZipDemographics
)

from app.agents.property import PropertyServiceAgent

from app.datasources.cherre import CherreDataService
from app.datasources.realtor import RealtorDataService

logger = get_logger(__name__)

# Cache settings
CACHING_ENABLED = True
CACHE_TTL = 86400  # 24 hours in seconds

@staticmethod   
def key_factory(full_address: str, data_type: str) -> str:
    """
    Generate a standardized cache key for a given address.
    
    Args:
        full_address: Full address string
        
    Returns:
        str: Formatted cache key
    """
    # Replace spaces with hyphens and remove any characters that could cause issues in Redis keys
    sanitized_address = full_address.replace(' ', '-')
    sanitized_address = ''.join(c for c in sanitized_address if c.isalnum() or c in '-_:')
    return f"prop_unit_full_info:{sanitized_address}:{data_type}"

class PropertyService:
    """
    Service for handling property-related operations.
    Uses the standardized database access pattern.
    """
    
    @classmethod
    async def get_properties(cls, portfolio_id: Optional[str] = None) -> List[Property]:
        """
        Get properties, optionally filtered by portfolio ID.
        
        Args:
            portfolio_id: Optional portfolio ID to filter by
            
        Returns:
            List[Property]: List of properties
        """
        try:
            # Get the client using the standard function
            client = await get_db_client()
            
            query = client.table("prop").select("*")
            
            if portfolio_id:
                query = query.eq("portfolio_id", portfolio_id)
                
            response = query.execute()
            
            properties = []
            for item in response.data:
                # Convert to Property model
                prop = Property(**item)
                
                # Get address
                addr_response = client.table("prop_addresses").select("*").eq("id", str(prop.address_id)).execute()
                if addr_response.data:
                    prop.address = PropertyAddress(**addr_response.data[0])
                
                # Get units
                units_response = client.table("prop_units").select("*").eq("prop_id", str(prop.id)).execute()
                if units_response.data:
                    prop.units = [PropertyUnit(**unit) for unit in units_response.data]
                
                # Get market data
                market_data_response = client.table("prop_market_data").select("*").eq("prop_id", str(prop.id)).execute()
                if market_data_response.data:
                    processed_market_data = []
                    for data in market_data_response.data:
                        # Convert string data to dict if needed
                        if isinstance(data.get("data"), str):
                            try:
                                data["data"] = json.loads(data["data"])
                            except json.JSONDecodeError:
                                logger.error(f"Error parsing market data JSON for property {prop.id}")
                                continue
                        
                        # Handle nullable fields
                        if data.get("beds") is None:
                            data["beds"] = None
                            
                        if data.get("baths") is None:
                            data["baths"] = None
                            
                        if data.get("sqft") is None:
                            data["sqft"] = None
                            
                        try:
                            processed_market_data.append(PropertyMarketData(**data))
                        except Exception as validation_error:
                            logger.error(f"Validation error for market data: {str(validation_error)}")
                            logger.error(f"Problem data: {data}")
                            continue
                    
                    prop.market_data = processed_market_data
                
                properties.append(prop)
            
            return properties
            
        except Exception as e:
            logger.error(f"Error fetching properties: {str(e)}")
            return []
    
    @classmethod
    async def get_property(cls, property_id: str) -> Optional[Property]:
        """
        Get a property by ID from the prop table only.
        
        Args:
            property_id: The ID of the property to retrieve
            
        Returns:
            Optional[Property]: The property if found, None otherwise
        """
        try:
            # Get the client using the standard function
            client = await get_db_client()
            
            # Get property from prop table only
            response = client.table("prop").select("*").eq("id", property_id).execute()
            
            if not response.data:
                logger.warning(f"Property not found: {property_id}")
                return None
                
            # Convert to Property model
            prop = Property(**response.data[0])
            return prop
            
        except Exception as e:
            logger.error(f"Error fetching property: {str(e)}")
            return None

    @classmethod
    async def get_property_by_address_id(cls, address_id: str) -> Optional[Property]:
        """
        Get a property by address ID with 1-day caching.

        Args:
            address_id: The address ID to retrieve property for

        Returns:
            Optional[Property]: The property if found, None otherwise
        """
        try:
            # Check cache first
            cache_key = f"property_by_address_id:{address_id}"
            cached_data = await redis_cache.get(cache_key)

            if cached_data:
                logger.debug(f"Cache hit for property with address_id: {address_id}")
                return Property(**cached_data)

            # Get the client using the standard function
            client = await get_db_client()

            # Get property from prop table by address_id
            response = client.table("prop").select("*").eq("address_id", address_id).execute()

            if not response.data:
                logger.warning(f"Property not found for address_id: {address_id}")
                return None

            # Convert to Property model
            prop = Property(**response.data[0])

            # Cache the result for 1 day (86400 seconds)
            await redis_cache.set(cache_key, prop.to_dict(), ttl=86400)
            logger.debug(f"Cached property for address_id: {address_id}")

            return prop

        except Exception as e:
            logger.error(f"Error fetching property by address_id: {str(e)}")
            return None

    @classmethod
    async def invalidate_property_cache_by_address_id(cls, address_id: str):
        """
        Invalidate all cached property data for a specific address ID.
        This includes both basic property data and property with relations.

        Args:
            address_id: The address ID to invalidate cache for

        Returns:
            bool: True if all caches were invalidated successfully, False otherwise
        """
        try:
            # Invalidate basic property cache
            cache_key = f"property_by_address_id:{address_id}"
            await redis_cache.delete(cache_key)
            

            logger.debug(f"Invalidated caches for address_id: {address_id}")
        except Exception as e:
            logger.error(f"Error invalidating caches for address_id {address_id}: {str(e)}")
            return False

    @classmethod
    async def get_address_id_by_property_id(cls, property_id: str) -> Optional[str]:
        """
        Get the address ID for a property by property ID.
        
        Args:
            property_id: The ID of the property

        Returns:
            Optional[str]: The address ID if found, None otherwise
        """
        try:
            # Get the client using the standard function
            client = await get_db_client()

            response = client.table("prop").select("address_id").eq("id", property_id).execute()
            if response.data:
                return response.data[0]["address_id"]
            return None
        except Exception as e:
            logger.error(f"Error fetching address ID: {str(e)}")
            return None
        
    @classmethod
    async def get_property_address(cls, address_id: str) -> Optional[PropertyAddress]:
        """
        Get property address by ID.
        
        Args:
            address_id: The ID of the address to retrieve
            
        Returns:
            Optional[PropertyAddress]: The address if found, None otherwise
        """
        try:
            # Get the client using the standard function
            client = await get_db_client()
            
            response = client.table("prop_addresses").select("*").eq("id", address_id).execute()
            if response.data:
                return PropertyAddress(**response.data[0])
            return None
        except Exception as e:
            logger.error(f"Error fetching property address: {str(e)}")
            return None

    @classmethod
    async def get_property_units(cls, property_id: str) -> List[PropertyUnit]:
        """
        Get property units.
        
        Args:
            property_id: The ID of the property
            
        Returns:
            List[PropertyUnit]: List of property units
        """
        try:
            # Get the client using the standard function
            client = await get_db_client()
            
            response = client.table("prop_units").select("*").eq("prop_id", property_id).execute()
            if response.data:
                return [PropertyUnit(**unit) for unit in response.data]
            return []
        except Exception as e:
            logger.error(f"Error fetching property units: {str(e)}")
            return []

    @classmethod
    async def get_property_market_data(cls, property_id: str) -> List[PropertyMarketData]:
        """
        Get property market data.
        
        Args:
            property_id: The ID of the property
            
        Returns:
            List[PropertyMarketData]: List of property market data
        """
        try:
            # Get the client using the standard function
            client = await get_db_client()
            
            response = client.table("prop_market_data").select("*").eq("prop_id", property_id).execute()
            if response.data:
                result = []
                for data in response.data:
                    # Check if data field is a string and convert it to dict if needed
                    if isinstance(data.get("data"), str):
                        try:
                            data["data"] = json.loads(data["data"])
                        except json.JSONDecodeError:
                            logger.error(f"Error parsing market data JSON for property {property_id}")
                            continue
                    
                    # Handle missing or null sqft value
                    if data.get("sqft") is None:
                        data["sqft"] = 0.0
                    
                    try:
                        result.append(PropertyMarketData(**data))
                    except Exception as validation_error:
                        logger.error(f"Validation error for market data: {str(validation_error)}")
                        # Log the problematic data for debugging
                        logger.error(f"Problem data: {data}")
                        continue
                
                return result
            return []
        except Exception as e:
            logger.error(f"Error fetching property market data: {str(e)}")
            return []

    @classmethod
    async def get_property_env_risk(cls, property_id: str) -> Optional[PropertyEnvironmentalRisk]:
        """
        Get environmental risk data.
        
        Args:
            property_id: The ID of the property
            
        Returns:
            Optional[PropertyEnvironmentalRisk]: Environmental risk data if found, None otherwise
        """
        try:
            # Get the client using the standard function
            client = await get_db_client()
            
            response = client.table("prop_env_risk").select("*").eq("prop_id", property_id).execute()
            if response.data:
                data = response.data[0]
                # Parse JSON data if needed
                if isinstance(data.get("data"), str):
                    try:
                        data["data"] = json.loads(data["data"])
                    except json.JSONDecodeError:
                        logger.error(f"Error parsing environmental risk JSON for property {property_id}")
                        return None
                return PropertyEnvironmentalRisk(**data)
            return None
        except Exception as e:
            logger.error(f"Error fetching environmental risk data: {str(e)}")
            return None

    @classmethod
    async def get_property_solar_potential(cls, property_id: str) -> Optional[PropertySolarPotential]:
        """
        Get solar potential data.
        
        Args:
            property_id: The ID of the property
            
        Returns:
            Optional[PropertySolarPotential]: Solar potential data if found, None otherwise
        """
        try:
            # Get the client using the standard function
            client = await get_db_client()
            
            response = client.table("prop_solar_potential").select("*").eq("prop_id", property_id).execute()
            if response.data:
                data = response.data[0]
                # Parse JSON data if needed
                if isinstance(data.get("data"), str):
                    try:
                        data["data"] = json.loads(data["data"])
                    except json.JSONDecodeError:
                        logger.error(f"Error parsing solar potential JSON for property {property_id}")
                        return None
                return PropertySolarPotential(**data)
            return None
        except Exception as e:
            logger.error(f"Error fetching solar potential data: {str(e)}")
            return None

    @classmethod
    async def get_property_air_quality(cls, property_id: str) -> Optional[PropertyAirQuality]:
        """
        Get air quality data.
        
        Args:
            property_id: The ID of the property
            
        Returns:
            Optional[PropertyAirQuality]: Air quality data if found, None otherwise
        """
        try:
            # Get the client using the standard function
            client = await get_db_client()
            
            response = client.table("prop_air_quality").select("*").eq("prop_id", property_id).execute()
            if response.data:
                data = response.data[0]
                # Parse JSON data if needed
                if isinstance(data.get("data"), str):
                    try:
                        data["data"] = json.loads(data["data"])
                    except json.JSONDecodeError:
                        logger.error(f"Error parsing air quality JSON for property {property_id}")
                        return None
                return PropertyAirQuality(**data)
            return None
        except Exception as e:
            logger.error(f"Error fetching air quality data: {str(e)}")
            return None

    @classmethod
    async def get_property_pois(cls, property_id: str) -> Optional[PointsOfInterest]:
        """
        Get points of interest data.
        
        Args:
            property_id: The ID of the property
            
        Returns:
            Optional[PointsOfInterest]: Points of interest data if found, None otherwise
        """
        try:
            # Get the client using the standard function
            client = await get_db_client()
            
            response = client.table("pois").select("*").eq("prop_id", property_id).execute()
            if response.data:
                data = response.data[0]
                # Parse JSON data if needed
                if isinstance(data.get("data"), str):
                    try:
                        data["data"] = json.loads(data["data"])
                    except json.JSONDecodeError:
                        logger.error(f"Error parsing POI JSON for property {property_id}")
                        return None
                return PointsOfInterest(**data)
            return None
        except Exception as e:
            logger.error(f"Error fetching points of interest data: {str(e)}")
            return None

    @classmethod
    async def get_property_financials(cls, property_id: str, year: int = 1) -> Optional[PropertyFinancials]:
        """
        Get property financials data.
        
        Args:
            property_id: The ID of the property
            
        Returns:
            Optional[PropertyFinancials]: Property financials data if found, None otherwise
        """
        try:
            # Get the client using the standard function
            client = await get_db_client()
            
            response = client.table("prop_financials").select("*").eq("prop_id", property_id).eq("year", year).execute()
            if response.data:
                return PropertyFinancials(**response.data[0])
            return None
        except Exception as e:
            logger.error(f"Error fetching property financials data: {str(e)}")
            return None
        
    @classmethod
    async def set_property_financials(cls, financials: PropertyFinancials, property_id: str, year: int = 1) -> None:
        """
        Set property financials data by updating only the fields that need to be changed.
        Invalidates cached property data since financials are part of the cached relations.

        Args:
            financials: PropertyFinancials object with the data to update
            property_id: The ID of the property
            year: The year for the financial data (default: 1)
        Returns:
            None
        """
        try:
            # Get the client using the standard function
            client = await get_db_client()
            
            # Check if record exists
            existing_record = await cls.get_property_financials(property_id, year)
            
            if existing_record:
                # Update only the fields that are not None in the financials object
                update_data = {}
                for field, value in financials.dict(exclude_unset=True, exclude_none=True).items():
                    if value is not None:
                        update_data[field] = value

                if update_data:
                    client.table("prop_financials").update(update_data).eq("prop_id", property_id).eq("year", year).execute()

                    # Invalidate cache since financials data changed
                    address_id = await cls.get_address_id_by_property_id(property_id)
                    if address_id:
                        await cls.invalidate_property_cache_by_address_id(address_id)
                        logger.debug(f"Invalidated property cache for address_id: {address_id} after financials update")
            else:
                # Insert new record
                client.table("prop_financials").insert(financials.dict(exclude_unset=True, exclude_none=True)).execute()

                # Invalidate cache since new financials data was added
                address_id = await cls.get_address_id_by_property_id(property_id)
                if address_id:
                    await cls.invalidate_property_cache_by_address_id(address_id)
                    logger.debug(f"Invalidated property cache for address_id: {address_id} after financials insert")

        except Exception as e:
            logger.error(f"Error setting property financials data: {str(e)}")

    @classmethod
    async def get_zip_demographics(cls, zipcode: str) -> Optional[ZipDemographics]:
        """
        Get zip demographics data.
        
        Args:
            zipcode: The zipcode to retrieve demographics for
            
        Returns:
            Optional[ZipDemographics]: Zip demographics data if found, None otherwise
        """
        try:
            # Get the client using the standard function
            client = await get_db_client()
            
            response = client.table("zip_demographics").select("*").eq("zipcode", zipcode).execute()
            if response.data:
                return ZipDemographics(**response.data[0])
            return None
        except Exception as e:
            logger.error(f"Error fetching zip demographics data: {str(e)}")
            return None

    @classmethod
    async def get_property_with_relations(cls, property_id: str) -> Optional[Dict[str, Any]]:
        """
        Get property with all related data.

        Args:
            property_id: The ID of the property

        Returns:
            Optional[Dict]: Dictionary containing property data with all relations if found, None otherwise
        """
        property_data = await cls.get_property(property_id)
        if not property_data:
            return None

        property_dict = property_data.dict()

        if property_data.address_id:
            address = await cls.get_property_address(str(property_data.address_id))
            if address:
                property_dict["prop_address"] = address

        units = await cls.get_property_units(property_id)
        if units:
            property_dict["prop_units"] = units

        market_data = await cls.get_property_market_data(property_id)
        if market_data:
            property_dict["prop_market_data"] = market_data

        env_risk = await cls.get_property_env_risk(property_id)
        if env_risk:
            property_dict["prop_env_risk"] = env_risk

        solar_potential = await cls.get_property_solar_potential(property_id)
        if solar_potential:
            property_dict["prop_solar_potential"] = solar_potential

        air_quality = await cls.get_property_air_quality(property_id)
        if air_quality:
            property_dict["prop_air_quality"] = air_quality

        pois = await cls.get_property_pois(property_id)
        if pois:
            property_dict["pois"] = pois

        financials = await cls.get_property_financials(property_id)
        if financials:
            property_dict["prop_financials"] = financials

        return property_dict
    

class PropertySearchService:
    """
    Service for searching properties.
    """

    @staticmethod
    def normalize_unit(unit_str: str) -> str:
        """
        Normalize unit string for comparison.
        Removes non-alphanumeric characters, lowercases, and strips spaces.

        Args:
            unit_str (str): The unit string to normalize.

        Returns:
            str: Normalized unit string.
        """
        if not unit_str:
            return ""
        return re.sub(r'[^a-zA-Z0-9]', '', unit_str).lower().strip()

    @staticmethod
    def best_unit_match(unit: str, market_units: Dict[str, Any]) -> Optional[str]:
        """
        Find the best matching unit key in market_units for the given unit string.
        Uses normalized string and SequenceMatcher for fuzzy matching.

        Args:
            unit (str): The unit string to match.
            market_units (Dict[str, Any]): Dictionary of market unit keys.

        Returns:
            Optional[str]: The best matching unit key if found, otherwise None.
        """
        if not unit or not market_units:
            return None
        norm_unit = PropertySearchService.normalize_unit(unit)
        best_score = 0.0
        best_key = None
        for key in market_units.keys():
            norm_key = PropertySearchService.normalize_unit(key)
            score = SequenceMatcher(None, norm_unit, norm_key).ratio()
            if score > best_score:
                best_score = score
                best_key = key
        if best_score > 0.7:
            return best_key
        return None

    @classmethod
    async def get_property_units_ai(
        cls, address: str, city: str, state: str, zipcode: str, task_token: str = "", task_key: str = "", force_refresh: bool = False
    ) -> Dict[str, Any]:
        """
        Get property units with AI and return market data in the new grouped structure.

        Returns:
            Dict[str, Any]: {
                "units": List[Dict[str, Any]],
                "market_data": Dict[str, Any],
                "refined_market_data": List[Dict[str, Any]]
            }
        """
        try:
            if not address or not zipcode:
                await redis_cache.set_task_queue(stage="data", token=task_token, status="failed", data={"error": "Missing address or zipcode"})
                return {"status": "failed", "token": task_token, "data": "Missing address or zipcode"}
            
            await redis_cache.set_task_queue(stage="data", token=task_token, status="processing")

            cache_key = key_factory(f"{address} {city} {state} {zipcode}", "units")
            ai_results_cache_key = key_factory(f"{address} {city} {state} {zipcode}", "ai_results")

            if CACHING_ENABLED and not force_refresh:
                cached_result = await redis_cache.get(cache_key)
                if cached_result:
                    await redis_cache.set_task_queue(stage="data", token=task_token, status="completed", data=cached_result)
                    return {"status": "completed", "token": task_token, "data": cached_result}

            # Build full address string
            address_parts = [part for part in [address, city, state, zipcode] if part]
            full_address = ",".join(address_parts)

            # Fetch units and market data concurrently
            results = await asyncio.gather(
                CherreDataService.fetch_multifamily_property_data(full_address),
                RealtorDataService.get_listing_history(full_address, address, zipcode),
                return_exceptions=True
            )
            units, market_data = (None, None)
            if results:
                units = results[0] if not isinstance(results[0], Exception) else None
                market_data = results[1] if not isinstance(results[1], Exception) else None

            if units and isinstance(units.get("units"), list):
                for unit in units["units"]:
                    unit["id"] = str(uuid.uuid4())

            if market_data and isinstance(market_data.get("market_data"), list):
                for market_unit in market_data["market_data"]:
                    market_unit["id"] = str(uuid.uuid4())

            units_list = [
                    UnitInput(
                        id=unit["id"],
                        data=unit.get("address", "")
                    ) for unit in units["units"]
                ]

            market_data_list = [
                    UnitInput(
                        id=market_unit["id"],
                        data=market_unit.get("unit", "")
                    ) for market_unit in market_data["market_data"]
                ]

            process_units_list = UnitMatchInputList(
                units=units_list,
                market_data=market_data_list
            )

            unit_list = None
            unit_match_list = None

            if CACHING_ENABLED and not force_refresh:
                cached_ai_results = await redis_cache.get(ai_results_cache_key)
                if cached_ai_results:
                    unit_list = cached_ai_results.get("unit_list")
                    unit_match_list = cached_ai_results.get("unit_match_list")

            if not unit_list or not unit_match_list:
                # Run both AI agent calls concurrently and handle exceptions individually.
                ai_tasks = [
                    PropertyServiceAgent.extract_unit_from_address(units_list),
                    PropertyServiceAgent.generate_unit_match_list(process_units_list)
                ]
                ai_results = await asyncio.gather(*ai_tasks, return_exceptions=True)

                unit_list = ai_results[0] if not isinstance(ai_results[0], Exception) else None
                unit_match_list = ai_results[1] if not isinstance(ai_results[1], Exception) else None

                await redis_cache.set(ai_results_cache_key, {
                    "unit_list": unit_list,
                    "unit_match_list": unit_match_list
                })
                await redis_cache.expire(ai_results_cache_key, CACHE_TTL)

            if isinstance(ai_results[0], Exception):
                logger.error(f"Error in extract_unit_from_address: {ai_results[0]}")
            if isinstance(ai_results[1], Exception):
                logger.error(f"Error in generate_unit_match_list: {ai_results[1]}")

            # Map unit["unit"] by matching unit["id"] to the corresponding unit in unit_list.units
            if unit_list and hasattr(unit_list, "units"):
                # Build a mapping from id to unit object for efficient lookup
                unit_id_map = {u.id: u for u in unit_list.units}
                for unit in units["units"]:
                    # Assign the extracted unit if found, else set to None
                    unit["unit"] = unit_id_map.get(unit["id"]).unit if unit_id_map.get(unit["id"]) else None

            # Transform unit_match_list.units (a list) into a dict keyed by market_data_unit_id
            # This enables efficient lookup and aligns with the required output structure.
            transformed_units = {}
            if unit_match_list and hasattr(unit_match_list, "units"):
                for match in unit_match_list.units:
                    # Ensure the object has the required attributes
                    if hasattr(match, "market_data_unit_id"):
                        transformed_units[match.market_data_unit_id] = match.dict() if hasattr(match, "dict") else dict(match)

            for market_unit in market_data["market_data"]:
                market_unit["unit_id"] = transformed_units.get(market_unit["id"], {}).get("units_unit_id", None)


            payload = {
                "timestamp": datetime.now().isoformat(),
                "units": units["units"],
                "market_data": market_data["market_data"],
            }

            if CACHING_ENABLED:
                await redis_cache.set(cache_key, payload)
                await redis_cache.expire(cache_key, CACHE_TTL)
                    
            await redis_cache.set_task_queue(stage="data", token=task_token, status="completed", data=payload)
            return {"status": "completed", "data": payload}
        
        except Exception as e:
            logger.error(f"Error fetching property units with AI: {str(e)}")
            await redis_cache.set_task_queue(stage="data", token=task_token, status="failed", data={"error": str(e)})
            return {"status": "failed", "token": task_token, "data": str(e)}
            

