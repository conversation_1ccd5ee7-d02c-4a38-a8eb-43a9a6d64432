"""
Vacancy Analysis Service - Calculates vacancy loss based on local vacancy rates and rental income projections.

This service analyzes local vacancy rates and calculates potential
vacancy losses for rental properties.
"""
import re
from typing import Dict, List, Optional, Any
from dataclasses import dataclass

from tavily import TavilyClient

from app.utils.logger import get_logger
from app.utils.env_cache import get_env
from app.models.ai import FinancialResponse, Source

logger = get_logger(__name__)

@dataclass
class VacancyAnalysisContext:
    """Context for vacancy analysis calculations."""
    prop_id: str
    portfolio_id: str
    zipcode: Optional[str] = None
    city: Optional[str] = None
    state: Optional[str] = None
    rental_projections: Optional[Dict[str, FinancialResponse]] = None

class VacancyAnalysisService:
    """Service for calculating vacancy loss projections."""
    
    def __init__(self):
        """Initialize the vacancy analysis service."""
        self.tavily_client = TavilyClient(api_key=get_env('TAVILY_API_KEY'))
    
    async def get_vacancy_rates(self, zipcode: Optional[str], city: Optional[str], state: Optional[str]) -> Dict[str, Any]:
        """Get vacancy rates for a location using Tavily search."""
        try:
            # Create search query
            location = ""
            if zipcode:
                location = f"ZIP code {zipcode}"
            elif city and state:
                location = f"{city}, {state}"
            else:
                logger.warning("No location information provided for vacancy rates search")
                return {"vacancy_rate": 0.05, "source": "default"}  # Default 5% vacancy
            
            query = f"rental vacancy rate {location} apartment housing market 2024 percentage"
            
            logger.info(f"Searching for vacancy rates: {query}")
            
            # Search using Tavily
            response = self.tavily_client.search(
                query=query,
                search_depth="advanced",
                max_results=5
            )
            
            if not response or 'results' not in response:
                logger.warning("No vacancy rate data found")
                return {"vacancy_rate": 0.05, "source": "default"}
            
            # Extract vacancy rate information
            vacancy_data = self._extract_vacancy_rates(response['results'], location)
            logger.info(f"Found vacancy rates: {vacancy_data}")
            
            return vacancy_data
            
        except Exception as e:
            logger.error(f"Error searching for vacancy rates: {str(e)}")
            return {"vacancy_rate": 0.05, "source": "default"}
    
    def _extract_vacancy_rates(self, search_results: List[Dict], location: str) -> Dict[str, Any]:
        """Extract vacancy rate information from search results."""
        try:
            # Look for vacancy rate information in search results
            for result in search_results:
                content = result.get('content', '').lower()
                
                # Common patterns for vacancy rates
                patterns = [
                    r'vacancy\s+rate[:\s]*(\d+\.?\d*)%',
                    r'(\d+\.?\d*)%\s+vacancy',
                    r'vacant[^\d]*(\d+\.?\d*)%',
                    r'(\d+\.?\d*)\s*percent\s+vacant',
                    r'vacancy[:\s]*(\d+\.?\d*)\s*percent'
                ]
                
                for pattern in patterns:
                    matches = re.findall(pattern, content)
                    if matches:
                        try:
                            rate = float(matches[0]) / 100  # Convert percentage to decimal
                            
                            # Sanity check - typical vacancy rates are between 1% and 30%
                            if 0.01 <= rate <= 0.30:
                                return {
                                    "vacancy_rate": rate,
                                    "source": result.get('url', 'search_result'),
                                    "description": f"Vacancy rate for {location}",
                                    "content_snippet": content[:200]
                                }
                        except ValueError:
                            continue
            
            # If no specific rate found, return regional average
            logger.warning(f"Could not extract specific vacancy rate from search results for {location}")
            return {"vacancy_rate": 0.05, "source": "estimated_average"}
            
        except Exception as e:
            logger.error(f"Error extracting vacancy rates: {str(e)}")
            return {"vacancy_rate": 0.05, "source": "default"}
    
    def calculate_vacancy_loss(
        self, 
        rental_income: float, 
        vacancy_rate: float
    ) -> Dict[str, Any]:
        """Calculate vacancy loss for a given rental income and vacancy rate."""
        try:
            # Vacancy loss should be negative since it reduces income
            vacancy_loss = -(rental_income * vacancy_rate)
            
            logger.info(f"Vacancy loss calculation: ${rental_income:.2f} * {vacancy_rate*100:.1f}% = ${vacancy_loss:.2f}")
            
            return {
                "vacancy_loss": vacancy_loss,
                "rental_income": rental_income,
                "vacancy_rate": vacancy_rate,
                "effective_income": rental_income + vacancy_loss  # Adding negative loss
            }
            
        except Exception as e:
            logger.error(f"Error calculating vacancy loss: {str(e)}")
            return {"vacancy_loss": 0, "rental_income": rental_income, "vacancy_rate": 0}
    
    async def calculate_vacancy_loss_projections(self, context: VacancyAnalysisContext) -> Dict[str, FinancialResponse]:
        """Calculate vacancy loss projections for years 1-10."""
        try:
            # Get vacancy rates
            vacancy_data = await self.get_vacancy_rates(context.zipcode, context.city, context.state)
            vacancy_rate = vacancy_data.get('vacancy_rate', 0.05)
            
            # Calculate vacancy loss for each year based on rental projections
            projections = {}
            
            if context.rental_projections:
                # Use provided rental projections to calculate vacancy loss for each year
                for year_key, rental_projection in context.rental_projections.items():
                    if rental_projection.money_value:
                        loss_calc = self.calculate_vacancy_loss(rental_projection.money_value, vacancy_rate)
                        vacancy_loss = loss_calc.get('vacancy_loss', 0)  # This is already negative
                        
                        year = rental_projection.year or 1
                        
                        projection = FinancialResponse(
                            portfolio_id=context.portfolio_id,
                            prop_id=context.prop_id,
                            year=year,
                            sources=[
                                Source(
                                    source="vacancy_rate_analysis",
                                    description="Calculated from local vacancy rates and rental income projections",
                                    url=vacancy_data.get('source', 'tavily_search')
                                )
                            ],
                            value=vacancy_rate * 100,  # Vacancy rate as percentage
                            unit="%",
                            money_value=vacancy_loss,  # This is negative as expected
                            money_unit="USD",
                            description=f"Projected vacancy loss for year {year} at {vacancy_rate*100:.1f}% vacancy rate.",
                            additional_data=None
                        )
                        
                        projections[f"year_{year}"] = projection
                        
                        logger.info(f"Vacancy Year {year}: ${vacancy_loss:.2f} loss ({vacancy_rate*100:.1f}% of ${rental_projection.money_value:.2f})")
            
            else:
                # No rental projections provided, can't calculate meaningful vacancy loss
                logger.warning("No rental income projections provided for vacancy loss calculation")
            
            return projections
            
        except Exception as e:
            logger.error(f"Error calculating vacancy loss projections: {str(e)}")
            return {} 