"""
Credit Risk Service - Calculates credit loss based on local creditworthiness data.

This service analyzes local creditworthiness and default rates to calculate 
potential credit losses for rental properties.
"""
import re
from dataclasses import dataclass
from typing import Dict, List, Optional, Any

from tavily import TavilyClient

from app.utils.logger import get_logger
from app.utils.env_cache import get_env
from app.models.ai import FinancialResponse, Source

logger = get_logger(__name__)

@dataclass
class CreditRiskContext:
    """Context for credit risk analysis calculations."""
    prop_id: str
    portfolio_id: str
    zipcode: Optional[str] = None
    city: Optional[str] = None
    state: Optional[str] = None
    rental_projections: Optional[Dict[str, FinancialResponse]] = None

class CreditRiskService:
    """Service for calculating credit loss projections."""
    
    def __init__(self):
        """Initialize the credit risk service."""
        self.tavily_client = TavilyClient(api_key=get_env('TAVILY_API_KEY'))
    
    async def get_credit_risk_data(self, zipcode: Optional[str], city: Optional[str], state: Optional[str]) -> Dict[str, Any]:
        """Get credit risk and default rate data for a location using Tavily search."""
        try:
            # Create search query
            location = ""
            if zipcode:
                location = f"ZIP code {zipcode}"
            elif city and state:
                location = f"{city}, {state}"
            else:
                logger.warning("No location information provided for credit risk search")
                return {"credit_loss_rate": 0.02, "source": "default"}  # Default 2% credit loss
            
            # Search for multiple credit-related metrics
            queries = [
                f"tenant default rate {location} rental eviction 2024",
                f"credit score average {location} creditworthiness demographics",
                f"bad debt collection rate {location} rental property"
            ]
            
            combined_results = []
            for query in queries:
                logger.info(f"Searching for credit data: {query}")
                
                response = self.tavily_client.search(
                    query=query,
                    search_depth="basic",
                    max_results=3
                )
                
                if response and 'results' in response:
                    combined_results.extend(response['results'])
            
            if not combined_results:
                logger.warning("No credit risk data found")
                return {"credit_loss_rate": 0.02, "source": "default"}
            
            # Extract credit risk information
            credit_data = self._extract_credit_risk_data(combined_results, location)
            logger.info(f"Found credit risk data: {credit_data}")
            
            return credit_data
            
        except Exception as e:
            logger.error(f"Error searching for credit risk data: {str(e)}")
            return {"credit_loss_rate": 0.02, "source": "default"}
    
    def _extract_credit_risk_data(self, search_results: List[Dict], location: str) -> Dict[str, Any]:
        """Extract credit risk information from search results."""
        try:
            default_rates = []
            credit_scores = []
            bad_debt_rates = []
            
            # Look for various credit-related metrics in search results
            for result in search_results:
                content = result.get('content', '').lower()
                
                # Patterns for default rates
                default_patterns = [
                    r'default\s+rate[:\s]*(\d+\.?\d*)%',
                    r'eviction\s+rate[:\s]*(\d+\.?\d*)%',
                    r'(\d+\.?\d*)%\s+default',
                    r'(\d+\.?\d*)\s*percent\s+default'
                ]
                
                # Patterns for credit scores
                credit_patterns = [
                    r'credit\s+score[:\s]*(\d{3})',
                    r'average\s+credit[:\s]*(\d{3})',
                    r'median\s+credit[:\s]*(\d{3})'
                ]
                
                # Patterns for bad debt rates
                bad_debt_patterns = [
                    r'bad\s+debt[:\s]*(\d+\.?\d*)%',
                    r'collection\s+rate[:\s]*(\d+\.?\d*)%',
                    r'(\d+\.?\d*)%\s+bad\s+debt'
                ]
                
                # Extract default rates
                for pattern in default_patterns:
                    matches = re.findall(pattern, content)
                    for match in matches:
                        try:
                            rate = float(match)
                            if 0.1 <= rate <= 50:  # Reasonable range for default rates
                                default_rates.append(rate / 100)
                        except ValueError:
                            continue
                
                # Extract credit scores
                for pattern in credit_patterns:
                    matches = re.findall(pattern, content)
                    for match in matches:
                        try:
                            score = float(match)
                            if 300 <= score <= 850:  # Valid credit score range
                                credit_scores.append(score)
                        except ValueError:
                            continue
                
                # Extract bad debt rates
                for pattern in bad_debt_patterns:
                    matches = re.findall(pattern, content)
                    for match in matches:
                        try:
                            rate = float(match)
                            if 0.1 <= rate <= 20:  # Reasonable range for bad debt rates
                                bad_debt_rates.append(rate / 100)
                        except ValueError:
                            continue
            
            # Calculate composite credit loss rate
            credit_loss_rate = self._calculate_credit_loss_rate(default_rates, credit_scores, bad_debt_rates)
            
            return {
                "credit_loss_rate": credit_loss_rate,
                "source": "tavily_search_composite",
                "description": f"Credit risk analysis for {location}",
                "default_rates_found": len(default_rates),
                "credit_scores_found": len(credit_scores),
                "bad_debt_rates_found": len(bad_debt_rates)
            }
            
        except Exception as e:
            logger.error(f"Error extracting credit risk data: {str(e)}")
            return {"credit_loss_rate": 0.02, "source": "default"}
    
    def _calculate_credit_loss_rate(self, default_rates: List[float], credit_scores: List[float], bad_debt_rates: List[float]) -> float:
        """Calculate composite credit loss rate from various metrics."""
        try:
            loss_estimates = []
            
            # Use default rates directly
            if default_rates:
                avg_default_rate = sum(default_rates) / len(default_rates)
                loss_estimates.append(avg_default_rate)
            
            # Convert credit scores to loss estimates
            if credit_scores:
                avg_credit_score = sum(credit_scores) / len(credit_scores)
                # Higher credit scores = lower loss rates
                # Scale from 850 (0.5% loss) to 300 (8% loss)
                score_based_loss = max(0.005, 0.08 - (avg_credit_score - 300) / 550 * 0.075)
                loss_estimates.append(score_based_loss)
            
            # Use bad debt rates with some adjustment
            if bad_debt_rates:
                avg_bad_debt_rate = sum(bad_debt_rates) / len(bad_debt_rates)
                # Bad debt rates might be higher than actual loss rates
                adjusted_rate = avg_bad_debt_rate * 0.7  # Assume 70% of bad debt is actual loss
                loss_estimates.append(adjusted_rate)
            
            if loss_estimates:
                # Weight different estimates (favor default rates if available)
                if default_rates:
                    # Give more weight to actual default rates
                    weights = [2.0 if i < len(default_rates) else 1.0 for i in range(len(loss_estimates))]
                    weighted_avg = sum(est * w for est, w in zip(loss_estimates, weights)) / sum(weights)
                else:
                    weighted_avg = sum(loss_estimates) / len(loss_estimates)
                
                # Cap the loss rate at reasonable bounds
                final_rate = max(0.005, min(0.10, weighted_avg))  # Between 0.5% and 10%
                
                logger.info(f"Credit loss rate calculation: estimates={loss_estimates}, final={final_rate*100:.2f}%")
                return final_rate
            
            # No data found, use default
            return 0.02  # 2% default
            
        except Exception as e:
            logger.error(f"Error calculating credit loss rate: {str(e)}")
            return 0.02
    
    def calculate_credit_loss(
        self, 
        rental_income: float, 
        credit_loss_rate: float
    ) -> Dict[str, Any]:
        """Calculate credit loss for a given rental income and credit loss rate."""
        try:
            # Credit loss should be negative since it reduces income
            credit_loss = -(rental_income * credit_loss_rate)
            
            logger.info(f"Credit loss calculation: ${rental_income:.2f} * {credit_loss_rate*100:.2f}% = ${credit_loss:.2f}")
            
            return {
                "credit_loss": credit_loss,
                "rental_income": rental_income,
                "credit_loss_rate": credit_loss_rate,
                "effective_income": rental_income + credit_loss  # Adding negative loss
            }
            
        except Exception as e:
            logger.error(f"Error calculating credit loss: {str(e)}")
            return {"credit_loss": 0, "rental_income": rental_income, "credit_loss_rate": 0}
    
    async def calculate_credit_loss_projections(self, context: CreditRiskContext) -> Dict[str, FinancialResponse]:
        """Calculate credit loss projections for years 1-5."""
        try:
            # Get credit risk data
            credit_data = await self.get_credit_risk_data(context.zipcode, context.city, context.state)
            credit_loss_rate = credit_data.get('credit_loss_rate', 0.02)
            
            # Calculate credit loss for each year based on rental projections
            projections = {}
            
            if context.rental_projections:
                # Use provided rental projections to calculate credit loss for each year
                for year_key, rental_projection in context.rental_projections.items():
                    if rental_projection.money_value:
                        loss_calc = self.calculate_credit_loss(rental_projection.money_value, credit_loss_rate)
                        credit_loss = loss_calc.get('credit_loss', 0)  # This is already negative
                        
                        year = rental_projection.year or 1
                        
                        projection = FinancialResponse(
                            portfolio_id=context.portfolio_id,
                            prop_id=context.prop_id,
                            year=year,
                            sources=[
                                Source(
                                    source="credit_risk_analysis",
                                    description="Calculated from local creditworthiness data and default rates",
                                    url=credit_data.get('source', 'tavily_search')
                                )
                            ],
                            value=credit_loss_rate * 100,  # Credit loss rate as percentage
                            unit="%",
                            money_value=credit_loss,  # This is negative as expected
                            money_unit="USD",
                            description=f"Projected credit loss for year {year} at {credit_loss_rate*100:.2f}% loss rate.",
                            additional_data=None
                        )
                        
                        projections[f"year_{year}"] = projection
                        
                        logger.info(f"Credit Loss Year {year}: ${credit_loss:.2f} loss ({credit_loss_rate*100:.2f}% of ${rental_projection.money_value:.2f})")
            
            else:
                # No rental projections provided, can't calculate meaningful credit loss
                logger.warning("No rental income projections provided for credit loss calculation")
            
            return projections
            
        except Exception as e:
            logger.error(f"Error calculating credit loss projections: {str(e)}")
            return {} 