"""
Insurance Service - Calculates property insurance expenses based on local rates and property value.

This service analyzes local insurance rates and uses property value to estimate
insurance costs for rental properties.
"""

import re
from dataclasses import dataclass
from typing import Dict, List, Optional, Any

from tavily import TavilyClient

from app.utils.logger import get_logger
from app.utils.env_cache import get_env
from app.models.ai import FinancialResponse, Source

logger = get_logger(__name__)

@dataclass
class InsuranceContext:
    """Context for insurance calculations."""
    prop_id: str
    portfolio_id: str
    zipcode: Optional[str] = None
    city: Optional[str] = None
    state: Optional[str] = None
    estimated_property_value: Optional[float] = None
    property_type: Optional[str] = None

class InsuranceService:
    """Service for property insurance calculations."""
    
    def __init__(self):
        """Initialize the insurance service."""
        self.tavily_client = TavilyClient(api_key=get_env('TAVILY_API_KEY'))
    
    async def get_local_insurance_rates(self, zipcode: Optional[str], city: Optional[str], state: Optional[str]) -> Dict[str, Any]:
        """Get local insurance rates using Tavily search."""
        try:
            # Create search query
            location = ""
            if zipcode:
                location = f"ZIP code {zipcode}"
            elif city and state:
                location = f"{city}, {state}"
            else:
                logger.warning("No location information provided for insurance rate search")
                return {"insurance_rate": 0.007, "source": "default"}  # Default 0.7% of property value
            
            query = f"property insurance cost rate {location} landlord insurance rental property 2024"
            
            logger.info(f"Searching for insurance rates: {query}")
            
            # Search using Tavily
            response = self.tavily_client.search(
                query=query,
                search_depth="advanced",
                max_results=5
            )
            
            if not response or 'results' not in response:
                logger.warning("No insurance rate data found")
                return {"insurance_rate": 0.007, "source": "default"}
            
            # Extract insurance rate information
            insurance_data = self._extract_insurance_rates(response['results'], location)
            logger.info(f"Found insurance rates: {insurance_data}")
            
            return insurance_data
            
        except Exception as e:
            logger.error(f"Error searching for insurance rates: {str(e)}")
            return {"insurance_rate": 0.007, "source": "default"}
    
    def _extract_insurance_rates(self, search_results: List[Dict], location: str) -> Dict[str, Any]:
        """Extract insurance rate information from search results."""
        try:
            insurance_costs = []
            property_values = []
            
            # Look for insurance cost information in search results
            for result in search_results:
                content = result.get('content', '').lower()
                
                # Patterns for insurance costs per year
                cost_patterns = [
                    r'insurance[:\s]*\$(\d{1,4})[,\s]*per\s+year',
                    r'annual\s+insurance[:\s]*\$(\d{1,4})',
                    r'\$(\d{1,4})[,\s]*per\s+year\s+insurance',
                    r'insurance\s+cost[:\s]*\$(\d{1,4})'
                ]
                
                # Patterns for property values
                value_patterns = [
                    r'\$(\d{2,3})[,\s]*000\s+home',
                    r'\$(\d{3})[,\s]*k\s+property',
                    r'property\s+value[:\s]*\$(\d{2,3})[,\s]*000'
                ]
                
                # Extract insurance costs
                for pattern in cost_patterns:
                    matches = re.findall(pattern, content)
                    for match in matches:
                        try:
                            cost = float(match.replace(',', ''))
                            # Reasonable range for annual insurance: $500-$5000
                            if 500 <= cost <= 5000:
                                insurance_costs.append(cost)
                        except ValueError:
                            continue
                
                # Extract property values (to calculate rates)
                for pattern in value_patterns:
                    matches = re.findall(pattern, content)
                    for match in matches:
                        try:
                            if 'k' in pattern:
                                value = float(match.replace(',', '')) * 1000
                            else:
                                value = float(match.replace(',', '')) * 1000
                            
                            # Reasonable range for property values: $100k-$2M
                            if 100000 <= value <= 2000000:
                                property_values.append(value)
                        except ValueError:
                            continue
            
            # Calculate insurance rates if we have both costs and values
            if insurance_costs and property_values:
                avg_cost = sum(insurance_costs) / len(insurance_costs)
                avg_value = sum(property_values) / len(property_values)
                calculated_rate = avg_cost / avg_value
                
                return {
                    "insurance_rate": calculated_rate,
                    "source": "tavily_search_calculated",
                    "description": f"Insurance rate for {location}",
                    "annual_cost_range": f"${min(insurance_costs):.0f}-${max(insurance_costs):.0f}",
                    "content_snippet": search_results[0].get('content', '')[:200] if search_results else ""
                }
            
            # Fallback: use industry averages based on location
            if insurance_costs:
                avg_cost = sum(insurance_costs) / len(insurance_costs)
                # Estimate rate based on average home value
                estimated_home_value = 300000  # Average US home value
                estimated_rate = avg_cost / estimated_home_value
                
                return {
                    "insurance_rate": estimated_rate,
                    "source": "tavily_search_estimated",
                    "description": f"Estimated insurance rate for {location}",
                    "annual_cost_found": f"${avg_cost:.0f}",
                    "content_snippet": search_results[0].get('content', '')[:200] if search_results else ""
                }
            
            # No specific data found, return default
            logger.warning(f"Could not extract specific insurance rates from search results for {location}")
            return {"insurance_rate": 0.007, "source": "default_fallback"}
            
        except Exception as e:
            logger.error(f"Error extracting insurance rates: {str(e)}")
            return {"insurance_rate": 0.007, "source": "default"}
    
    def calculate_insurance_cost(
        self, 
        property_value: float, 
        insurance_rate: float
    ) -> Dict[str, Any]:
        """Calculate insurance cost based on property value and insurance rate."""
        try:
            annual_insurance = property_value * insurance_rate
            
            logger.info(f"Insurance calculation: ${property_value:.2f} * {insurance_rate*100:.3f}% = ${annual_insurance:.2f}")
            
            return {
                "annual_insurance": annual_insurance,
                "property_value": property_value,
                "insurance_rate": insurance_rate,
                "calculation_method": "rate_based"
            }
            
        except Exception as e:
            logger.error(f"Error calculating insurance cost: {str(e)}")
            return {"annual_insurance": 0, "property_value": property_value, "insurance_rate": insurance_rate}
    
    async def calculate_insurance_projections(self, context: InsuranceContext) -> Dict[str, FinancialResponse]:
        """Calculate insurance projections for years 1-5."""
        try:
            projections = {}
            
            # Get local insurance rates
            insurance_data = await self.get_local_insurance_rates(context.zipcode, context.city, context.state)
            insurance_rate = insurance_data.get('insurance_rate', 0.007)
            
            # Use estimated property value
            property_value = context.estimated_property_value or 0
            
            if property_value <= 0:
                logger.warning("No property value data for insurance calculation")
                return {}
            
            # Calculate base insurance cost
            insurance_calc = self.calculate_insurance_cost(property_value, insurance_rate)
            base_insurance = insurance_calc.get('annual_insurance', 0)
            
            if base_insurance > 0:
                # Project with modest annual increase (typically 3-5% for insurance)
                annual_increase = 0.04  # 4% annual increase
                
                for year in range(1, 6):
                    projected_insurance = base_insurance * ((1 + annual_increase) ** (year - 1))
                    
                    projection = FinancialResponse(
                        task_type="insurance",
                        portfolio_id=context.portfolio_id,
                        prop_id=context.prop_id,
                        year=year,
                        sources=[
                            Source(
                                source="insurance_rate_analysis",
                                description="Calculated from local insurance rates and property value",
                                url=insurance_data.get('source', 'tavily_search')
                            )
                        ],
                        value=insurance_rate * 100,  # Insurance rate as percentage of property value
                        unit="%",
                        money_value=-projected_insurance,  # Make negative since this is an expense
                        money_unit="USD",
                        description=f"Projected property insurance for year {year} at {insurance_rate*100:.3f}% of property value.",
                        additional_data=None
                    )
                    
                    projections[f"year_{year}"] = projection
                    logger.info(f"Insurance Year {year}: ${projected_insurance:.2f}")
            
            return projections
            
        except Exception as e:
            logger.error(f"Error calculating insurance projections: {str(e)}")
            return {} 