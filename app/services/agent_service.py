import os
import json
import uuid
import asyncio
from datetime import datetime
from typing import List, Optional

from fastapi import WebSocket
from dotenv import load_dotenv
from pydantic import BaseModel, Field
from pydantic_ai import Agent, RunContext, ImageUrl

from app.utils.logger import get_logger
from app.models.prompts import AgentPrompts
from app.repository.db import get_db_client
from app.services.document_service import document_service
from app.models.message import Message, MessageContent, MessageRole

# Load environment variables
load_dotenv()

logger = get_logger(__name__)
openai_api_key = os.getenv("OPENAI_API_KEY")
if not openai_api_key:
    logger.error("OPENAI_API_KEY not found in environment variables")

async def send_websocket_update(websocket: Optional[WebSocket], message: str, message_id: str = None) -> None:
    """
    Send a websocket update if available.
    
    Args:
        websocket: Optional websocket connection
        message: Short update message (3-5 words)
    """
    if websocket:
        try:
            update_message = json.dumps({
                "id": message_id,
                "role": "update",
                "content": {"text": message},
                "timestamp": datetime.now().isoformat()
            })
            await websocket.send_text(update_message)
            logger.info(f"Sent update: {update_message}")
        except Exception as e:
            logger.error(f"Error sending websocket update: {str(e)}")

async def rename_chat_background(chat_id: str) -> None:
    """
    Background task to rename a chat based on its message content.
    This runs asynchronously without blocking other operations.
    
    Args:
        chat_id: The ID of the chat to rename
    """
    try:
        from app.services.chat_service import chat_service
        from app.services.ai_service import ai_service
        
        logger.info(f"Starting background chat rename for chat_id: {chat_id}")
        
        # Get recent messages for name generation
        messages = await chat_service.get_recent_messages_for_naming(chat_id, limit=10)
        
        if not messages:
            logger.info(f"No messages found for chat {chat_id}, skipping rename")
            return
        
        # Generate new chat name
        new_name = await ai_service.generate_chat_name(messages, max_length=30)
        
        # Update chat name
        success = await chat_service.update_chat_name(chat_id, new_name)
        
        if success:
            logger.info(f"Successfully renamed chat {chat_id} to: {new_name}")
        else:
            logger.warning(f"Failed to rename chat {chat_id}")
            
    except Exception as e:
        logger.error(f"Error in background chat rename for {chat_id}: {str(e)}")

# Define the dependency type for the agent
class AgentDependencies(BaseModel):
    """Dependencies for the PydanticAI agent"""
    portfolio_id: Optional[str] = Field(None, description="Portfolio ID for context")
    chat_history: List[Message] = Field(default_factory=list, description="Chat history")
    websocket: Optional[WebSocket] = Field(None, description="Websocket connection")
    message_id: Optional[str] = Field(None, description="Message ID for update")
    
    model_config = {"arbitrary_types_allowed": True}

class AgentService:
    """
    Service responsible for handling agent-based operations using PydanticAI agents.
    """
    def __init__(self, model_name: str = "gpt-4o"):
        self.model_name = model_name
        self._init_agent()
        logger.info(f"Initialized AgentService with model: {model_name}")

    def _init_agent(self) -> None:
        """Initialize the PydanticAI agent with tools and system prompt"""
        self.agent = Agent(
            name="Intel Chat Agent",
            model=self.model_name,
            deps_type=AgentDependencies,
            output_type=str,
            system_prompt=AgentPrompts().main_agent_prompts()
        )

        # Register tools with the agent
        self.agent.tool(self.retrieve_documents)
        self.agent.tool(self.get_property_info)
        self.agent.tool(self.generate_direct_response)

    async def should_rename_chat(self, chat_id: str) -> bool:
        """
        Check if a chat should be renamed based on user message count.
        Renames every 3rd user message.
        
        Args:
            chat_id: The ID of the chat to check
            
        Returns:
            bool: True if chat should be renamed
        """
        try:
            from app.services.chat_service import chat_service
            
            user_message_count = await chat_service.get_user_message_count(chat_id)
            
            # Rename on 3rd, 6th, 9th, etc. user message
            return user_message_count > 0 and user_message_count % 3 == 0
            
        except Exception as e:
            logger.error(f"Error checking if chat should be renamed: {str(e)}")
            return False

    async def trigger_chat_rename_if_needed(self, chat_id: str) -> None:
        """
        Trigger background chat rename if conditions are met.
        This is completely asynchronous and won't block other operations.
        
        Args:
            chat_id: The ID of the chat to potentially rename
        """
        try:
            if await self.should_rename_chat(chat_id):
                # Create background task
                asyncio.create_task(rename_chat_background(chat_id))
                logger.info(f"Triggered background rename for chat {chat_id}")
        except Exception as e:
            logger.error(f"Error triggering chat rename: {str(e)}")

    @staticmethod
    async def retrieve_documents(ctx: RunContext[AgentDependencies], query: str) -> str:
        """
        Retrieve relevant documents based on the query.
        
        Args:
            ctx: The run context with dependencies
            query: The search query
            
        Returns:
            Formatted string with relevant document chunks
        """
        # Get portfolio_id from dependencies
        portfolio_id = ctx.deps.portfolio_id
        
        if not portfolio_id:
            return "Unable to retrieve documents: No portfolio context available."
            
        try:
            # Send document retrieval update
            await send_websocket_update(ctx.deps.websocket, "Retrieving documents", ctx.deps.message_id)
            
            # Retrieve documents using the portfolio namespace
            results = await document_service.retrieve_relevant_chunks(query, portfolio_id=portfolio_id, top_k=5)
            
            # Send processing update
            await send_websocket_update(ctx.deps.websocket, "Processing search results", ctx.deps.message_id)
            
            if not results:
                return "No relevant documents found in this portfolio."
                
            # Format the results for the agent
            formatted = []
            for r in results:
                # If attributes is None, show a placeholder or fetch from DB if needed
                if r["attributes"] and r["attributes"].get("text"):
                    text = r["attributes"]["text"]
                else:
                    text = "[No chunk text found for this result. Check vector DB schema and upsert logic.]"
                snippet = text[:300] + ("..." if len(text) > 300 else "")
                formatted.append(f"Chunk ID: {r['id']}\nScore: {r['distance']:.4f}\n{snippet}\n")
            return "\n---\n".join(formatted)
        except Exception as e:
            logger.error(f"Error retrieving documents: {str(e)}")
            return f"[RAG Retrieval Error] {str(e)}"

    @staticmethod
    async def get_property_info(ctx: RunContext[AgentDependencies]) -> str:
        """
        Get information about properties in the user's portfolio.
        
        Args:
            ctx: The run context with dependencies
            
        Returns:
            String with property information
        """
        await send_websocket_update(ctx.deps.websocket, "Fetching property data", ctx.deps.message_id)
        
        # Get portfolio_id from dependencies
        portfolio_id = ctx.deps.portfolio_id
        
        if not portfolio_id:
            return "No specific portfolio identified. Please provide more context."
            
        try:
            # Get properties using connection_manager directly
            # Get a database connection
            client = await get_db_client()
            
            # Query properties for this portfolio
            response = client.table("prop").select("*").eq("portfolio_id", str(portfolio_id)).execute()
            
            if not response.data:
                return "No properties found in this portfolio."
                
            # Get property data
            properties = response.data
            
            # For each property, get its address and additional data
            for prop in properties:
                if prop.get("address_id"):
                    # Fetch the address
                    addr_response = client.table("prop_addresses").select("*")\
                        .eq("id", str(prop["address_id"])).execute()
                    if addr_response.data:
                        prop["address"] = addr_response.data[0]
                
                # Get units
                units_response = client.table("prop_units").select("*")\
                    .eq("prop_id", str(prop["id"])).execute()
                if units_response.data:
                    prop["units"] = units_response.data
                    
                # Get property details
                details_response = client.table("prop_details").select("*")\
                    .eq("prop_id", str(prop["id"])).execute()
                if details_response.data:
                    prop["details"] = details_response.data[0]
                    
                # Get market data
                market_response = client.table("prop_market_data").select("*")\
                    .eq("prop_id", str(prop["id"])).execute()
                if market_response.data:
                    prop["market_data"] = market_response.data
                    
                # Get solar potential
                solar_response = client.table("prop_solar_potential").select("*")\
                    .eq("prop_id", str(prop["id"])).execute()
                if solar_response.data:
                    prop["solar_potential"] = solar_response.data[0]
                    
                # Get tax history
                tax_response = client.table("prop_tax_history").select("*")\
                    .eq("prop_id", str(prop["id"])).execute()
                if tax_response.data:
                    prop["tax_history"] = tax_response.data
            
            await send_websocket_update(ctx.deps.websocket, "Processing property info", ctx.deps.message_id)
            
            # Format property information
            prop_info = "Here are the properties in the portfolio:\n\n"
            for idx, prop in enumerate(properties, 1):
                # Get address details
                prop_address = "Unknown address"
                prop_city = ""
                prop_state = ""
                
                if prop.get("address"):
                    addr = prop["address"]
                    prop_address = addr.get("address", "Unknown address")
                    prop_city = addr.get("city", "")
                    prop_state = addr.get("state", "")
                
                prop_info += f"{idx}. {prop_address}, {prop_city}, {prop_state}\n"
                
                # Add property details if available
                if prop.get("details"):
                    details = prop["details"]
                    prop_info += "   Property Details:\n"
                    if details.get("year_built"):
                        prop_info += f"   - Year Built: {details.get('year_built')}\n"
                    if details.get("gross_sq_ft"):
                        prop_info += f"   - Square Footage: {details.get('gross_sq_ft')} sq ft\n"
                    if details.get("lot_size_acre"):
                        prop_info += f"   - Lot Size: {details.get('lot_size_acre')} acres\n"
                    if details.get("last_sale_date") and details.get("last_sale_amount"):
                        prop_info += f"   - Last Sale: ${details.get('last_sale_amount'):,.2f} on {details.get('last_sale_date')}\n"
                
                # Add unit information if available
                if prop.get("units"):
                    prop_info += "   Units:\n"
                    for unit in prop["units"]:
                        beds = unit.get("beds", "?")
                        baths = unit.get("baths", "?")
                        prop_info += f"   - Unit {unit.get('unit', 'N/A')}: {beds} bed, {baths} bath\n"
                
                # Add tax history information
                if prop.get("tax_history"):
                    prop_info += "   Tax History:\n"
                    # Sort by most recent tax year
                    tax_history = sorted(prop["tax_history"], key=lambda x: x.get("assessed_tax_year", 0), reverse=True)
                    # Show latest 2 tax entries
                    for tax in tax_history[:2]:
                        if tax.get("assessed_tax_year") and tax.get("tax_bill_amount"):
                            prop_info += f"   - {tax.get('assessed_tax_year')}: ${tax.get('tax_bill_amount'):,.2f}\n"
                
                # Add solar potential information
                if prop.get("solar_potential") and prop["solar_potential"].get("data"):
                    solar_data = prop["solar_potential"]["data"]
                    prop_info += "   Solar Potential:\n"
                    if isinstance(solar_data, dict):
                        if solar_data.get("annual_kwh"):
                            prop_info += f"   - Annual kWh Potential: {solar_data.get('annual_kwh'):,.2f}\n"
                        if solar_data.get("savings_estimate"):
                            prop_info += f"   - Estimated Savings: ${solar_data.get('savings_estimate'):,.2f}\n"
                
                # Add market data highlights
                if prop.get("market_data"):
                    prop_info += "   Market Data:\n"
                    # Get the most recent market data
                    if prop["market_data"]:
                        recent_data = max(prop["market_data"], key=lambda x: x.get("created_at", ""), default=None)
                        if recent_data and recent_data.get("data"):
                            market_info = recent_data["data"]
                            if isinstance(market_info, dict):
                                if market_info.get("median_rent"):
                                    prop_info += f"   - Median Rent: ${market_info.get('median_rent'):,.2f}\n"
                                if market_info.get("price_per_sqft"):
                                    prop_info += f"   - Price/SqFt: ${market_info.get('price_per_sqft'):,.2f}\n"
                                if market_info.get("value_trend"):
                                    prop_info += f"   - Value Trend: {market_info.get('value_trend')}\n"
                
                prop_info += "\n"
                
            return prop_info
        except Exception as e:
            logger.error(f"Error retrieving portfolio data: {str(e)}")
            return "I'm having trouble accessing the property information at the moment."

    @staticmethod
    async def generate_direct_response(ctx: RunContext[AgentDependencies], query: str) -> str:
        """
        Generate a direct response when no specialized tools are needed.
        
        Args:
            ctx: The run context with dependencies
            query: The user query
            
        Returns:
            Direct response string
        """
        await send_websocket_update(ctx.deps.websocket, "Generating response", ctx.deps.message_id)
        
        return "I'll help you with that! Let me provide a direct response since this doesn't require specialized tools."

    async def generate_response(self, chat_history: List[Message], portfolio_id: str = None, websocket: WebSocket = None, message_id: str = None) -> Message:
        """
        Generate a response to a user message using the agent.
        
        Args:
            chat_history: List of previous messages in the conversation
            portfolio_id: Portfolio ID for document retrieval context
            websocket: WebSocket connection for sending updates
            message_id: Message ID for update tracking
            
        Returns:
            A Message object containing the agent's response
        """
        if chat_history and chat_history[-1].role == MessageRole.USER:
            user_message = chat_history[-1].content.text
            logger.info(f"User message: {user_message}")
        
        await send_websocket_update(websocket, "Understanding your request", message_id)
        
        # Extract the latest user query
        user_query = chat_history[-1].content.text if chat_history and chat_history[-1].role == MessageRole.USER else ""
        
        # Create dependencies for the agent
        deps = AgentDependencies(
            portfolio_id=portfolio_id,
            chat_history=chat_history,
            websocket=websocket,
            message_id=message_id
        )
        
        try:
            # Prepare the input for the agent including any images
            agent_input = [user_query]
            
            # Check if the latest message has images
            if chat_history and chat_history[-1].role == MessageRole.USER and chat_history[-1].content.image_urls:
                for image_url in chat_history[-1].content.image_urls:
                    # Add each image to the agent input
                    agent_input.append(ImageUrl(url=image_url))
                    logger.info(f"Added image to agent input: {image_url}")
            
            # Run the agent with the prepared input
            result = await self.agent.run(agent_input, deps=deps)
            response_text = result.output
            
            # Create response message
            response = Message(
                id=f"msg_{uuid.uuid4().hex[:10]}",
                role=MessageRole.ASSISTANT,
                content=MessageContent(text=response_text),
                user_id=None,
                chat_id=None
            )
            
            # Copy user_id and chat_id from the last message if available
            if chat_history and hasattr(chat_history[-1], 'chat_id'):
                response.chat_id = chat_history[-1].chat_id
                response.user_id = chat_history[-1].user_id
                
            logger.info(f"Generated agent response: {response.content.text[:50]}...")
            return response
            
        except Exception as e:
            logger.error(f"Error generating agent response: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            
            # Create error response
            error_response = Message(
                id=f"msg_{uuid.uuid4().hex[:10]}",
                role=MessageRole.ASSISTANT,
                content=MessageContent(
                    text="I'm sorry, I'm having trouble processing your request right now. Please try again later."
                ),
                user_id=None,
                chat_id=None
            )
            
            # Copy user_id and chat_id from the last message if available
            if chat_history and hasattr(chat_history[-1], 'chat_id'):
                error_response.chat_id = chat_history[-1].chat_id
                error_response.user_id = chat_history[-1].user_id
                
            return error_response

# Create singleton instance
agent_service = AgentService() 