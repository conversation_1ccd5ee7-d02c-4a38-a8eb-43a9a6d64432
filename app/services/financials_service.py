"""
Financials Service - Main service for financial analysis tasks.

This service acts as the primary orchestrator for financial analysis tasks,
delegating work to specialized sub-agents.
"""
import httpx
import numpy as np
import pandas as pd

import numpy_financial as npf
from datetime import datetime
from typing import Optional, Dict, Any, List

from app.utils.logger import get_logger
from app.utils.env_cache import get_env
from app.repository.db import get_db_client
from app.repository.cache import redis_cache

from app.models.database import PropertyFinancials

from app.services.property_service import PropertyService

logger = get_logger(__name__)

class PropertFinancialService:
    """
    Service for managing property financial data.
    """

    @classmethod
    async def is_field_human_edited(cls, prop_id: str, year: int, field_name: str) -> bool:
        """
        Check if a specific field has human edits and should not be overwritten.

        Args:
            prop_id: Property ID to check
            year: Year to check for human edits
            field_name: Specific field name to check (e.g., 'property_tax', 'repairs')

        Returns:
            Boolean indicating if the field has human edits
        """
        try:
            # Get metadata for the property and year
            metadata_response = await PropertFinancialMetadataService.get_property_metadata(prop_id, year)

            # Check if metadata exists and has attribution data
            if metadata_response and "metadata" in metadata_response:
                metadata = metadata_response["metadata"]

                if "attribution" in metadata:
                    attribution = metadata["attribution"]

                    if field_name in attribution:
                        field_attribution = attribution[field_name]
                        history = field_attribution.get("history", [])

                        if history:
                            # Get the most recent entry (last in history)
                            latest_entry = history[-1]
                            if latest_entry.get("source") == "human":
                                human_value = latest_entry.get('value', 'N/A')
                                edit_date = latest_entry.get('datetime_changed', 'Unknown')
                                logger.debug(f"✋ Field {field_name} has human edit: ${human_value:,.2f} (edited: {edit_date})")
                                return True

            # No human edits found
            return False

        except Exception as e:
            logger.error(f"Error checking if field {field_name} is human-edited for property {prop_id} year {year}: {str(e)}")
            # Return False to ensure AI computation continues
            return False

    @classmethod
    async def set_single_property_financial(cls, prop_id: str, year: int, field_name: str, value: Any) -> Optional[list]:
        """
        Set specific property financial data fields without affecting other values.
        Checks for human edits and skips database updates if field has human source.

        Args:
            prop_id: Property ID
            year: Financial year
            field_name: Name of the field to update (e.g., 'property_tax', 'maintenance_fees')
            value: Value to set

        Returns:
            List of updated records or None if error occurs or field is human-edited
        """
        try:
            # Check if this field has human edits - if so, don't update database
            is_human_edited = await cls.is_field_human_edited(prop_id, year, field_name)

            if is_human_edited:
                return None  # Don't update database

            # Proceed with database update if no human edits found
            client = await get_db_client()
            update_data = {field_name: value}
            response = client.table("prop_financials").update(update_data).eq("prop_id", prop_id).eq("year", year).execute()
            logger.debug(f"Updated database field {field_name} = ${value:,.2f} for year {year}")
            return response.data
        except Exception as e:
            logger.error(f"Error setting property financials: {str(e)}")
            return None
        
    @classmethod
    async def set_metadata_attribution(cls, prop_id: str, year: int, field_name: str) -> Optional[list]:
        """
        Set the metadata attribution for a property by appending to the attribution list.
        Checks for human edits and skips AI attribution if field has human source.

        Args:
            prop_id: Property ID
            year: Financial year
            field_name: Name of the field that was changed

        Returns:
            List of updated records or None if error occurs
        """
        try:
            # Check if this field has human edits - if so, don't add AI attribution
            is_human_edited = await cls.is_field_human_edited(prop_id, year, field_name)

            if is_human_edited:
                logger.info(f"Skipping AI attribution for human-edited field {field_name} in year {year}")
                return None  # Don't add AI attribution

            # Get the client using the standard function
            client = await get_db_client()

            # Get current metadata
            response = client.table("prop_financials").select("metadata").eq("prop_id", prop_id).eq("year", year).execute()
            current_metadata = response.data[0].get("metadata", {}) if response.data else {}

            # Get current attribution list or initialize empty list
            attribution_list = current_metadata.get("attribution", []) if current_metadata else []

            # Append new attribution entry
            attribution_list.append({
                "year": year,
                "entity": "ai",
                "row_name": field_name,
                "datetime_changed": datetime.now().isoformat()
            })

            # Update metadata with new attribution list
            if current_metadata:
                current_metadata["attribution"] = attribution_list
            else:
                current_metadata = {"attribution": attribution_list}

            # Update the database
            response = client.table("prop_financials").update({"metadata": current_metadata}).eq("prop_id", prop_id).eq("year", year).execute()
            logger.debug(f"Added AI attribution for field {field_name} in year {year}")
            return response.data
        except Exception as e:
            logger.error(f"Error setting property metadata attribution: {str(e)}")
            return None
        
    @classmethod
    async def get_property_financials_sources(cls, prop_id: str, year: Optional[int] = None, all_years: bool = False) -> Optional[Dict[str, Any]]:
        """
        Get the metadata sources for a property for a specific year or all years.
        
        Args:
            prop_id: Property ID
            year: Financial year (optional if all_years is True)
            all_years: If True, get sources for all years
            
        Returns:
            Dictionary containing sources metadata or None if error occurs
        """
        try:
            # Get the client using the standard function
            client = await get_db_client()
            
            # Build query based on parameters
            query = client.table("prop_financials").select("year, metadata").eq("prop_id", prop_id)
            
            if all_years:
                # Get all years for this property
                response = query.execute()
            else:
                # Get specific year
                if year is None:
                    logger.error("Year must be provided when all_years is False")
                    return None
                response = query.eq("year", year).execute()
            
            if not response.data:
                return None
            
            # Extract sources from metadata for each record
            sources_data = {}
            for record in response.data:
                record_year = record.get("year")
                metadata = record.get("metadata", {})
                sources = metadata.get("sources", {})
                
                if all_years:
                    sources_data[str(record_year)] = sources
                else:
                    sources_data = sources
                    break
            
            return sources_data
            
        except Exception as e:
            logger.error(f"Error getting property financials sources: {str(e)}")
            return None

    @classmethod
    async def get_property_financials_dataframe(cls, prop_id: str) -> pd.DataFrame:
        """
        Get property financials data for years 1-10 as a pandas DataFrame with years as columns.
        This function uses Redis caching to persist the DataFrame in the user session.
        Validates data through PropertyFinancials model before creating DataFrame.
        If the cached DataFrame is missing expected fields (e.g. after a schema update),
        the cache is automatically refreshed.
        
        Args:
            prop_id: Property ID
            
        Returns:
            pd.DataFrame: DataFrame with property financials data for years 1-10
        """
        try:
            # Define ALL financial fields from PropertyFinancials model - COMPLETE LIST
            financial_fields = [
                'id', 'prop_id', 'year',
                # Income Projections
                'rental_income', 'long_term_rental', 'short_term_rental', 'other_income', 
                'vacancy_loss', 'credit_loss', 'effective_gross_income',
                # Expense Projections
                'property_tax', 'insurance', 'repairs', 'maintenance', 'professional_fees',
                'management_fees', 'leasing_fees', 'legal_fees', 'accounting_fees',
                'engineering_fees', 'marketing_fees', 'consulting_fees', 'utilities',
                'services', 'reserves', 'total_operating_expenses',
                # Net Operating Income
                'net_operating_income',
                # Financing & Debt Service
                'annual_debt_service', 'dscr',
                # Cash Flow Analysis
                'cash_flow_before_taxes', 'cash_flow_after_taxes', 'cumulative_cash_flow',
                # Valuation Metrics
                'cap_rate', 'gross_rent_multiplier', 'equity_multiple', 'cash_on_cash_return',
                # Portfolio Summary & Roll-Up
                'total_acquisition_cost', 'aggregated_noi', 'blended_cap_rate', 'portfolio_irr',
                # Timestamps and metadata
                'created_at', 'updated_at', 'metadata'
            ]

            # Check if data is in cache
            cache_key = f"property_financials_df:{prop_id}"
            cached_data = await redis_cache.get(cache_key)
            if cached_data:
                df_cached = pd.DataFrame.from_dict(cached_data)
                
                # Fix duplicate columns issue - keep only unique columns
                if df_cached.columns.duplicated().any():
                    logger.warning("Found duplicate columns in cached DataFrame - removing duplicates")
                    df_cached = df_cached.loc[:, ~df_cached.columns.duplicated()]
                
                # Ensure columns are integers (years 1-10)
                try:
                    df_cached.columns = [int(col) for col in df_cached.columns]
                except (ValueError, TypeError):
                    logger.warning("Could not convert cached DataFrame columns to integers - regenerating cache")
                    await redis_cache.delete(cache_key)
                else:
                    # If cached df is missing any of the expected fields, refresh it
                    if set(financial_fields).issubset(set(df_cached.index)):
                        return df_cached
                    # Cached version is stale – delete and regenerate
                    await redis_cache.delete(cache_key)
                    logger.info("Cached financials DataFrame was missing fields; regenerating cache.")

            # Get the client using the standard function
            client = await get_db_client()

            # Fetch financial data for years 1-10
            response = client.table("prop_financials").select("*").eq("prop_id", prop_id).in_("year", [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]).execute()

            # Validate and parse data through PropertyFinancials model
            validated_records = []
            for record in response.data:
                try:
                    # Create PropertyFinancials instance to validate data
                    financial_record = PropertyFinancials(**record)
                    validated_records.append(financial_record)
                except Exception as validation_error:
                    logger.warning(f"Data validation failed for record {record.get('id', 'unknown')}: {str(validation_error)}")
                    continue

            # Create an empty dataframe with financial fields as index and years as columns
            years = list(range(1, 11))  # Years 1-10
            df = pd.DataFrame(index=financial_fields, columns=years)

            # Fill in data from validated records
            for record in validated_records:
                year = record.year
                if year in years:
                    # Convert Pydantic model to dict for DataFrame population
                    record_dict = record.model_dump()
                    for field in financial_fields:
                        if field in record_dict:
                            df.at[field, year] = record_dict[field]

            # Fill NaN values with empty strings for display purposes
            df = df.fillna('')

            # Store in cache for future use (1 hour TTL)
            # Convert DataFrame to dict with string keys to ensure compatibility with Redis cache
            # Handle UUID serialization by converting UUIDs to strings
            df_dict = {}
            for k, v in df.to_dict().items():
                # Convert column keys to strings
                str_key = str(k)
                # Handle the column values (which are dictionaries with row indices as keys)
                if isinstance(v, dict):
                    converted_v = {}
                    for row_key, cell_value in v.items():
                        # Convert UUID objects to strings for JSON serialization
                        if hasattr(cell_value, '__class__') and cell_value.__class__.__name__ == 'UUID':
                            converted_v[str(row_key)] = str(cell_value)
                        else:
                            converted_v[str(row_key)] = cell_value
                    df_dict[str_key] = converted_v
                else:
                    df_dict[str_key] = v
            
            await redis_cache.set(cache_key, df_dict, ttl=3600)

            return df
        except Exception as e:
            logger.error(f"Error fetching property financials dataframe: {str(e)}")
            # Return an empty DataFrame with the right structure if an error occurs
            years = list(range(1, 11))  # Years 1-10
            return pd.DataFrame(index=financial_fields, columns=years)
        
    @classmethod
    async def _calculate_missing_expenses(cls, df: pd.DataFrame, year: int, rental_income: float, property_value: float, location_info: Optional[Dict[str, Any]] = None) -> None:
        """
        Calculate missing expense categories using market research and industry standards.
        For years 2-10, project Year 1 values forward with inflation instead of recalculating.
        
        Args:
            df: DataFrame with financial data
            year: Current year being processed
            rental_income: Annual rental income for the property
            property_value: Estimated property value
            location_info: Dictionary with city, state, zipcode for Tavily research
        """
        try:
            from tavily import TavilyClient
            from app.utils.env_cache import get_env
            
            # Helper to safely convert any cell to float
            def _to_float(value) -> float:
                if value in ("", None):
                    return 0.0
                try:
                    if isinstance(value, str):
                        value = value.replace("$", "").replace(",", "").strip()
                    return float(value)
                except (ValueError, TypeError):
                    return 0.0
            
            # Define missing expense fields and their typical percentages (only used for Year 1)
            # Note: Removed 'reserves' as per user request - not accurate for financial calculations
            expense_defaults = {
                'repairs': 0.02,           # 2% of property value annually
                'maintenance': 0.015,      # 1.5% of property value annually
                'insurance': 0.004,        # 0.4% of property value (will research actual rates via Tavily)
                'management_fees': 0.06,   # 6% of rental income (property management)
                'leasing_fees': 0.04,      # 4% of rental income (tenant placement costs)
                'legal_fees': 0.01,        # 1% of rental income (legal services)
                'accounting_fees': 0.005,  # 0.5% of rental income
                'engineering_fees': 0.003, # 0.3% of rental income  
                'marketing_fees': 0.005,   # 0.5% of rental income
                'consulting_fees': 0.002,  # 0.2% of rental income
                'utilities': 0.015,        # 1.5% of rental income (landlord-paid utilities)
                'services': 0.008,         # 0.8% of property value (landscaping, cleaning)
            }
            
            # Inflation rate for projecting expenses forward (3% annually)
            annual_inflation = 0.03
            
            # For Year 1: Calculate missing expenses from scratch
            if year == 1:
                # Initialize Tavily client for market research (only for Year 1)
                try:
                    tavily_api_key = get_env('TAVILY_API_KEY')
                    if tavily_api_key:
                        tavily_client = TavilyClient(api_key=tavily_api_key)
                    else:
                        logger.warning("TAVILY_API_KEY not found - using default insurance rates")
                        tavily_client = None
                except Exception as e:
                    logger.warning(f"Failed to initialize Tavily client: {str(e)} - using default insurance rates")
                    tavily_client = None
                
                # Helper function to research insurance rates using Tavily
                async def _research_insurance_rate(property_value: float, location_info: dict) -> float:
                    """Research local insurance rates using Tavily search"""
                    try:
                        if not tavily_client:
                            # Fallback to state-based estimation if no Tavily client
                            state = location_info.get('state', '')
                            if state:
                                state_lower = state.lower()
                                # Higher rates for high-risk states
                                if state_lower in ['florida', 'louisiana', 'texas', 'california']:
                                    rate = 0.006  # 0.6% for high-risk states
                                elif state_lower in ['oklahoma', 'kansas', 'alabama', 'mississippi']:
                                    rate = 0.005  # 0.5% for moderate-risk states
                                else:
                                    rate = 0.004  # 0.4% for lower-risk states
                                
                                logger.info(f"Using state-based insurance rate: {rate*100:.2f}% for {state}")
                                return rate
                            return 0.004  # Default fallback
                        
                        city = location_info.get('city', 'Unknown')
                        state = location_info.get('state', 'Unknown')
                        zipcode = location_info.get('zipcode', '')
                        
                        # Create search query for insurance rates
                        search_query = f"rental property landlord insurance rates {city} {state} {zipcode} 2024 cost per year percentage property value"
                        
                        # Search using Tavily
                        search_results = tavily_client.search(
                            query=search_query,
                            search_depth="advanced",
                            max_results=3
                        )
                        
                        # Extract insurance rate information from results
                        if search_results and 'results' in search_results:
                            for result in search_results['results']:
                                content = result.get('content', '').lower()
                                # Look for percentage patterns in the content
                                import re
                                
                                # Look for patterns like "0.3% of property value", "0.4% annually", etc.
                                percentage_patterns = [
                                    r'(\d+\.?\d*)\s*%\s*of\s*property\s*value',
                                    r'(\d+\.?\d*)\s*%\s*annually',
                                    r'(\d+\.?\d*)\s*%\s*per\s*year',
                                    r'insurance.*?(\d+\.?\d*)\s*%'
                                ]
                                
                                for pattern in percentage_patterns:
                                    matches = re.findall(pattern, content)
                                    if matches:
                                        rate = float(matches[0]) / 100  # Convert percentage to decimal
                                        if 0.001 <= rate <= 0.02:  # Reasonable range (0.1% - 2.0%)
                                            logger.info(f"Found insurance rate via Tavily: {rate*100:.2f}% for {city}, {state}")
                                            return rate
                        
                        # If no specific rate found, use location-based estimation
                        if state:
                            state_lower = state.lower()
                            # Higher rates for high-risk states
                            if state_lower in ['florida', 'louisiana', 'texas', 'california']:
                                rate = 0.006  # 0.6% for high-risk states
                            elif state_lower in ['oklahoma', 'kansas', 'alabama', 'mississippi']:
                                rate = 0.005  # 0.5% for moderate-risk states
                            else:
                                rate = 0.004  # 0.4% for lower-risk states
                            
                            logger.info(f"Using state-based insurance rate: {rate*100:.2f}% for {state}")
                            return rate
                        
                    except Exception as e:
                        logger.warning(f"Error researching insurance rates via Tavily: {str(e)}")
                    
                    # Fallback to default
                    return 0.004  # 0.4% default
                
                # Get location info for Tavily research
                location_info = location_info or {}
                
                # Check each expense field and calculate if missing
                for expense_field, default_rate in expense_defaults.items():
                    try:
                        # Safe DataFrame access - check if field exists in index
                        if expense_field not in df.index:
                            logger.warning(f"Expense field '{expense_field}' not found in DataFrame index - skipping")
                            continue
                            
                        current_value = _to_float(df.at[expense_field, year])
                        
                        if current_value == 0.0:  # Field is missing or zero
                            # Special handling for insurance with Tavily research
                            if expense_field == 'insurance':
                                researched_rate = await _research_insurance_rate(property_value, location_info)
                                calculated_value = -(property_value * researched_rate)  # Negative for expenses
                                df.at[expense_field, year] = round(calculated_value, 2)
                                logger.info(f"Year {year}: Calculated {expense_field}: ${calculated_value:.2f} ({researched_rate*100:.2f}% rate via research)")
                            
                            elif expense_field in ['repairs', 'maintenance', 'services']:
                                # Property value-based expenses
                                if property_value > 0:
                                    calculated_value = -(property_value * default_rate)  # Negative for expenses
                                else:
                                    # Fallback to rental income estimate
                                    calculated_value = -(rental_income * default_rate * 0.5)
                                df.at[expense_field, year] = round(calculated_value, 2)
                                logger.info(f"Year {year}: Calculated {expense_field}: ${calculated_value:.2f} ({default_rate*100:.1f}% rate)")
                            
                            else:
                                # Rental income-based expenses (management_fees, leasing_fees, legal_fees, etc.)
                                calculated_value = -(rental_income * default_rate)  # Negative for expenses
                                df.at[expense_field, year] = round(calculated_value, 2)
                                logger.info(f"Year {year}: Calculated {expense_field}: ${calculated_value:.2f} ({default_rate*100:.1f}% rate)")
                    except Exception as field_error:
                        logger.error(f"Error calculating {expense_field} for year {year}: {str(field_error)}")
                        continue
            
            # For Years 2-10: Project Year 1 values forward with inflation
            else:
                for expense_field in expense_defaults.keys():
                    current_value = _to_float(df.at[expense_field, year])
                    year_1_value = _to_float(df.at[expense_field, 1])
                    
                    # Only project if Year 1 has a value and current year is missing/zero
                    if current_value == 0.0 and year_1_value != 0.0:
                        # Apply compound inflation from Year 1
                        years_elapsed = year - 1
                        projected_value = year_1_value * ((1 + annual_inflation) ** years_elapsed)
                        
                        df.at[expense_field, year] = round(projected_value, 2)
                        growth_rate = ((projected_value / year_1_value) - 1) * 100
                        logger.info(f"Year {year}: Projected {expense_field}: ${projected_value:.2f} ({growth_rate:.1f}% growth from Year 1)")
                    
                    # If Year 1 is missing but current year needs calculation, fall back to percentage method
                    elif current_value == 0.0 and year_1_value == 0.0:
                        default_rate = expense_defaults[expense_field]
                        if expense_field in ['repairs', 'maintenance', 'services', 'insurance']:
                            # Property value-based expenses
                            if property_value > 0:
                                calculated_value = -(property_value * default_rate)
                            else:
                                calculated_value = -(rental_income * default_rate * 0.5)
                        else:
                            # Rental income-based expenses (management_fees, leasing_fees, legal_fees, utilities, etc.)
                            calculated_value = -(rental_income * default_rate)
                        
                        df.at[expense_field, year] = round(calculated_value, 2)
                        logger.info(f"Year {year}: Fallback calculated {expense_field}: ${calculated_value:.2f} (Year 1 missing)")
            
        except Exception as e:
            logger.error(f"Error calculating missing expenses: {str(e)}")
            # Continue with defaults if calculation fails
        
    @classmethod
    async def compute_property_financials(cls, df: pd.DataFrame, override_user_input: bool = False) -> pd.DataFrame:
        """
        Compute derived financial metrics for a property given a DataFrame and
        return an updated DataFrame with the new values populated.

        The incoming ``df`` **MUST** follow the same structure produced by
        ``get_property_financials_dataframe`` –  financial fields as the index
        and years (1-10) as the columns.  The method operates *purely* on the
        DataFrame; **no** external IO (database, cache, etc.) is performed.

        CALCULATION SOURCES AND REFERENCES:
        -----------------------------------
        
        Professional Fees Aggregation:
        - Source: Real Estate Investment Analysis (Geltner, Miller, Clayton, Eichholtz)
        - Industry Standard: Management (6-12%), Leasing (5-8%), Legal (1-2%), Accounting (0.5-1%)
        - Professional fees = sum of all individual professional service components
        
        Income Calculations:
        - Source: NAREIT (National Association of Real Estate Investment Trusts) standards
        - Rental Income = Long-term rental + Short-term rental income
        - Effective Gross Income = Rental Income + Other Income - Vacancy Loss - Credit Loss
        - Note: Vacancy and credit losses are stored as negative values (expense deductions)
        
        Operating Expenses:
        - Source: IREM (Institute of Real Estate Management) expense categorization
        - Components: Property Tax + Insurance + Repairs + Maintenance + Professional Fees + 
          Utilities + Services + Reserves
        - Industry benchmark: 35-45% of gross rental income for typical multifamily properties
        
        Net Operating Income (NOI):
        - Source: Appraisal Institute standards
        - Formula: NOI = Effective Gross Income - Total Operating Expenses
        - Key metric for property valuation and investment analysis
        
        Debt Service Coverage Ratio (DSCR):
        - Source: Commercial lending standards (Fannie Mae, Freddie Mac guidelines)
        - Formula: DSCR = NOI / Annual Debt Service
        - Industry minimum: 1.25x for most commercial loans, 1.35x+ preferred
        
        Cash Flow Analysis:
        - Source: Real Estate Financial Modeling (REFM) standards
        - Before-tax Cash Flow = NOI - Annual Debt Service
        - Cumulative Cash Flow = Running sum of annual cash flows
        
        Valuation Metrics:
        - Cap Rate Source: NCREIF (National Council of Real Estate Investment Fiduciaries)
        - Formula: Cap Rate = NOI / Property Value (expressed as percentage)
        - Market cap rates vary by location, property type, and market conditions
        
        Return Metrics:
        - Cash-on-Cash Return Source: CCIM Institute standards
        - Formula: CoC = Annual Cash Flow / Initial Cash Investment
        - Equity Multiple = Total Cash Distributed / Total Cash Invested
        
        IRR Calculation:
        - Source: CFA Institute investment performance standards
        - Uses numpy.irr function for internal rate of return calculation
        - Accounts for timing of cash flows over investment period

        Parameters
        ----------
        df : pd.DataFrame
            A DataFrame whose index corresponds to the complete list of
            financial fields (see ``financial_fields`` in
            ``get_property_financials_dataframe``) and whose columns correspond
            to the years 1-10.
        override_user_input : bool, optional
            If False (default), preserves human-entered values based on metadata attribution.
            If True, recalculates all values regardless of source.

        Returns
        -------
        pd.DataFrame
            The same DataFrame instance with the derived metrics filled-in. The
            function mutates *in-place* and also returns the mutated object for
            convenience.
        """
        # Early exit if dataframe is empty or mal-formatted
        if df is None or df.empty:
            logger.warning("No financial data available to compute derived metrics – incoming DataFrame is empty.")
            return df

        # Helper to safely convert any cell to float
        def _to_float(value: Any) -> float:
            """
            Coerce *value* to *float* – returning 0.0 on failure/empties.
            Handles currency formatting, commas, and various string representations.
            """
            if value in ("", None):
                return 0.0
            try:
                if isinstance(value, str):
                    # Remove currency symbols and commas for proper conversion
                    value = value.replace("$", "").replace(",", "").strip()
                return float(value)
            except (ValueError, TypeError):
                return 0.0

        # --- Metadata Processing for Human Input Preservation ---
        metadata_by_year = {}
        if not override_user_input:
            try:
                # Extract prop_id from DataFrame if available
                prop_id = None
                if "prop_id" in df.index:
                    prop_id = df.at["prop_id", 1] if 1 in df.columns else None
                
                if prop_id:
                    # Get metadata for all years for this property
                    # Note: Removing invalid import - metadata functionality will be implemented separately
                    
                    years_to_check = [col for col in df.columns if str(col).isdigit()]
                    for year in years_to_check:
                        year_int = int(str(year))
                        try:
                            # TODO: Implement metadata retrieval when service is available
                            # metadata = await PropertyFinancialsService.get_property_financials_metadata(
                            #     prop_id=prop_id, year=year_int
                            # )
                            # if metadata:
                            #     metadata_by_year[year_int] = metadata
                            #     logger.debug(f"Retrieved metadata for property {prop_id}, year {year_int}")
                            pass
                        except Exception as e:
                            logger.warning(f"Could not retrieve metadata for property {prop_id}, year {year_int}: {str(e)}")
                            
                    # Preprocess DataFrame to ensure human values are preserved
                    for year_int, metadata in metadata_by_year.items():
                        if "attribution" in metadata:
                            attribution = metadata["attribution"]
                            
                            for field_name, field_attribution in attribution.items():
                                if field_name in df.index and year_int in df.columns:
                                    # Check if latest entry is from human source
                                    history = field_attribution.get("history", [])
                                    if history:
                                        # Get the most recent entry (last in history)
                                        latest_entry = history[-1]
                                        if latest_entry.get("source") == "human" and "value" in latest_entry:
                                            human_value = latest_entry["value"]
                                            # Force set the human value in DataFrame
                                            df.at[field_name, year_int] = human_value
                                            logger.info(f"Preserved human input for {field_name} year {year_int}: {human_value}")
                                            
            except Exception as e:
                logger.error(f"Error processing metadata for human input preservation: {str(e)}")
                # Continue with calculation even if metadata processing fails

        # Professional fee component fields per IREM and NAREIT standards
        # Source: Institute of Real Estate Management (IREM) Income/Expense Analysis
        professional_fee_fields: List[str] = [
            "management_fees",      # Property management: 6-12% of rental income (IREM standard)
            "leasing_fees",        # Tenant placement: 5-8% annually amortized (industry standard)
            "legal_fees",          # Legal services: 1-2% of rental income (typical range)
            "accounting_fees",     # Bookkeeping/tax prep: 0.5-1% of rental income
            "engineering_fees",    # Inspections/assessments: 0.2-0.5% of rental income
            "marketing_fees",      # Advertising/promotion: 1-3% of rental income
            "consulting_fees",     # Investment advisory: 0.5-1% of rental income
        ]

        # Expense categories contributing to *total_operating_expenses*
        # Source: IREM Chart of Accounts for Income-Producing Real Estate
        # Note: Removed 'reserves' as per user request - not accurate for financial calculations
        expense_fields: List[str] = [
            "property_tax",        # Real estate taxes (varies by jurisdiction)
            "insurance",          # Property/liability insurance: 0.2-1.0% of property value
            "repairs",            # Repairs and maintenance: 1-3% of property value annually
            "maintenance",        # Ongoing maintenance: separate from major repairs
            "professional_fees",  # Sum of all professional service fees (calculated above)
            "utilities",          # Landlord-paid utilities: varies by lease structure
            "services",           # Landscaping, cleaning, security services
        ]

        # Keep original column labels (could be int or str) but sort chronologically
        years: List[Any] = sorted(
            [col for col in df.columns if str(col).isdigit()], key=lambda x: int(str(x))
        )

        # Keep running totals for values that need aggregation/IRR style metrics
        yearly_cash_flows: List[float] = []  # cash flow before taxes per year
        yearly_noi: List[float] = []         # NOI per year

        # Get current mortgage rate for debt service calculations
        current_mortgage_rate = await cls._get_current_mortgage_rate()
        
        # Track year 1 acquisition cost to ensure consistency across years
        year_1_acquisition_cost = None

        for year in years:
            year_int = int(str(year))
            
            # Check if this field/year combination should be protected from recalculation
            def is_human_input(field_name: str) -> bool:
                """Check if a field has human input that should be preserved"""
                if override_user_input:
                    return False
                    
                if year_int in metadata_by_year:
                    attribution = metadata_by_year[year_int].get("attribution", {})
                    field_attribution = attribution.get(field_name, {})
                    history = field_attribution.get("history", [])
                    if history:
                        latest_entry = history[-1]
                        return latest_entry.get("source") == "human"
                return False

            # --- Income Calculations --------------------------------------------------
            # Source: NAREIT standards for income categorization and reporting
            
            # Rental income is the sum of long term and short term rental income
            long_term_rental_val = _to_float(df.at["long_term_rental", year])
            short_term_rental_val = _to_float(df.at["short_term_rental", year])
            rental_income_val = long_term_rental_val + short_term_rental_val
            
            # Only use rental_income field as fallback when both long-term and short-term are zero
            if rental_income_val == 0.0:
                rental_income_val = _to_float(df.at["rental_income", year])
            
            # Always update rental_income to be the sum of long-term and short-term (if not human input)
            if not is_human_input("rental_income"):
                df.at["rental_income", year] = round(rental_income_val, 2)

            other_income_val = _to_float(df.at["other_income", year])
            vacancy_loss_val = _to_float(df.at["vacancy_loss", year])
            credit_loss_val = _to_float(df.at["credit_loss", year])

            # Source: IREM Income/Expense Analysis methodology
            # Vacancy and credit losses are stored as negative values in our system
            # They reduce gross income to arrive at effective gross income
            effective_gross_income = rental_income_val + other_income_val + vacancy_loss_val + credit_loss_val

            # --- Property Tax Calculation and Capping Logic ---------------------------
            if not is_human_input("property_tax"):
                property_tax_val = _to_float(df.at["property_tax", year])
                if property_tax_val == 0.0:
                    # If property tax is missing, calculate a reasonable value
                    property_tax_val = rental_income_val * 0.012  # 1.2% of rental income as a placeholder
                    df.at["property_tax", year] = round(property_tax_val, 2)

            # Cap property tax increases at 10% per year as requested
            if year_int > 1 and not is_human_input("property_tax"):
                previous_year = year_int - 1
                
                # Check if previous year column exists in DataFrame
                if previous_year in df.columns:
                    current_property_tax = _to_float(df.at["property_tax", year])
                    previous_property_tax = _to_float(df.at["property_tax", previous_year])
                    
                    if previous_property_tax != 0 and current_property_tax != 0:
                        # Calculate year-over-year increase
                        tax_increase = (current_property_tax / previous_property_tax) - 1
                        if tax_increase > 0.10:  # More than 10% increase
                            # Cap at 10% increase
                            capped_tax = previous_property_tax * 1.10
                            df.at["property_tax", year] = round(capped_tax, 2)
                            logger.info(f"Year {year_int}: Capped property tax increase at 10%: ${capped_tax:.2f} (was ${current_property_tax:.2f})")
                else:
                    logger.debug(f"Year {year_int}: Previous year {previous_year} not found in DataFrame columns {list(df.columns)} - skipping tax cap")

            # --- Acquisition Cost Consistency ----------------------------------------
            # Ensure total acquisition cost is the same in all years as in year 1
            if year_int == 1:
                year_1_acquisition_cost = _to_float(df.at["total_acquisition_cost", year])
            else:
                if year_1_acquisition_cost and year_1_acquisition_cost > 0 and not is_human_input("total_acquisition_cost"):
                    df.at["total_acquisition_cost", year] = year_1_acquisition_cost
                    logger.debug(f"Year {year_int}: Set acquisition cost to match year 1: ${year_1_acquisition_cost:,.2f}")

            # Get debt service and acquisition cost values for missing expense calculations
            annual_debt_service_val = _to_float(df.at["annual_debt_service", year])
            total_acquisition_cost_val = _to_float(df.at["total_acquisition_cost", year])

            # --- Calculate Missing Expense Categories Using Market Research -------------
            # Before calculating total operating expenses, ensure all categories are populated
            
            # Extract location info from DataFrame metadata if available (for Tavily research)
            location_info = {}
            try:
                # Check if DataFrame has prop_id available to lookup location
                prop_id = df.at["prop_id", year] if "prop_id" in df.index else None
                if prop_id and prop_id != '' and prop_id is not None:
                    # TODO: This should ideally fetch property address data
                    # For now, use placeholder - will be enhanced in future iterations
                    location_info = {'city': 'Unknown', 'state': 'Unknown', 'zipcode': ''}
            except Exception as e:
                location_info = {}
                logger.warning(f"Year {year_int}: Could not retrieve location info for property {prop_id}. Location info: {location_info}. Error: {e}")
            
            # --- Professional Fees Computation ------------------------------------------
            # Source: IREM standards - professional fees are sum of individual components
            # This aggregation follows GAAP accounting principles for expense categorization
            # NOTE: This must happen AFTER _calculate_missing_expenses to include calculated management fees
            if not is_human_input("professional_fees"):
                total_professional_fees = sum(_to_float(df.at[field, year]) for field in professional_fee_fields)
                df.at["professional_fees", year] = round(total_professional_fees, 2)
                
                # Validation: Professional fees should typically be 8-15% of rental income
                rental_income_for_validation = _to_float(df.at["rental_income", year])
                if rental_income_for_validation > 0:
                    prof_fee_percentage = (abs(total_professional_fees) / rental_income_for_validation) * 100
                    if prof_fee_percentage > 20:
                        logger.warning(f"Year {year_int}: Professional fees ({prof_fee_percentage:.1f}%) exceed typical range (8-15%)")

            # --- Operating Expenses Computation ----------------------------------------
            # Source: IREM Chart of Accounts - standard expense categorization
            # Note: This calculation depends on individual expense fields being populated
            if not is_human_input("total_operating_expenses"):
                total_operating_expenses = sum(_to_float(df.at[field, year]) for field in expense_fields)
                
                # Validation: Operating expenses typically 35-45% of gross rental income for multifamily
                # Source: IREM Income/Expense Analysis benchmarks
                if rental_income_val > 0:
                    expense_ratio = (total_operating_expenses / rental_income_val) * 100
                    if expense_ratio > 60:
                        logger.warning(f"Year {year_int}: Operating expense ratio ({expense_ratio:.1f}%) exceeds typical range (35-45%)")
                    elif expense_ratio < 25 and total_operating_expenses > 0:
                        logger.warning(f"Year {year_int}: Operating expense ratio ({expense_ratio:.1f}%) below typical range (35-45%)")
            else:
                total_operating_expenses = _to_float(df.at["total_operating_expenses", year])

            # --- Net Operating Income (NOI) Calculation --------------------------------
            # Source: Appraisal Institute, Real Estate Finance and Investments
            # NOI is the fundamental metric for real estate investment analysis
            if not is_human_input("net_operating_income"):
                net_operating_income = effective_gross_income + total_operating_expenses
            else:
                net_operating_income = _to_float(df.at["net_operating_income", year])
            
            # --- Enhanced Debt Service Calculation with Real Mortgage Rates -----------
            # Calculate missing annual debt service using real mortgage rates from FRED
            if annual_debt_service_val == 0 and net_operating_income > 0 and total_acquisition_cost_val > 0 and not is_human_input("annual_debt_service"):
                # Estimate loan amount (assume 80% LTV for commercial properties)
                loan_to_value = 0.80
                loan_amount = total_acquisition_cost_val * loan_to_value
                
                # Calculate monthly payment using real mortgage rate
                monthly_rate = current_mortgage_rate / 12
                num_payments = 30 * 12  # 30-year amortization
                
                if monthly_rate > 0:
                    # Standard mortgage payment formula: PMT = P * [r(1+r)^n] / [(1+r)^n - 1]
                    monthly_payment = loan_amount * (monthly_rate * (1 + monthly_rate)**num_payments) / ((1 + monthly_rate)**num_payments - 1)
                    annual_debt_service = monthly_payment * 12
                else:
                    # Interest-only fallback if rate is 0
                    annual_debt_service = loan_amount * current_mortgage_rate
                
                df.at["annual_debt_service", year] = round(annual_debt_service, 2)
                annual_debt_service_val = annual_debt_service
                logger.info(f"Year {year_int}: Calculated debt service: ${annual_debt_service:,.2f} (rate: {current_mortgage_rate*100:.2f}%, LTV: {loan_to_value*100:.0f}%)")
            
            # Calculate acquisition cost if missing using cap rate method
            if total_acquisition_cost_val == 0 and net_operating_income > 0 and not is_human_input("total_acquisition_cost"):
                # When AI agent doesn't return cap value, set default cap rate to 6% and compute property value
                default_cap_rate = 0.06  # 6% - default when AI agent doesn't return cap value
                estimated_acquisition_cost = net_operating_income / default_cap_rate
                df.at["total_acquisition_cost", year] = round(estimated_acquisition_cost, 2)
                total_acquisition_cost_val = estimated_acquisition_cost
                # Update year 1 acquisition cost if this is year 1
                if year_int == 1:
                    year_1_acquisition_cost = estimated_acquisition_cost
                logger.info(f"Year {year_int}: Estimated acquisition cost: ${estimated_acquisition_cost:,.2f} (6% default cap rate)")

            # --- Debt Service & Cash-flow Analysis ------------------------------------
            # Source: Commercial real estate lending standards (Fannie Mae/Freddie Mac)
            dscr_val: Optional[float] = None
            cash_flow_before_taxes_val: Optional[float] = None

            if annual_debt_service_val > 0:
                # DSCR calculation per commercial lending standards
                if not is_human_input("dscr"):
                    dscr_val = round(net_operating_income / annual_debt_service_val, 4)
                    
                    # Validation: DSCR should typically be 1.25x or higher for commercial loans
                    if dscr_val < 1.25:
                        logger.warning(f"Year {year_int}: DSCR ({dscr_val:.2f}) below typical lending requirement (1.25x)")
                
                if not is_human_input("cash_flow_before_taxes"):
                    cash_flow_before_taxes_val = net_operating_income - annual_debt_service_val
            else:
                # If no debt service, cash flow equals NOI
                if not is_human_input("cash_flow_before_taxes"):
                    cash_flow_before_taxes_val = net_operating_income

            # --- Valuation Metrics Calculation ----------------------------------------
            # Source: NCREIF and NAREIT valuation standards
            total_acquisition_cost_val = _to_float(df.at["total_acquisition_cost", year])
            cap_rate_val: Optional[float] = None
            gross_rent_multiplier_val: Optional[float] = None

            # Cap rate calculation and property value computation
            if not is_human_input("cap_rate"):
                current_cap_rate = _to_float(df.at["cap_rate", year])

                if total_acquisition_cost_val > 0:
                    # Calculate cap rate from NOI and acquisition cost per NCREIF standards
                    cap_rate_val = round((net_operating_income / total_acquisition_cost_val) * 100, 4)

                    # Validation: Cap rates typically range 3-12% depending on market and property type
                    if cap_rate_val and (cap_rate_val < 1 or cap_rate_val > 15):
                        logger.warning(f"Year {year_int}: Cap rate ({cap_rate_val:.2f}%) outside typical range (3-12%)")

                elif current_cap_rate == 0 and net_operating_income > 0:
                    # When AI agent doesn't return cap value, set default cap rate to 6% and compute property value
                    cap_rate_val = 6.0  # 6% default cap rate
                    computed_property_value = net_operating_income / (cap_rate_val / 100)

                    # Update acquisition cost with computed property value
                    if not is_human_input("total_acquisition_cost"):
                        df.at["total_acquisition_cost", year] = round(computed_property_value, 2)
                        total_acquisition_cost_val = computed_property_value
                        # Update year 1 acquisition cost if this is year 1
                        if year_int == 1:
                            year_1_acquisition_cost = computed_property_value
                        logger.info(f"Year {year_int}: Set default cap rate 6% and computed property value: ${computed_property_value:,.2f}")

            # Gross Rent Multiplier (GRM) calculation
            if total_acquisition_cost_val > 0 and not is_human_input("gross_rent_multiplier"):
                gross_rent_multiplier_val = (
                    round(total_acquisition_cost_val / rental_income_val, 4) if rental_income_val else None
                )

            # -----------------------------------------------------------------------------
            # Persist computed values back to the DataFrame (in-place mutation)
            # Source documentation embedded in field updates
            # -----------------------------------------------------------------------------
            if not is_human_input("effective_gross_income"):
                df.at["effective_gross_income", year] = round(effective_gross_income, 2)
            if not is_human_input("total_operating_expenses"):
                df.at["total_operating_expenses", year] = round(total_operating_expenses, 2)
            if not is_human_input("net_operating_income"):
                df.at["net_operating_income", year] = round(net_operating_income, 2)

            if dscr_val is not None and not is_human_input("dscr"):
                df.at["dscr", year] = dscr_val
            if cash_flow_before_taxes_val is not None and not is_human_input("cash_flow_before_taxes"):
                cfbt_val_rounded = round(cash_flow_before_taxes_val, 2)
                df.at["cash_flow_before_taxes", year] = cfbt_val_rounded
                # In absence of detailed tax inputs, assume same for after-tax
                # Note: This is a simplification - actual tax calculations would be more complex
                if not is_human_input("cash_flow_after_taxes"):
                    df.at["cash_flow_after_taxes", year] = cfbt_val_rounded
                yearly_cash_flows.append(cfbt_val_rounded)
            else:
                # Use existing value if human input or calculation failed
                existing_cfbt = _to_float(df.at["cash_flow_before_taxes", year])
                yearly_cash_flows.append(existing_cfbt)

            if cap_rate_val is not None and not is_human_input("cap_rate"):
                df.at["cap_rate", year] = cap_rate_val
            if gross_rent_multiplier_val is not None and not is_human_input("gross_rent_multiplier"):
                df.at["gross_rent_multiplier", year] = gross_rent_multiplier_val

            yearly_noi.append(round(net_operating_income, 2))

        # ------------------------------------------------------------------
        # Post-loop: Portfolio and cumulative metrics calculation
        # Source: CFA Institute portfolio performance measurement standards
        # ------------------------------------------------------------------
        
        # Cumulative Cash Flow calculation
        if "cumulative_cash_flow" in df.index:
            cumulative_vals: List[float] = []
            running_total = 0.0
            for idx, year in enumerate(years):
                year_int = int(str(year))
                if not is_human_input("cumulative_cash_flow") or override_user_input:
                    running_total += yearly_cash_flows[idx]
                    cumulative_vals.append(round(running_total, 2))
                else:
                    # Use existing human value and update running total
                    existing_val = _to_float(df.at["cumulative_cash_flow", year])
                    cumulative_vals.append(existing_val)
                    running_total = existing_val
            
            # Only update if not all values are human input
            if override_user_input or not all(
                int(str(year)) in metadata_by_year and 
                metadata_by_year[int(str(year))].get("attribution", {}).get("cumulative_cash_flow", {}).get("history", []) and
                metadata_by_year[int(str(year))]["attribution"]["cumulative_cash_flow"]["history"][-1].get("source") == "human"
                for year in years
            ):
                df.loc["cumulative_cash_flow", years] = cumulative_vals

        # Aggregated NOI calculation (portfolio-level metric)
        if "aggregated_noi" in df.index:
            noi_cumulative_vals: List[float] = []
            running_noi_total = 0.0
            for idx, year in enumerate(years):
                year_int = int(str(year))
                if not is_human_input("aggregated_noi") or override_user_input:
                    running_noi_total += yearly_noi[idx]
                    noi_cumulative_vals.append(round(running_noi_total, 2))
                else:
                    # Use existing human value and update running total
                    existing_val = _to_float(df.at["aggregated_noi", year])
                    noi_cumulative_vals.append(existing_val)
                    running_noi_total = existing_val
            
            # Only update if not all values are human input
            if override_user_input or not all(
                int(str(year)) in metadata_by_year and 
                metadata_by_year[int(str(year))].get("attribution", {}).get("aggregated_noi", {}).get("history", []) and
                metadata_by_year[int(str(year))]["attribution"]["aggregated_noi"]["history"][-1].get("source") == "human"
                for year in years
            ):
                df.loc["aggregated_noi", years] = noi_cumulative_vals

        # Return metrics calculation (requires acquisition cost data)
        # Source: CCIM Institute and CFA Institute return calculation standards
        for idx, year in enumerate(years):
            year_int = int(str(year))
            total_acquisition_cost_val = _to_float(df.at["total_acquisition_cost", year])
            if total_acquisition_cost_val > 0:
                # Equity Multiple calculation
                # Source: Private equity and real estate investment standards
                if not is_human_input("equity_multiple"):
                    equity_mult = 1 + (df.at["cumulative_cash_flow", year] / total_acquisition_cost_val)
                    df.at["equity_multiple", year] = round(equity_mult, 4)

                # Cash-on-Cash Return calculation
                # Source: CCIM Institute commercial investment analysis
                if not is_human_input("cash_on_cash_return"):
                    cfbt_val = _to_float(df.at["cash_flow_before_taxes", year])
                    coc_return = (cfbt_val / total_acquisition_cost_val) * 100
                    df.at["cash_on_cash_return", year] = round(coc_return, 4)

                # Blended cap-rate for portfolio analysis
                if "aggregated_noi" in df.index and not is_human_input("blended_cap_rate"):
                    blended_cr = (df.at["aggregated_noi", year] / total_acquisition_cost_val) * 100
                    df.at["blended_cap_rate", year] = round(blended_cr, 4)

        # Portfolio IRR calculation
        # Source: CFA Institute GIPS standards for investment performance calculation
        try:
            # Get acquisition cost and calculate IRR across the investment period
            acquisition_costs = [_to_float(df.at["total_acquisition_cost", year]) for year in years]
            initial_cost = next((cost for cost in acquisition_costs if cost > 0), None)
            
            if initial_cost and len(yearly_cash_flows) >= 3:  # Need at least 3 years for meaningful IRR
                # For real estate IRR, include terminal value in final year
                # Assume property is held and sold at cap rate-based valuation in final year
                final_year_noi = yearly_noi[-1] if yearly_noi else 0
                terminal_cap_rate = 0.06  # 6% assumed exit cap rate (consistent with default cap rate)
                
                if final_year_noi > 0:
                    # Calculate terminal value (sale proceeds) at end of investment period
                    terminal_value = final_year_noi / terminal_cap_rate
                    # Subtract remaining loan balance (simplified - assume 20% principal paydown over 10 years)
                    remaining_debt = initial_cost * 0.80 * 0.90  # 80% LTV, 90% remaining after 10 years
                    net_terminal_value = terminal_value - remaining_debt
                    
                    # Adjust final year cash flow to include sale proceeds
                    adjusted_yearly_cash_flows = yearly_cash_flows.copy()
                    adjusted_yearly_cash_flows[-1] += net_terminal_value
                    
                    # IRR calculation: [initial_investment (negative), annual_cash_flows..., final_year_with_sale]
                    cash_flows = [-initial_cost] + adjusted_yearly_cash_flows
                    
                    # Debug logging
                    logger.info("IRR calculation inputs:")
                    logger.info(f"  Initial cost: ${initial_cost:,.2f}")
                    logger.info(f"  Terminal value: ${terminal_value:,.2f}")
                    logger.info(f"  Remaining debt: ${remaining_debt:,.2f}")
                    logger.info(f"  Net terminal value: ${net_terminal_value:,.2f}")
                    logger.info(f"  Cash flows: {[f'${cf:,.0f}' for cf in cash_flows]}")
                    
                    try:
                        # Check if cash flows make sense for IRR calculation
                        if abs(cash_flows[0]) < 1000 or all(cf <= 0 for cf in cash_flows[1:]):
                            # If initial investment is too small or all subsequent cash flows are negative
                            logger.warning("IRR calculation skipped: inadequate cash flow structure")
                        else:
                            irr_val = npf.irr(cash_flows)
                            
                            # Convert to percentage and check validity
                            if irr_val is not None and not np.isnan(irr_val) and np.isfinite(irr_val):
                                irr_percentage = irr_val * 100
                                
                                # Validation: IRR should typically be -20% to +30% for real estate investments
                                if -20 <= irr_percentage <= 30:
                                    # Only update IRR if not human input
                                    for year in years:
                                        year_int = int(str(year))
                                        if not is_human_input("portfolio_irr"):
                                            df.at["portfolio_irr", year] = round(float(irr_percentage), 4)
                                    logger.info(f"Portfolio IRR calculated: {irr_percentage:.2f}% (including terminal sale value)")
                                else:
                                    logger.warning(f"Calculated IRR ({irr_percentage:.2f}%) outside reasonable range (-20% to +30%)")
                            else:
                                logger.warning("IRR calculation returned invalid result (NaN or infinite)")
                    except (ValueError, TypeError, OverflowError) as irr_error:
                        logger.warning(f"IRR calculation failed: {str(irr_error)}")
                else:
                    logger.info("Cannot calculate IRR: no positive NOI for terminal value estimation")
            else:
                logger.info("Insufficient data for IRR calculation: need acquisition cost and at least 3 years of data")
                
        except Exception as _err:  # noqa: BLE001
            logger.warning("Could not compute IRR - numpy-financial package may not be available: %s", _err)

        # Final validation summary
        logger.info(f"Financial computation completed for {len(years)} years")
        for year in years:
            noi = _to_float(df.at["net_operating_income", year])
            expenses = _to_float(df.at["total_operating_expenses", year])
            if noi > 0:
                logger.info(f"Year {year}: NOI=${noi:,.2f}, Expenses=${expenses:,.2f}")

        return df
            
    @classmethod
    async def _get_current_mortgage_rate(cls) -> float:
        """
        Get current 30-year mortgage rate from FRED API.
        
        Returns:
            Current mortgage rate as decimal (e.g., 0.0675 for 6.75%)
        """
        try:
            FRED_BASE_URL = "https://api.stlouisfed.org/fred"
            FRED_API_KEY = get_env('FRED_API_KEY')
            series_id = 'MORTGAGE30US'
            
            if not FRED_API_KEY:
                logger.warning("FRED_API_KEY not found, using default mortgage rate of 6.75%")
                return 0.0675
            
            async with httpx.AsyncClient() as client:
                response = await client.get(
                    f"{FRED_BASE_URL}/series/observations",
                    params={
                        'series_id': series_id,
                        'api_key': FRED_API_KEY,
                        'file_type': 'json',
                        'limit': 1,
                        'sort_order': 'desc'
                    },
                    headers={'Accept': 'application/json'}
                )
                
                if response.status_code == 200:
                    data = response.json()
                    observations = data.get('observations', [])
                    
                    if observations and len(observations) > 0:
                        rate_str = observations[0].get('value', '6.75')
                        if rate_str != '.':  # FRED uses '.' for missing data
                            mortgage_rate = float(rate_str) / 100  # Convert percentage to decimal
                            logger.info(f"Retrieved current mortgage rate from FRED: {mortgage_rate*100:.2f}%")
                            return mortgage_rate
                
                logger.warning("Could not retrieve mortgage rate from FRED, using default 6.75%")
                return 0.0675
                
        except Exception as e:
            logger.error(f"Error fetching mortgage rate from FRED: {str(e)}")
            return 0.0675  # Default fallback rate

class PropertFinancialMetadataService:
    """
    Service for managing property metadata.
    """

    @classmethod
    async def get_property_metadata(cls, prop_id: str, year: int) -> Optional[Dict[str, Any]]:
        """
        Get property metadata.
        """
        try:
            # Get the client using the standard function
            client = await get_db_client()
            
            response = client.table("prop_financials").select("metadata").eq("prop_id", prop_id).eq("year", year).execute()
            if response.data:
                return response.data[0]
            else:
                return None
        except Exception as e:
            logger.error(f"Error getting property metadata: {str(e)}")
            return None
        
    @classmethod
    async def set_property_metadata(cls, prop_id: str, year: int, metadata: Dict[str, Any]) -> Optional[list]:
        """
        Set property metadata.
        """
        try:
            # Get the client using the standard function
            client = await get_db_client()
            response = client.table("prop_financials").update({"metadata": metadata}).eq("prop_id", prop_id).eq("year", year).execute()
            return response.data
        except Exception as e:
            logger.error(f"Error setting property metadata: {str(e)}")
            return None

    @classmethod
    async def batch_set_metadata_attribution(cls, prop_id: str, year: int, field_names: List[str]) -> Optional[list]:
        """
        Batch set metadata attribution for multiple fields to avoid blocking the event loop.

        Args:
            prop_id: Property ID
            year: Financial year
            field_names: List of field names that were changed

        Returns:
            List of updated records or None if error occurs
        """
        try:
            if not field_names:
                return None

            # Check which fields have human edits and filter them out
            non_human_fields = []
            for field_name in field_names:
                is_human_edited = await cls.is_field_human_edited(prop_id, year, field_name)
                if not is_human_edited:
                    non_human_fields.append(field_name)
                else:
                    logger.info(f"Skipping AI attribution for human-edited field {field_name} in year {year}")

            if not non_human_fields:
                logger.info("All fields are human-edited, skipping AI attribution")
                return None

            # Get the client using the standard function
            client = await get_db_client()

            # Get current metadata once
            response = client.table("prop_financials").select("metadata").eq("prop_id", prop_id).eq("year", year).execute()
            current_metadata = response.data[0].get("metadata", {}) if response.data else {}

            # Get current attribution list or initialize empty list
            attribution_list = current_metadata.get("attribution", []) if current_metadata else []

            # Append new attribution entries for all non-human fields
            current_time = datetime.now().isoformat()
            for field_name in non_human_fields:
                attribution_list.append({
                    "year": year,
                    "entity": "ai",
                    "row_name": field_name,
                    "datetime_changed": current_time
                })

            # Update metadata with new attribution list
            if current_metadata:
                current_metadata["attribution"] = attribution_list
            else:
                current_metadata = {"attribution": attribution_list}

            # Single database update with all attribution changes
            response = client.table("prop_financials").update({"metadata": current_metadata}).eq("prop_id", prop_id).eq("year", year).execute()
            logger.debug(f"Batch added AI attribution for {len(non_human_fields)} fields in year {year}: {non_human_fields}")
            return response.data
        except Exception as e:
            logger.error(f"Error in batch metadata attribution: {str(e)}")
            # Fallback to individual updates if batch fails
            logger.info("Falling back to individual attribution updates")
            for field_name in field_names:
                try:
                    await cls.set_metadata_attribution(prop_id, year, field_name)
                except Exception as individual_error:
                    logger.error(f"Error in individual attribution update for {field_name}: {str(individual_error)}")
            return None

    @classmethod
    async def append_property_metadata(cls, metadata: dict, prop_id: str, year: int) -> Optional[list]:
        """
        Append property metadata to the existing metadata without overriding it.
        Appends the new metadata to the task_type key as a list item.
        
        Args:
            prop_id: Property ID
            year: Financial year
            metadata: New metadata to append
        
        Returns:
            Updated metadata or None if operation failed
        """
        try:
            # Get the client using the standard function
            client = await get_db_client()
            
            # First get existing metadata
            existing_data = await cls.get_property_metadata(prop_id, year)
            
            # Initialize new metadata structure
            updated_metadata = existing_data.get("metadata", {}) if existing_data else {}
            
            # Get the task type from the metadata
            task_type = metadata.get("task_type")
            
            if task_type:
                # Initialize the task_type list if it doesn't exist
                if task_type not in updated_metadata:
                    updated_metadata[task_type] = []
                
                # Append the new metadata to the task_type list
                updated_metadata[task_type].append(metadata)
            else:
                # If no task_type, just merge the metadata
                updated_metadata.update(metadata)
            
            # Update the database with the merged metadata
            response = client.table("prop_financials").update({"metadata": updated_metadata}).eq("prop_id", prop_id).eq("year", year).execute()
            return response.data
        except Exception as e:
            logger.error(f"Error appending property metadata: {str(e)}")
            return None
    
    @classmethod
    async def override_property_metadata(cls, metadata: dict, prop_id: str, year: int, key: str) -> Optional[list]:
        """
        Override a specific key in the property metadata without affecting other keys.
        
        Args:
            prop_id: Property ID
            year: Financial year
            metadata: New metadata to set for the specified key
            key: The specific key to override in the metadata
        
        Returns:
            Updated metadata or None if operation failed
        """
        try:
            # Get the client using the standard function
            client = await get_db_client()
            
            # First get existing metadata
            existing_data = await cls.get_property_metadata(prop_id, year)
            
            # Initialize new metadata structure
            updated_metadata = existing_data.get("metadata", {}) if existing_data else {}
            
            # Override just the specified key
            updated_metadata[key] = metadata
            
            # Update the database with the modified metadata
            response = client.table("prop_financials").update({"metadata": updated_metadata}).eq("prop_id", prop_id).eq("year", year).execute()
            return response.data
        except Exception as e:
            logger.error(f"Error overriding property metadata key: {str(e)}")
            return None
        
class CherreDataService:
    """
    Service for managing Cherre data.
    """
    
    
