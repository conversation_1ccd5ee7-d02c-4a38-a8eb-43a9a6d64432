"""
Stripe webhook event handlers.
"""
from typing import Dict, Any
from datetime import datetime

from app.utils.logger import get_logger
from app.repository.db import get_db_client
from app.models.database import WorkspaceSubscriptions, Workspaces
import stripe

logger = get_logger(__name__)

async def handle_checkout_completed(event_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Handle a checkout.session.completed event.
    
    When a user completes checkout, we need to:
    1. Extract metadata from the session
    2. Create or update workspace_subscriptions entries for each state
    3. Update workspace billing_admin_id with the user_id
    4. Activate any states in "pending_payment" status
    
    Args:
        event_data: The Stripe event data
        
    Returns:
        Dict with processing status
    """
    db = await get_db_client()
    session = event_data.get("data", {}).get("object", {})
    
    if not session:
        return {"success": False, "error": "Session data not found in event"}
    
    # Get metadata from the session
    metadata = session.get("metadata", {})
    user_id = metadata.get("user_id")
    workspace_id = metadata.get("workspace_id")
    states_str = metadata.get("states", "")
    
    if not user_id or not workspace_id or not states_str:
        logger.error("Missing required metadata in checkout session")
        return {
            "success": False,
            "error": "Missing required metadata in checkout session",
            "metadata": metadata
        }
    
    # Get the subscription ID from the session
    subscription_id = session.get("subscription")
    if not subscription_id:
        logger.error("No subscription ID found in completed checkout session")
        return {
            "success": False,
            "error": "No subscription ID found in completed checkout session"
        }
    
    # Parse the states
    states = states_str.split(",")
    logger.info(f"Processing {len(states)} workspace subscriptions for workspace {workspace_id}")
    
    # Track created and updated entries
    created_entries = []
    updated_entries = []
    activated_pending_entries = []
    
    try:
        # Get all existing subscriptions for the workspace - we need full data, not just state codes
        existing_subscriptions_result = db.table(WorkspaceSubscriptions.Config.table) \
            .select("*") \
            .eq("workspace_id", workspace_id) \
            .execute()
            
        # Create a lookup map of state_code -> subscription record
        existing_subscriptions_map = {}
        if existing_subscriptions_result.data:
            for sub in existing_subscriptions_result.data:
                if sub.get("state_code"):
                    existing_subscriptions_map[sub.get("state_code")] = sub
        
        # Verify the workspace exists
        workspace_result = db.table(Workspaces.Config.table) \
            .select("id") \
            .eq("id", workspace_id) \
            .execute()
            
        if not workspace_result.data:
            logger.error(f"Workspace {workspace_id} not found")
            return {
                "success": False,
                "error": f"Workspace {workspace_id} not found"
            }
        
        now = datetime.utcnow().isoformat()
        
        # Update the workspace billing_admin_id if not already set
        workspace = workspace_result.data[0]
        if not workspace.get("billing_admin_id"):
            logger.info(f"Setting billing_admin_id to {user_id} for workspace {workspace_id}")
            db.table(Workspaces.Config.table) \
                .update({"billing_admin_id": user_id, "updated_at": now}) \
                .eq("id", workspace_id) \
                .execute()
        
        # Process each state - create or update entries
        for state_code in states:
            existing_subscription = existing_subscriptions_map.get(state_code)
            
            if existing_subscription:
                # Update existing entry with new subscription ID and ensure it's active
                logger.info(f"Updating existing subscription for state {state_code} with new subscription ID")
                update_data = {
                    "stripe_subscription_id": subscription_id,
                    "status": "active",  # Ensure it's active even if it was previously cancelled or inactive
                    "updated_at": now
                }
                
                result = db.table(WorkspaceSubscriptions.Config.table) \
                    .update(update_data) \
                    .eq("id", existing_subscription["id"]) \
                    .execute()
                
                if result.data:
                    updated_entries.append(result.data[0])
                else:
                    logger.error(f"Failed to update subscription entry for state {state_code}")
            else:
                # Create a new entry
                logger.info(f"Creating new subscription entry for state {state_code}")
                entry_data = {
                    "workspace_id": workspace_id,
                    "state_code": state_code,
                    "status": "active",
                    "stripe_subscription_id": subscription_id,
                    "created_at": now,
                    "updated_at": now
                }
                
                result = db.table(WorkspaceSubscriptions.Config.table).insert(entry_data).execute()
                if result.data:
                    created_entries.append(result.data[0])
                else:
                    logger.error(f"Failed to create subscription entry for state {state_code}")
        
        # Additionally, we should check for any "pending_payment" states and activate them
        # since this webhook means a payment went through successfully
        pending_states_result = db.table(WorkspaceSubscriptions.Config.table) \
            .select("*") \
            .eq("workspace_id", workspace_id) \
            .eq("status", "pending_payment") \
            .execute()
            
        if pending_states_result.data:
            # Update all pending states to active since payment is now complete
            for pending_sub in pending_states_result.data:
                result = db.table(WorkspaceSubscriptions.Config.table) \
                    .update({
                        "status": "active",
                        "stripe_subscription_id": subscription_id,  # Ensure the subscription ID is updated 
                        "updated_at": now
                    }) \
                    .eq("id", pending_sub["id"]) \
                    .execute()
                
                if result.data:
                    activated_pending_entries.append(result.data[0])
                    logger.info(f"Activated pending state {pending_sub.get('state_code')} after successful payment")
        
        logger.info(f"Created {len(created_entries)} new subscription entries, updated {len(updated_entries)} existing entries, and activated {len(activated_pending_entries)} pending entries")
        
        return {
            "success": True,
            "status": "processed",
            "subscription_id": subscription_id,
            "created_entries": len(created_entries),
            "updated_entries": len(updated_entries),
            "activated_pending_entries": len(activated_pending_entries)
        }
    except Exception as e:
        logger.error(f"Error processing checkout.session.completed event: {str(e)}")
        return {
            "success": False,
            "error": str(e)
        }

async def handle_subscription_created(event_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Handle a customer.subscription.created event.
    
    This event is fired when a subscription is created directly via API
    rather than through a checkout session.
    
    Args:
        event_data: The Stripe event data
        
    Returns:
        Dict with processing status
    """
    # For now, we just log this as subscription entries are created 
    # through checkout.session.completed
    logger.info("Received subscription.created event")
    
    # Extract subscription data
    subscription = event_data.get("data", {}).get("object", {})
    subscription_id = subscription.get("id")
    
    if not subscription_id:
        return {"success": False, "error": "Subscription ID not found in event"}
    
    # Check metadata to see if we need to handle this
    metadata = subscription.get("metadata", {})
    
    # If this has already been handled by checkout.session.completed
    # or doesn't have our metadata, we can skip
    if not metadata.get("workspace_id"):
        logger.info(f"Subscription {subscription_id} doesn't have workspace metadata, skipping")
        return {"success": True, "status": "skipped"}
    
    # Otherwise, log the receipt for audit purposes
    return {
        "success": True, 
        "status": "processed",
        "subscription_id": subscription_id
    }

async def handle_subscription_updated(event_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Handle a customer.subscription.updated event.
    
    This event is fired when a subscription is updated, such as when
    a plan changes, quantity changes, or when billing details are updated.
    
    Args:
        event_data: The Stripe event data
        
    Returns:
        Dict with processing status
    """
    logger.info("Received subscription.updated event")
    
    # Extract subscription data
    subscription = event_data.get("data", {}).get("object", {})
    subscription_id = subscription.get("id")
    
    if not subscription_id:
        return {"success": False, "error": "Subscription ID not found in event"}
    
    # Get previous subscription state
    previous_attributes = event_data.get("data", {}).get("previous_attributes", {})
    
    # Check if this is a status change
    if "status" in previous_attributes:
        old_status = previous_attributes.get("status")
        new_status = subscription.get("status")
        
        logger.info(f"Subscription {subscription_id} status changed: {old_status} -> {new_status}")
        
        # Handle specific status changes
        if new_status == "unpaid" or new_status == "past_due":
            # We might want to notify someone or mark the account
            logger.warning(f"Subscription {subscription_id} is now {new_status}")
        
        elif new_status == "canceled":
            # Handle cancellation - mark all subscriptions as inactive
            # This might also be handled by subscription.deleted event, but we handle it here too
            logger.info(f"Subscription {subscription_id} was canceled - marking as inactive")
            return await handle_subscription_deleted(event_data)
        
        elif new_status == "paused":
            # Subscription was paused - mark all subscriptions as inactive
            logger.info(f"Subscription {subscription_id} was paused - marking as inactive")
            return await handle_subscription_paused(event_data)
        
        elif new_status == "active":
            # Determine what kind of reactivation this is
            if old_status == "paused":
                # Subscription was resumed from paused state
                logger.info(f"Subscription {subscription_id} was resumed from paused state")
                return await handle_subscription_resumed(event_data)
            elif old_status in ["unpaid", "past_due"]:
                # Subscription was reactivated from payment issue
                logger.info(f"Subscription {subscription_id} was reactivated from {old_status}")
                # Reactivate the subscriptions
                db = await get_db_client()
                now = datetime.utcnow().isoformat()
                
                result = db.table(WorkspaceSubscriptions.Config.table) \
                    .update({"status": "active", "updated_at": now}) \
                    .eq("stripe_subscription_id", subscription_id) \
                    .eq("status", "inactive") \
                    .execute()
                    
                updated_count = len(result.data) if result.data else 0
                logger.info(f"Reactivated {updated_count} workspace subscriptions")
            else:
                logger.info(f"Subscription {subscription_id} changed to active from {old_status} - no special handling needed")
    
    # For now, we just acknowledge receipt
    return {
        "success": True,
        "status": "processed",
        "subscription_id": subscription_id
    }

async def handle_subscription_deleted(event_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Handle a customer.subscription.deleted event.
    
    When a subscription is cancelled/deleted, we need to:
    1. Find all workspace_subscriptions with the given subscription_id
    2. Mark them as inactive
    
    Args:
        event_data: The Stripe event data
        
    Returns:
        Dict with processing status
    """
    db = await get_db_client()
    subscription = event_data.get("data", {}).get("object", {})
    
    if not subscription:
        return {"success": False, "error": "Subscription data not found in event"}
    
    subscription_id = subscription.get("id")
    if not subscription_id:
        return {"success": False, "error": "Subscription ID not found in event"}
    
    try:
        # Update all workspace subscriptions with this subscription ID
        now = datetime.utcnow().isoformat()
        update_data = {
            "status": "cancelled",
            "updated_at": now
        }
        
        result = db.table(WorkspaceSubscriptions.Config.table) \
            .update(update_data) \
            .eq("stripe_subscription_id", subscription_id) \
            .execute()
        
        updated_count = len(result.data) if result.data else 0
        logger.info(f"Marked {updated_count} workspace subscriptions as cancelled")
        
        return {
            "success": True,
            "status": "processed",
            "updated_entries": updated_count
        }
    except Exception as e:
        logger.error(f"Error processing subscription.deleted event: {str(e)}")
        return {
            "success": False,
            "error": str(e)
        }

async def handle_subscription_paused(event_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Handle a customer.subscription.paused event.
    
    When a subscription is paused, we need to mark all workspace_subscriptions
    with this subscription ID as inactive.
    
    Args:
        event_data: The Stripe event data
        
    Returns:
        Dict with processing status
    """
    db = await get_db_client()
    subscription = event_data.get("data", {}).get("object", {})
    
    if not subscription:
        return {"success": False, "error": "Subscription data not found in event"}
    
    subscription_id = subscription.get("id")
    if not subscription_id:
        return {"success": False, "error": "Subscription ID not found in event"}
    
    try:
        # Update all workspace subscriptions with this subscription ID to inactive
        now = datetime.utcnow().isoformat()
        update_data = {
            "status": "inactive",
            "updated_at": now
        }
        
        result = db.table(WorkspaceSubscriptions.Config.table) \
            .update(update_data) \
            .eq("stripe_subscription_id", subscription_id) \
            .execute()
        
        updated_count = len(result.data) if result.data else 0
        logger.info(f"Marked {updated_count} workspace subscriptions as inactive due to subscription pause")
        
        return {
            "success": True,
            "status": "processed",
            "updated_entries": updated_count
        }
    except Exception as e:
        logger.error(f"Error processing subscription.paused event: {str(e)}")
        return {
            "success": False,
            "error": str(e)
        }

async def handle_subscription_resumed(event_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Handle a customer.subscription.resumed event.
    
    When a subscription is resumed, we need to mark all workspace_subscriptions
    with this subscription ID as active again.
    
    Args:
        event_data: The Stripe event data
        
    Returns:
        Dict with processing status
    """
    db = await get_db_client()
    subscription = event_data.get("data", {}).get("object", {})
    
    if not subscription:
        return {"success": False, "error": "Subscription data not found in event"}
    
    subscription_id = subscription.get("id")
    if not subscription_id:
        return {"success": False, "error": "Subscription ID not found in event"}
    
    try:
        # Update all workspace subscriptions with this subscription ID to active
        now = datetime.utcnow().isoformat()
        update_data = {
            "status": "active",
            "updated_at": now
        }
        
        result = db.table(WorkspaceSubscriptions.Config.table) \
            .update(update_data) \
            .eq("stripe_subscription_id", subscription_id) \
            .eq("status", "inactive") \
            .execute()
        
        updated_count = len(result.data) if result.data else 0
        logger.info(f"Marked {updated_count} workspace subscriptions as active due to subscription resumption")
        
        return {
            "success": True,
            "status": "processed",
            "updated_entries": updated_count
        }
    except Exception as e:
        logger.error(f"Error processing subscription.resumed event: {str(e)}")
        return {
            "success": False,
            "error": str(e)
        }

async def handle_subscription_trial_will_end(event_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Handle a customer.subscription.trial_will_end event.
    
    This event is sent 3 days before a trial ends.
    We can use this to notify the customer.
    
    Args:
        event_data: The Stripe event data
        
    Returns:
        Dict with processing status
    """
    subscription = event_data.get("data", {}).get("object", {})
    
    if not subscription:
        return {"success": False, "error": "Subscription data not found in event"}
    
    subscription_id = subscription.get("id")
    if not subscription_id:
        return {"success": False, "error": "Subscription ID not found in event"}
    
    # Get trial end date
    trial_end = subscription.get("trial_end")
    
    logger.info(f"Trial will end soon for subscription {subscription_id}, trial end: {trial_end}")
    
    # For now, just log the event. In the future, we could send notifications.
    return {
        "success": True,
        "status": "processed",
        "subscription_id": subscription_id,
        "trial_end": trial_end
    }

async def handle_payment_succeeded(event_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Handle a payment_intent.succeeded or invoice.paid event.
    
    When a payment succeeds, we need to check if there are any pending_payment states
    associated with the customer and activate them.
    
    Args:
        event_data: The Stripe event data
        
    Returns:
        Dict with processing status
    """
    db = await get_db_client()
    
    # Get the event object (payment intent or invoice)
    event_object = event_data.get("data", {}).get("object", {})
    
    if not event_object:
        return {"success": False, "error": "Event object not found in event data"}
    
    # Get customer ID
    customer_id = event_object.get("customer")
    if not customer_id:
        logger.info("No customer ID found in payment event")
        return {"success": True, "status": "skipped", "reason": "No customer ID in event"}
    
    # Get associated subscription ID
    subscription_id = None
    invoice_id = None
    
    # For payment_intent.succeeded event
    if event_data.get("type") == "payment_intent.succeeded":
        invoice_id = event_object.get("invoice")
        if invoice_id:
            try:
                # Fetch the invoice to get the subscription ID
                invoice = stripe.Invoice.retrieve(invoice_id)
                subscription_id = invoice.get("subscription")
                
                # If no subscription ID directly on invoice, check metadata
                if not subscription_id and invoice.get("metadata"):
                    subscription_id = invoice.get("metadata", {}).get("subscription_id")
                    
                # Check for state information in metadata
                added_states_str = invoice.get("metadata", {}).get("added_states")
                workspace_id = invoice.get("metadata", {}).get("workspace_id")
                
                if not subscription_id and workspace_id:
                    # Try to find the subscription ID for this workspace
                    subscription_result = db.table("workspace_subscriptions") \
                        .select("stripe_subscription_id") \
                        .eq("workspace_id", workspace_id) \
                        .eq("status", "active") \
                        .limit(1) \
                        .execute()
                    
                    if subscription_result.data:
                        subscription_id = subscription_result.data[0].get("stripe_subscription_id")
                        logger.info(f"Found subscription ID {subscription_id} for workspace {workspace_id} from database")
            except Exception as e:
                logger.error(f"Error fetching invoice {invoice_id}: {str(e)}")
    
    # For invoice.paid event
    elif event_data.get("type") == "invoice.paid":
        subscription_id = event_object.get("subscription")
        invoice_id = event_object.get("id")
        
        # If no subscription ID directly on invoice, check metadata
        if not subscription_id and event_object.get("metadata"):
            subscription_id = event_object.get("metadata", {}).get("subscription_id")
            
        # Check for state information in metadata
        added_states_str = event_object.get("metadata", {}).get("added_states")
        workspace_id = event_object.get("metadata", {}).get("workspace_id")
        
        if not subscription_id and workspace_id:
            # Try to find the subscription ID for this workspace
            subscription_result = db.table("workspace_subscriptions") \
                .select("stripe_subscription_id") \
                .eq("workspace_id", workspace_id) \
                .eq("status", "active") \
                .limit(1) \
                .execute()
            
            if subscription_result.data:
                subscription_id = subscription_result.data[0].get("stripe_subscription_id")
                logger.info(f"Found subscription ID {subscription_id} for workspace {workspace_id} from database")
                
        # If still no subscription ID but we have an invoice ID, try to get it from the lines
        if not subscription_id and invoice_id:
            try:
                invoice_with_lines = stripe.Invoice.retrieve(
                    invoice_id,
                    expand=["lines"]
                )
                
                # Check invoice line items for subscription
                if invoice_with_lines.get("lines") and invoice_with_lines.get("lines").get("data"):
                    for line in invoice_with_lines.get("lines").get("data"):
                        if line.get("subscription"):
                            subscription_id = line.get("subscription")
                            logger.info(f"Found subscription ID {subscription_id} from invoice line items")
                            break
            except Exception as e:
                logger.error(f"Error fetching invoice with lines {invoice_id}: {str(e)}")
    
    # If we couldn't find a subscription ID but have an invoice, check the customer's subscriptions
    if not subscription_id and customer_id and invoice_id:
        try:
            # Get all active subscriptions for this customer
            subscriptions = stripe.Subscription.list(
                customer=customer_id,
                status="active",
                limit=5
            )
            
            if subscriptions and subscriptions.data:
                # Use the most recent active subscription
                subscription_id = subscriptions.data[0].id
                logger.info(f"Using most recent subscription {subscription_id} for customer {customer_id}")
        except Exception as e:
            logger.error(f"Error fetching customer subscriptions: {str(e)}")
    
    if not subscription_id:
        logger.info(f"No subscription ID found for payment event with invoice {invoice_id}")
        # Even without a subscription ID, we'll try to find pending states by customer
        
    # Find user_id from customer_id
    # First try to get from Stripe's customer metadata
    user_id = None
    try:
        customer = stripe.Customer.retrieve(customer_id)
        user_id = customer.get("metadata", {}).get("user_id")
    except Exception as e:
        logger.error(f"Error fetching customer {customer_id}: {str(e)}")
    
    # If not found in Stripe metadata, check our own database
    if not user_id:
        user_result = db.table("users").select("id").eq("stripe_customer_id", customer_id).execute()
        if user_result.data:
            user_id = user_result.data[0].get("id")
    
    # Get specific added states from metadata if available
    added_states = []
    if added_states_str:
        added_states = added_states_str.split(",")
        logger.info(f"Found added states in metadata: {added_states}")
        
    # Process workspace-specific states if we have workspace_id
    if workspace_id and (subscription_id or user_id):
        # Handle explicit metadata about added states
        if added_states:
            logger.info(f"Processing explicit added states for workspace {workspace_id}: {added_states}")
            now = datetime.utcnow().isoformat()
            
            for state_code in added_states:
                # Check if this state already exists
                state_result = db.table("workspace_subscriptions") \
                    .select("id") \
                    .eq("workspace_id", workspace_id) \
                    .eq("state_code", state_code) \
                    .execute()
                
                if state_result.data:
                    # State already exists, update it to active
                    db.table("workspace_subscriptions") \
                        .update({
                            "status": "active",
                            "updated_at": now,
                            "stripe_subscription_id": subscription_id
                        }) \
                        .eq("id", state_result.data[0]["id"]) \
                        .execute()
                    logger.info(f"Updated existing state {state_code} to active for workspace {workspace_id}")
                else:
                    # Create new state entry
                    db.table("workspace_subscriptions") \
                        .insert({
                            "workspace_id": workspace_id,
                            "state_code": state_code,
                            "status": "active",
                            "stripe_subscription_id": subscription_id,
                            "created_at": now,
                            "updated_at": now
                        }) \
                        .execute()
                    logger.info(f"Created new active state {state_code} for workspace {workspace_id}")
            
            return {
                "success": True,
                "status": "processed",
                "invoice_id": invoice_id,
                "subscription_id": subscription_id,
                "workspace_id": workspace_id,
                "added_states": added_states
            }
    
    # Find workspaces associated with the subscription or the user
    workspace_ids = []
    
    # If we have a subscription ID, look up associated workspaces
    if subscription_id:
        subscription_result = db.table("workspace_subscriptions") \
            .select("workspace_id") \
            .eq("stripe_subscription_id", subscription_id) \
            .execute()
        
        if subscription_result.data:
            workspace_ids = list(set([sub["workspace_id"] for sub in subscription_result.data if sub.get("workspace_id")]))
    
    # If we don't have workspaces by subscription but have user_id, find workspaces by billing admin
    if not workspace_ids and user_id:
        workspace_result = db.table("workspaces") \
            .select("id") \
            .eq("billing_admin_id", user_id) \
            .execute()
        
        if workspace_result.data:
            workspace_ids = [w["id"] for w in workspace_result.data]
    
    # Find and activate any pending payment states
    activated_states = []
    now = datetime.utcnow().isoformat()
    
    for workspace_id in workspace_ids:
        # Find any pending payment states for this workspace
        pending_states_result = db.table("workspace_subscriptions") \
            .select("*") \
            .eq("workspace_id", workspace_id) \
            .eq("status", "pending_payment") \
            .execute()
        
        if pending_states_result.data:
            for pending_sub in pending_states_result.data:
                # Activate the state
                result = db.table("workspace_subscriptions") \
                    .update({
                        "status": "active",
                        "updated_at": now,
                        "stripe_subscription_id": subscription_id or pending_sub.get("stripe_subscription_id")
                    }) \
                    .eq("id", pending_sub["id"]) \
                    .execute()
                
                if result.data:
                    state_code = pending_sub.get("state_code")
                    activated_states.append(state_code)
                    logger.info(f"Activated state {state_code} for workspace {workspace_id} after payment succeeded")
    
    if activated_states:
        logger.info(f"Activated {len(activated_states)} pending states after payment succeeded: {', '.join(activated_states)}")
        return {
            "success": True,
            "status": "processed",
            "subscription_id": subscription_id,
            "activated_states": activated_states,
            "workspace_ids": workspace_ids
        }
    else:
        logger.info("No pending states found to activate for payment event")
        return {
            "success": True,
            "status": "skipped",
            "reason": "No pending states found to activate"
        }

async def handle_invoice_payment_succeeded(event_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Handle an invoice.payment_succeeded event.
    
    This is very similar to invoice.paid but may be sent in different scenarios.
    We'll reuse the payment_succeeded handler for consistent processing.
    
    Args:
        event_data: The Stripe event data
        
    Returns:
        Dict with processing status
    """
    logger.info("Processing invoice.payment_succeeded as payment_succeeded")
    return await handle_payment_succeeded(event_data)

async def handle_payment_intent_created(event_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Handle a payment_intent.created event.
    
    We just log this event for auditing purposes but don't take any specific action.
    
    Args:
        event_data: The Stripe event data
        
    Returns:
        Dict with acknowledgment of receipt
    """
    payment_intent = event_data.get("data", {}).get("object", {})
    
    if not payment_intent:
        return {"success": False, "error": "Payment intent not found in event"}
    
    payment_intent_id = payment_intent.get("id")
    amount = payment_intent.get("amount", 0) / 100  # Convert cents to dollars
    
    logger.info(f"Payment intent created: {payment_intent_id} for ${amount}")
    
    return {
        "success": True,
        "status": "acknowledged",
        "payment_intent_id": payment_intent_id
    }

async def handle_payment_intent_failed(event_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Handle a payment_intent.payment_failed event.
    
    We log the failure but don't change subscription state as the user may retry payment.
    For pending_payment states, we keep them pending until payment succeeds or until 
    a specific time period has passed (implemented separately).
    
    Args:
        event_data: The Stripe event data
        
    Returns:
        Dict with status of handling
    """
    payment_intent = event_data.get("data", {}).get("object", {})
    
    if not payment_intent:
        return {"success": False, "error": "Payment intent not found in event"}
    
    payment_intent_id = payment_intent.get("id")
    invoice_id = payment_intent.get("invoice")
    error = payment_intent.get("last_payment_error", {})
    
    error_message = error.get("message", "Unknown error")
    error_code = error.get("code", "unknown")
    
    logger.warning(f"Payment failed for intent {payment_intent_id}: {error_code} - {error_message}")
    
    # We could add additional logic here to mark a state as failed after multiple retries,
    # but for now we'll keep pending states as pending to allow retry
    
    return {
        "success": True,
        "status": "logged",
        "payment_intent_id": payment_intent_id,
        "invoice_id": invoice_id,
        "error_code": error_code,
        "error_message": error_message
    }

async def handle_invoice_payment_failed(event_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Handle an invoice.payment_failed event.
    
    This is sent when an automatic payment attempt fails.
    We want to keep the states in pending_payment status to allow retry,
    but we might want to notify admin in the future.
    
    Args:
        event_data: The Stripe event data
        
    Returns:
        Dict with processing status
    """
    invoice = event_data.get("data", {}).get("object", {})
    
    if not invoice:
        return {"success": False, "error": "Invoice data not found in event"}
    
    invoice_id = invoice.get("id")
    subscription_id = invoice.get("subscription")
    customer_id = invoice.get("customer")
    
    if not invoice_id:
        return {"success": False, "error": "Invoice ID not found in event"}
    
    # Get payment error details if available
    payment_intent_id = invoice.get("payment_intent")
    attempt_count = invoice.get("attempt_count", 0)
    next_payment_attempt = invoice.get("next_payment_attempt")
    
    logger.warning(f"Invoice {invoice_id} payment failed with payment intent {payment_intent_id} (attempt {attempt_count}), next attempt: {next_payment_attempt}")
    
    if subscription_id:
        # Look up the subscription to get more details
        try:
            # Find workspaces associated with the subscription
            db = await get_db_client()
            subscription_result = db.table("workspace_subscriptions") \
                .select("workspace_id") \
                .eq("stripe_subscription_id", subscription_id) \
                .execute()
            
            if subscription_result.data:
                workspace_ids = list(set([sub["workspace_id"] for sub in subscription_result.data if sub.get("workspace_id")]))
                logger.info(f"Failed payment affects workspaces: {workspace_ids}")
        except Exception as e:
            logger.error(f"Error looking up workspaces for subscription {subscription_id}: {str(e)}")
    
    # For now, we just log the failure. In the future we could implement:
    # 1. Notification to the billing admin
    # 2. Status update after multiple consecutive failures
    # 3. Grace period handling
    
    return {
        "success": True,
        "status": "logged",
        "invoice_id": invoice_id,
        "subscription_id": subscription_id,
        "customer_id": customer_id,
        "attempt_count": attempt_count,
        "next_payment_attempt": next_payment_attempt
    }

async def handle_payment_action_required(event_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Handle events that require payment action from the customer.
    
    This includes invoice.payment_action_required and payment_intent.requires_action
    which indicate the customer needs to take additional steps to complete payment
    (e.g., 3D Secure authentication).
    
    Args:
        event_data: The Stripe event data
        
    Returns:
        Dict with processing status
    """
    # The object might be an invoice or a payment intent
    event_object = event_data.get("data", {}).get("object", {})
    
    if not event_object:
        return {"success": False, "error": "Event object not found"}
    
    event_type = event_data.get("type", "")
    object_id = event_object.get("id")
    customer_id = event_object.get("customer")
    
    # Extract relevant IDs based on event type
    payment_intent_id = None
    invoice_id = None
    subscription_id = None
    
    if event_type == "invoice.payment_action_required":
        invoice_id = object_id
        payment_intent_id = event_object.get("payment_intent")
        subscription_id = event_object.get("subscription")
    elif event_type == "payment_intent.requires_action":
        payment_intent_id = object_id
        invoice_id = event_object.get("invoice")
        # Need to look up subscription_id from invoice if available
        if invoice_id:
            try:
                invoice = stripe.Invoice.retrieve(invoice_id)
                subscription_id = invoice.get("subscription")
            except Exception as e:
                logger.error(f"Error retrieving invoice {invoice_id}: {str(e)}")
    
    logger.info(f"Payment action required for {event_type}: {object_id}")
    logger.info(f"Associated IDs - Invoice: {invoice_id}, Payment Intent: {payment_intent_id}, Subscription: {subscription_id}")
    
    if subscription_id:
        try:
            # Find workspaces associated with the subscription
            db = await get_db_client()
            subscription_result = db.table("workspace_subscriptions") \
                .select("workspace_id, state_code") \
                .eq("stripe_subscription_id", subscription_id) \
                .eq("status", "pending_payment") \
                .execute()
            
            if subscription_result.data:
                pending_states = [sub.get("state_code") for sub in subscription_result.data if sub.get("state_code")]
                workspace_ids = list(set([sub["workspace_id"] for sub in subscription_result.data if sub.get("workspace_id")]))
                
                logger.info(f"Payment action required for workspaces {workspace_ids} with pending states: {pending_states}")
                
                # In the future, we could notify the user about the required action
                # e.g., send an email with a link to complete the payment
        except Exception as e:
            logger.error(f"Error looking up subscription details: {str(e)}")
    
    return {
        "success": True,
        "status": "logged",
        "event_type": event_type,
        "object_id": object_id,
        "invoice_id": invoice_id,
        "payment_intent_id": payment_intent_id,
        "subscription_id": subscription_id,
        "customer_id": customer_id
    }

async def handle_invoice_upcoming(event_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Handle an invoice.upcoming event.
    
    This event is sent 1-3 hours before an invoice is scheduled to be created.
    We can use this to verify the customer has a valid payment method and
    alert them if there might be issues with the upcoming payment.
    
    Args:
        event_data: The Stripe event data
        
    Returns:
        Dict with processing status
    """
    invoice = event_data.get("data", {}).get("object", {})
    
    if not invoice:
        return {"success": False, "error": "Invoice data not found in event"}
    
    invoice_id = invoice.get("id")
    subscription_id = invoice.get("subscription")
    customer_id = invoice.get("customer")
    amount_due = invoice.get("amount_due", 0) / 100  # Convert cents to dollars
    
    # Check if the customer has a default payment method
    has_payment_method = False
    try:
        customer = stripe.Customer.retrieve(
            customer_id,
            expand=["invoice_settings.default_payment_method"]
        )
        has_payment_method = bool(customer.get("invoice_settings", {}).get("default_payment_method"))
    except Exception as e:
        logger.error(f"Error checking default payment method: {str(e)}")
    
    logger.info(f"Upcoming invoice {invoice_id} for ${amount_due}, has default payment method: {has_payment_method}")
    
    if subscription_id:
        # Find the associated workspaces
        try:
            # Find workspaces associated with the subscription
            db = await get_db_client()
            subscription_result = db.table("workspace_subscriptions") \
                .select("workspace_id") \
                .eq("stripe_subscription_id", subscription_id) \
                .execute()
            
            if subscription_result.data:
                workspace_ids = list(set([sub["workspace_id"] for sub in subscription_result.data if sub.get("workspace_id")]))
                
                # If there's no valid payment method, we could send an early warning
                if not has_payment_method and amount_due > 0:
                    logger.warning(f"Upcoming invoice {invoice_id} for subscription {subscription_id} has no payment method!")
                    # In the future, send a notification to the user
                    
                    # Find billing admin for each workspace
                    for workspace_id in workspace_ids:
                        workspace_result = db.table("workspaces") \
                            .select("billing_admin_id") \
                            .eq("id", workspace_id) \
                            .execute()
                        
                        if workspace_result.data and workspace_result.data[0].get("billing_admin_id"):
                            admin_id = workspace_result.data[0].get("billing_admin_id")
                            logger.info(f"Should notify billing admin {admin_id} about upcoming invoice for workspace {workspace_id}")
                            # TODO: Implement notification system
        except Exception as e:
            logger.error(f"Error processing upcoming invoice for subscription {subscription_id}: {str(e)}")
    
    return {
        "success": True,
        "status": "logged",
        "invoice_id": invoice_id,
        "subscription_id": subscription_id,
        "customer_id": customer_id,
        "has_payment_method": has_payment_method,
        "amount_due": amount_due
    }

# Map of event types to handler functions
EVENT_HANDLERS = {
    "checkout.session.completed": handle_checkout_completed,
    "customer.subscription.created": handle_subscription_created,
    "customer.subscription.updated": handle_subscription_updated,
    "customer.subscription.deleted": handle_subscription_deleted,
    "customer.subscription.paused": handle_subscription_paused,
    "customer.subscription.resumed": handle_subscription_resumed,
    "customer.subscription.trial_will_end": handle_subscription_trial_will_end,
    "payment_intent.succeeded": handle_payment_succeeded,
    "invoice.paid": handle_payment_succeeded,
    "invoice.payment_succeeded": handle_invoice_payment_succeeded,
    "payment_intent.created": handle_payment_intent_created,
    "payment_intent.payment_failed": handle_payment_intent_failed,
    "invoice.payment_failed": handle_invoice_payment_failed,
    "invoice.payment_action_required": handle_payment_action_required,
    "payment_intent.requires_action": handle_payment_action_required,
    "invoice.upcoming": handle_invoice_upcoming,
}

IGNORED_EVENTS = [
    # Payment notification events that don't require special handling
    "charge.succeeded",      # Individual charge succeeded, but we handle the invoice/subscription level
    
    # Customer management events
    "customer.updated",      # General updates to customer data
    
    # Invoice lifecycle events that don't require immediate action
    "invoice.created",       # Initial invoice creation - we wait for payment
    "invoice.finalized",     # Invoice finalized but not yet paid
    "invoice.updated",       # General invoice updates
    
    # Line-item level events
    "invoiceitem.created",   # Individual invoice items
    
    # Payment method events
    "mandate.created",       # Mandate for recurring payments created
    "mandate.updated",       # Mandate updated
    "payment_method.attached", # User attached a payment method
]