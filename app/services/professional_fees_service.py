"""
Professional Fees Analysis Service - LLM-based analysis for location and year-specific professional service rates.

This service uses AI analysis combined with Tavily search to determine realistic
professional service rates based on location, property type, and current market conditions.
"""

from dataclasses import dataclass
from pydantic import BaseModel, Field
from typing import Dict, List, Optional, Any

from pydantic_ai import Agent
from pydantic_ai.models.openai import OpenAIModel
from pydantic_ai.providers.openai import OpenAIProvider

from tavily import TavilyClient

from app.utils.logger import get_logger
from app.utils.env_cache import get_env
from app.models.ai import FinancialResponse, Source

logger = get_logger(__name__)

@dataclass
class ProfessionalFeesContext:
    """Context for professional fees analysis."""
    prop_id: str
    portfolio_id: str
    zipcode: Optional[str] = None
    city: Optional[str] = None
    state: Optional[str] = None
    rental_income: float = 0.0
    property_type: Optional[str] = None
    current_year: int = 2024

class FeeAnalysis(BaseModel):
    """Model for individual fee analysis results."""
    fee_type: str = Field(..., description="Type of professional fee (management, leasing, legal, etc.)")
    rate_percentage: float = Field(..., description="Rate as a percentage of rental income (e.g., 8.5 for 8.5%)")
    annual_amount: float = Field(..., description="Annual fee amount in USD")
    description: str = Field(..., description="Description of the fee")
    category: str = Field(..., description="Category of the fee (Management, Legal, Professional Services, etc.)")
    source_reasoning: str = Field(..., description="Explanation of how this rate was determined")

class ProfessionalFeesAnalysis(BaseModel):
    """Complete professional fees analysis results."""
    fees: List[FeeAnalysis] = Field(..., description="List of all professional fees")
    total_annual_fees: float = Field(..., description="Total annual professional fees")
    location_summary: str = Field(..., description="Summary of location-specific factors affecting rates")
    market_conditions: str = Field(..., description="Current market conditions affecting professional service rates")

class ProfessionalFeesService:
    """Service for analyzing professional fees using AI and market research."""
    
    def __init__(self, model_name: str = 'gpt-4o-mini'):
        """Initialize the professional fees service."""
        self.model = OpenAIModel(model_name, provider=OpenAIProvider(api_key=get_env('OPENAI_API_KEY')))
        self.tavily_client = TavilyClient(api_key=get_env('TAVILY_API_KEY'))
        
        # Initialize AI agent for professional fees analysis
        self.analysis_agent = Agent(
            self.model,
            deps_type=ProfessionalFeesContext,
            output_type=ProfessionalFeesAnalysis,
            system_prompt="""
            You are a real estate financial analyst specializing in professional service rates and fees.
            
            Your task is to analyze professional service rates for rental properties based on:
            1. Location-specific market conditions
            2. Current year market rates
            3. Property type and rental income level
            4. Local competition and service availability
            
            IMPORTANT: Use EXACTLY these fee_type values (no variations):
            - management_fees: Day-to-day operations, tenant relations, maintenance coordination (typically 6-10% of rental income)
            - leasing_fees: Finding and placing tenants, lease preparation, screening (typically 1-3% annually, amortized)
            - legal_fees: Contracts, evictions, compliance, legal advice (typically 0.3-1% of rental income)
            - accounting_fees: Financial records, tax preparation, reporting (typically 0.5-1.5% of rental income)
            - engineering_fees: Property assessments, inspections, technical evaluations (typically 0.2-0.5% of rental income)
            - marketing_fees: Property promotion, listing management, advertising (typically 0.3-0.8% of rental income)
            - consulting_fees: Real estate investment advice, strategic planning (typically 0.1-0.5% of rental income)
            
            RATE GUIDELINES (be conservative):
            - Total professional fees should generally be 8-15% of rental income maximum
            - Management fees are the largest component (6-10%)
            - Other fees should be modest and realistic
            - Consider that many fees are occasional, not constant annual expenses
            
            Consider these factors in your analysis:
            - Market rates vary significantly by location (urban vs rural, high vs low cost areas)
            - Larger rental portfolios often command lower percentage rates
            - Premium markets may have higher base rates but more competition
            - Economic conditions affect service pricing
            - Property type influences complexity and therefore rates
            
            Provide realistic, conservative market-based rates with clear reasoning for each fee type.
            Base your analysis on current market conditions and location-specific factors.
            
            CRITICAL: The fee_type field must be exactly one of the standard values listed above.
            """
        )
    
    async def get_local_market_data(self, context: ProfessionalFeesContext) -> Dict[str, Any]:
        """Get local market data for professional service rates."""
        try:
            # Build location string for search
            location_parts = []
            if context.city:
                location_parts.append(context.city)
            if context.state:
                location_parts.append(context.state)
            elif context.zipcode:
                location_parts.append(context.zipcode)
            
            location_str = ", ".join(location_parts) if location_parts else "United States"
            
            # Search for current professional service rates
            search_queries = [
                f"property management fees rates {location_str} {context.current_year}",
                f"real estate professional services costs {location_str} {context.current_year}",
                f"rental property leasing fees {location_str} {context.current_year}"
            ]
            
            market_data = {
                "sources": [],
                "findings": [],
                "location": location_str
            }
            
            for query in search_queries:
                try:
                    logger.info(f"Searching for professional fees data: {query}")
                    search_results = self.tavily_client.search(
                        query=query,
                        search_depth="basic",
                        max_results=3
                    )
                    
                    if search_results and 'results' in search_results:
                        for result in search_results['results']:
                            market_data["sources"].append(result.get('url', ''))
                            market_data["findings"].append({
                                "title": result.get('title', ''),
                                "content": result.get('content', ''),
                                "url": result.get('url', '')
                            })
                    
                except Exception as e:
                    logger.warning(f"Error in Tavily search for '{query}': {str(e)}")
                    continue
            
            logger.info(f"Gathered {len(market_data['findings'])} market data points for professional fees")
            return market_data
            
        except Exception as e:
            logger.error(f"Error getting local market data: {str(e)}")
            return {"sources": [], "findings": [], "location": "Unknown"}
    
    async def analyze_professional_fees(self, context: ProfessionalFeesContext) -> ProfessionalFeesAnalysis:
        """Analyze professional fees for a property using AI and market data."""
        try:
            # Get local market data
            market_data = await self.get_local_market_data(context)
            
            # Enhance context with market data
            analysis_context = f"""
            Property Information:
            - Location: {context.city}, {context.state} (ZIP: {context.zipcode})
            - Annual Rental Income: ${context.rental_income:,.2f}
            - Property Type: {context.property_type or 'Not specified'}
            - Analysis Year: {context.current_year}
            
            Local Market Research Findings:
            """
            
            for i, finding in enumerate(market_data["findings"][:5], 1):  # Limit to top 5 findings
                analysis_context += f"""
                
                Finding {i}:
                Title: {finding['title']}
                Content: {finding['content'][:500]}...
                Source: {finding['url']}
                """
            
            if not market_data["findings"]:
                analysis_context += "\nNo specific local market data found. Use general market knowledge and industry standards."
            
            # Run AI analysis
            result = await self.analysis_agent.run(
                analysis_context,
                deps=context
            )
            
            logger.info(f"Professional fees analysis completed: {len(result.data.fees)} fee types analyzed")
            return result.data
            
        except Exception as e:
            logger.error(f"Error analyzing professional fees: {str(e)}")
            # Return fallback analysis
            return self._get_fallback_analysis(context)
    
    def _get_fallback_analysis(self, context: ProfessionalFeesContext) -> ProfessionalFeesAnalysis:
        """Provide fallback analysis when AI analysis fails."""
        logger.warning("Using fallback professional fees analysis")
        
        # Conservative market-based rates - reduced to be more reasonable
        fallback_fees = [
            FeeAnalysis(
                fee_type="management_fees",
                rate_percentage=6.0,  # Reduced from 8% to 6%
                annual_amount=context.rental_income * 0.06,
                description="Property management fees for day-to-day operations",
                category="Management",
                source_reasoning="Industry standard range 6-12%, using conservative 6% estimate"
            ),
            FeeAnalysis(
                fee_type="leasing_fees",
                rate_percentage=2.0,  # Reduced from 5% to 2% (amortized annual cost)
                annual_amount=context.rental_income * 0.02,
                description="Tenant placement and leasing fees (amortized annually)",
                category="Leasing",
                source_reasoning="Typical leasing fees 50-100% of monthly rent, amortized to ~2% annually"
            ),
            FeeAnalysis(
                fee_type="legal_fees",
                rate_percentage=0.5,  # Reduced from 1% to 0.5%
                annual_amount=context.rental_income * 0.005,
                description="Legal fees for contracts, evictions, and compliance",
                category="Legal",
                source_reasoning="Conservative estimate for legal services and compliance"
            ),
            FeeAnalysis(
                fee_type="accounting_fees",
                rate_percentage=1.0,  # Reduced from 0.5% to 1% but this is reasonable
                annual_amount=context.rental_income * 0.01,
                description="Accounting and bookkeeping services",
                category="Professional Services",
                source_reasoning="Basic bookkeeping and tax preparation services"
            ),
            FeeAnalysis(
                fee_type="engineering_fees",
                rate_percentage=0.3,  # Reduced from 1% to 0.3%
                annual_amount=context.rental_income * 0.003,
                description="Property assessments and inspections",
                category="Professional Services",
                source_reasoning="Annual inspections and occasional assessments"
            ),
            FeeAnalysis(
                fee_type="marketing_fees",
                rate_percentage=0.5,  # Reduced from 3% to 0.5%
                annual_amount=context.rental_income * 0.005,
                description="Property promotion and advertising",
                category="Marketing",
                source_reasoning="Modest marketing and advertising costs"
            ),
            FeeAnalysis(
                fee_type="consulting_fees",
                rate_percentage=0.2,  # Reduced from 1.5% to 0.2%
                annual_amount=context.rental_income * 0.002,
                description="Real estate investment advice",
                category="Consulting",
                source_reasoning="Occasional consulting and advisory services"
            )
        ]
        
        total_fees = sum(fee.annual_amount for fee in fallback_fees)
        
        return ProfessionalFeesAnalysis(
            fees=fallback_fees,
            total_annual_fees=total_fees,
            location_summary=f"Fallback analysis for {context.city or 'Unknown'}, {context.state or 'US'}",
            market_conditions="Unable to access current market data, using conservative estimates"
        )
    
    async def calculate_professional_fees_projections(
        self, 
        context: ProfessionalFeesContext
    ) -> Dict[str, Dict[str, FinancialResponse]]:
        """Calculate professional fees projections for years 1-5."""
        try:
            # Get AI-based analysis
            analysis = await self.analyze_professional_fees(context)
            
            # Group fees by type for projection calculations
            all_fees = {}
            
            for fee_analysis in analysis.fees:
                fee_projections = {}
                
                # Calculate projections for years 1-5
                for year in range(1, 6):
                    # Project rental income growth (modest 2% annually)
                    projected_rental = context.rental_income * ((1.02) ** (year - 1))
                    annual_fee = projected_rental * (fee_analysis.rate_percentage / 100)
                    
                    projection = FinancialResponse(
                        task_type=fee_analysis.fee_type,
                        portfolio_id=context.portfolio_id,
                        prop_id=context.prop_id,
                        year=year,
                        sources=[
                            Source(
                                source="ai_market_analysis",
                                description=f"AI analysis: {fee_analysis.source_reasoning}",
                                url="ai_analysis_with_market_research"
                            )
                        ],
                        value=fee_analysis.rate_percentage,
                        unit="%",
                        money_value=-annual_fee,  # Make negative since this is an expense
                        money_unit="USD",
                        description=f"{fee_analysis.description} - {fee_analysis.category} (Year {year})",
                        additional_data=None
                    )
                    
                    fee_projections[f"year_{year}"] = projection
                
                all_fees[fee_analysis.fee_type] = fee_projections
                logger.info(f"{fee_analysis.fee_type}: {fee_analysis.rate_percentage:.1f}% = ${fee_analysis.annual_amount:.2f} annually")
            
            return all_fees
            
        except Exception as e:
            logger.error(f"Error calculating professional fees projections: {str(e)}")
            return {} 