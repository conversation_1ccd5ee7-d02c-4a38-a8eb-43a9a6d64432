"""
Rental Income Service - Service for calculating rental income for property units.

This service implements a waterfall approach to estimate rental income:
1. Get known rents from PropertyUnit table
2. For missing rents, estimate using sqft and rent/sqft from known units
3. If no known units, use RELM DB rental listings for market data
4. If no RELM data, fall back to Realtor API search
"""
import statistics
from uuid import UUID
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional

from app.utils.logger import get_logger
from app.repository.db import get_db_client
from app.models.database import PropertyUnit
from app.models.relm_models import RentalListing
from app.repository.relm_db import RelmDbService
from app.services.property_service import PropertyService
from app.datasources.realtor import RealtorDataService, ListingType

logger = get_logger(__name__)

class RentalIncomeCalculationResult:
    """Result class for rental income calculations."""
    
    def __init__(self):
        self.units_with_rent: List[PropertyUnit] = []
        self.units_missing_rent: List[PropertyUnit] = []
        self.estimated_rents: Dict[str, float] = {}  # unit_id -> estimated rent
        self.rent_per_sqft: Optional[float] = None
        self.total_rental_income: float = 0.0
        self.sources_used: List[str] = []
        self.market_data_used: List[Dict[str, Any]] = []

class RentalIncomeService:
    """Service for calculating rental income using waterfall approach."""
    
    @classmethod
    async def calculate_rental_income(
        cls, 
        prop_id: str
    ) -> RentalIncomeCalculationResult:
        """
        Calculate total rental income for a property using waterfall approach.
        
        Args:
            prop_id: Property ID
            
        Returns:
            RentalIncomeCalculationResult with calculated rents and details
        """
        result = RentalIncomeCalculationResult()
        
        try:
            # Step 1: Get all units and their current rent data
            units = await PropertyService.get_property_units(prop_id)
            if not units:
                logger.warning(f"No units found for property {prop_id}")
                return result
            
            # Separate units with and without rent
            for unit in units:
                if unit.rent and unit.rent > 0:
                    result.units_with_rent.append(unit)
                else:
                    result.units_missing_rent.append(unit)
            
            logger.info(f"Property {prop_id}: {len(result.units_with_rent)} units with rent, "
                       f"{len(result.units_missing_rent)} units missing rent")
            
            # Step 2: If we have units with rent, calculate average rent per sqft
            if result.units_with_rent:
                result.rent_per_sqft = cls._calculate_rent_per_sqft(result.units_with_rent)
                if result.rent_per_sqft:
                    result.sources_used.append("existing_property_units_sqft")
                    
                    # Estimate rent for missing units using property's own data
                    for unit in result.units_missing_rent:
                        if unit.sqft and unit.sqft > 0:
                            estimated_rent = unit.sqft * result.rent_per_sqft
                            result.estimated_rents[str(unit.id)] = estimated_rent
                            logger.info(f"Estimated rent for unit {unit.unit}: ${estimated_rent:.2f} "
                                       f"({unit.sqft} sqft * ${result.rent_per_sqft:.2f}/sqft)")
                            
                            # Update the database with estimated rent
                            if unit.id:
                                await cls._update_unit_rent(unit.id, estimated_rent)
                else:
                    # Fallback: Use median rent when sqft data is not available
                    logger.info("Sqft data not available, using median rent estimation")
                    median_rent = cls._estimate_rent_by_median(result.units_with_rent)
                    if median_rent:
                        result.sources_used.append("existing_property_units_median")
                        
                        # Apply median rent to all missing units
                        for unit in result.units_missing_rent:
                            estimated_rent = median_rent
                            result.estimated_rents[str(unit.id)] = estimated_rent
                            logger.info(f"Estimated rent for unit {unit.unit}: ${estimated_rent:.2f} "
                                       f"(using median of existing units)")
                            
                            # Update the database with estimated rent
                            if unit.id:
                                await cls._update_unit_rent(unit.id, estimated_rent)
            
            # Step 3: For units still missing rent (no sqft or no existing rent data), try RELM DB
            remaining_units = [
                unit for unit in result.units_missing_rent 
                if str(unit.id) not in result.estimated_rents
            ]
            
            if remaining_units:
                await cls._estimate_from_relm_db(prop_id, remaining_units, result)
            
            # Step 4: For units still missing rent, try Realtor API
            final_remaining_units = [
                unit for unit in result.units_missing_rent 
                if str(unit.id) not in result.estimated_rents
            ]
            if final_remaining_units:
                await cls._estimate_from_realtor_api(prop_id, final_remaining_units, result)
            
            # Calculate total rental income
            result.total_rental_income = cls._calculate_total_income(result)
            
            logger.info(f"Total estimated rental income for property {prop_id}: "
                       f"${result.total_rental_income:.2f}/month")
            
            return result
            
        except Exception as e:
            logger.error(f"Error calculating rental income for property {prop_id}: {str(e)}")
            return result
    
    @classmethod
    async def _update_unit_rent(cls, unit_id: UUID, estimated_rent: float) -> None:
        """Update the PropertyUnit table with estimated rent."""
        try:
            client = await get_db_client()
            response = client.table("prop_units").update({
                "rent": estimated_rent
            }).eq("id", str(unit_id)).execute()
            
            if response.data:
                logger.info(f"Updated unit {unit_id} with estimated rent ${estimated_rent:.2f}")
            else:
                logger.warning(f"Failed to update unit {unit_id} with estimated rent")
                
        except Exception as e:
            logger.error(f"Error updating unit rent: {str(e)}")
    
    @classmethod
    def _calculate_rent_per_sqft(cls, units_with_rent: List[PropertyUnit]) -> Optional[float]:
        """Calculate average rent per square foot from units with known rent."""
        rent_per_sqft_values = []
        
        for unit in units_with_rent:
            if unit.rent and unit.sqft and unit.sqft > 0:
                rent_per_sqft = unit.rent / unit.sqft
                rent_per_sqft_values.append(rent_per_sqft)
                logger.info(f"Unit {unit.unit}: ${unit.rent}/month ÷ {unit.sqft} sqft = ${rent_per_sqft:.2f}/sqft")
        
        if rent_per_sqft_values:
            # Use median to avoid outliers
            avg_rent_per_sqft = statistics.median(rent_per_sqft_values)
            logger.info(f"Calculated average rent per sqft: ${avg_rent_per_sqft:.2f} "
                       f"from {len(rent_per_sqft_values)} units")
            return avg_rent_per_sqft
        else:
            logger.warning("No units have sqft data - cannot calculate rent per sqft")
        
        return None
    
    @classmethod
    def _estimate_rent_by_median(cls, units_with_rent: List[PropertyUnit]) -> Optional[float]:
        """Estimate rent using median of existing unit rents when sqft data is unavailable."""
        if not units_with_rent:
            return None
            
        rent_values = [unit.rent for unit in units_with_rent if unit.rent and unit.rent > 0]
        
        if rent_values:
            median_rent = statistics.median(rent_values)
            logger.info(f"Calculated median rent: ${median_rent:.2f} from {len(rent_values)} units")
            logger.info(f"Individual rents: {[f'${rent:.2f}' for rent in sorted(rent_values)]}")
            return median_rent
            
        return None
    
    @classmethod
    async def _estimate_from_relm_db(
        cls, 
        prop_id: str, 
        remaining_units: List[PropertyUnit],
        result: RentalIncomeCalculationResult
    ) -> None:
        """Estimate rents using RELM database rental listings."""
        try:
            # Get property address for location filtering
            property = await PropertyService.get_property(prop_id)
            if not property:
                logger.warning(f"Property not found: {prop_id}")
                return
                
            address = await PropertyService.get_property_address(str(property.address_id))
            if not address:
                logger.warning(f"Address not found for property: {prop_id}")
                return
            
            logger.info(f"Searching RELM DB for rental comps near {address.zip}")
            
            # Get recent rental listings from RELM DB
            rental_listings = await RelmDbService.get_recent_rental_listings(
                postal_code=address.zip,
                limit=50,
                prefer_active=True
            )
            
            if rental_listings:
                result.sources_used.append("relm_database")
                result.market_data_used.extend([listing.to_dict() for listing in rental_listings])
                
                # Calculate rent per sqft from RELM data
                relm_rent_per_sqft = cls._calculate_relm_rent_per_sqft(rental_listings)
                if relm_rent_per_sqft and not result.rent_per_sqft:
                    result.rent_per_sqft = relm_rent_per_sqft
                
                # Estimate rents for units missing rent
                for unit in remaining_units:
                    if str(unit.id) not in result.estimated_rents:
                        if unit.sqft and unit.sqft > 0 and result.rent_per_sqft:
                            estimated_rent = unit.sqft * result.rent_per_sqft
                            result.estimated_rents[str(unit.id)] = estimated_rent
                            logger.info(f"Estimated rent from RELM DB for unit {unit.unit}: "
                                       f"${estimated_rent:.2f} ({unit.sqft} sqft * ${result.rent_per_sqft:.2f}/sqft)")
                            
                            # Update the database with estimated rent
                            if unit.id:
                                await cls._update_unit_rent(unit.id, estimated_rent)
                        else:
                            # Fallback: use median of RELM listings if no sqft data
                            if rental_listings:
                                rent_values = [listing.list_price for listing in rental_listings 
                                             if listing.list_price and listing.list_price > 0]
                                if rent_values:
                                    median_market_rent = statistics.median(rent_values)
                                    result.estimated_rents[str(unit.id)] = median_market_rent
                                    logger.info(f"Estimated rent from RELM DB median for unit {unit.unit}: "
                                               f"${median_market_rent:.2f} (no sqft data available)")
                                    
                                    # Update the database with estimated rent
                                    if unit.id:
                                        await cls._update_unit_rent(unit.id, median_market_rent)
                
                logger.info(f"Found {len(rental_listings)} rental listings in RELM DB")
            else:
                logger.info("No rental listings found in RELM DB")
                
        except Exception as e:
            logger.error(f"Error estimating from RELM DB: {str(e)}")
    
    @classmethod
    def _calculate_relm_rent_per_sqft(cls, listings: List[RentalListing]) -> Optional[float]:
        """Calculate average rent per sqft from RELM rental listings."""
        rent_per_sqft_values = []
        
        for listing in listings:
            if listing.list_price and listing.sqft and listing.sqft > 0:
                rent_per_sqft = listing.list_price / listing.sqft
                rent_per_sqft_values.append(rent_per_sqft)
        
        if rent_per_sqft_values:
            avg_rent_per_sqft = statistics.median(rent_per_sqft_values)
            logger.info(f"Calculated RELM DB rent per sqft: ${avg_rent_per_sqft:.2f} "
                       f"from {len(rent_per_sqft_values)} listings")
            return avg_rent_per_sqft
        
        return None
    
    @classmethod
    async def _estimate_from_realtor_api(
        cls,
        prop_id: str,
        remaining_units: List[PropertyUnit],
        result: RentalIncomeCalculationResult
    ) -> None:
        """Estimate rents using Realtor API as fallback."""
        try:
            # Get property address
            property = await PropertyService.get_property(prop_id)
            if not property:
                return
                
            address = await PropertyService.get_property_address(str(property.address_id))
            if not address:
                return
                
            logger.info(f"Searching Realtor API for rental comps in {address.zip}")
            
            # Search for recent rental listings
            end_date = datetime.now().strftime("%Y-%m-%d")
            start_date = (datetime.now() - timedelta(days=365)).strftime("%Y-%m-%d")
            
            search_results = await RealtorDataService.general_search(
                start_date=start_date,
                end_date=end_date,
                listing_type=ListingType.FOR_RENT,
                postal_code=address.zip
            )
            
            if search_results and search_results.get("properties"):
                result.sources_used.append("realtor_api")
                
                # Extract rent per sqft from API results
                api_rent_per_sqft = cls._calculate_api_rent_per_sqft(search_results["properties"])
                if api_rent_per_sqft and not result.rent_per_sqft:
                    result.rent_per_sqft = api_rent_per_sqft
                
                # Estimate rents for remaining units
                for unit in remaining_units:
                    if str(unit.id) not in result.estimated_rents:
                        if unit.sqft and unit.sqft > 0 and result.rent_per_sqft:
                            estimated_rent = unit.sqft * result.rent_per_sqft
                            result.estimated_rents[str(unit.id)] = estimated_rent
                            logger.info(f"Estimated rent from Realtor API for unit {unit.unit}: "
                                       f"${estimated_rent:.2f} ({unit.sqft} sqft * ${result.rent_per_sqft:.2f}/sqft)")
                            
                            # Update the database with estimated rent
                            if unit.id:
                                await cls._update_unit_rent(unit.id, estimated_rent)
                        else:
                            # Fallback: use median of Realtor listings if no sqft data
                            rent_values = [prop.get("list_price") for prop in search_results["properties"] 
                                         if prop.get("list_price") and prop.get("list_price") > 0]
                            if rent_values:
                                median_market_rent = statistics.median(rent_values)
                                result.estimated_rents[str(unit.id)] = median_market_rent
                                logger.info(f"Estimated rent from Realtor API median for unit {unit.unit}: "
                                           f"${median_market_rent:.2f} (no sqft data available)")
                                
                                # Update the database with estimated rent
                                if unit.id:
                                    await cls._update_unit_rent(unit.id, median_market_rent)
                
                logger.info(f"Calculated Realtor API rent per sqft: ${api_rent_per_sqft:.2f} "
                           f"from {len(search_results['properties'])} listings")
            else:
                logger.info("No rental listings found from Realtor API")
                
        except Exception as e:
            logger.error(f"Error estimating from Realtor API: {str(e)}")
    
    @classmethod
    def _calculate_api_rent_per_sqft(cls, properties: List[Dict[str, Any]]) -> Optional[float]:
        """Calculate average rent per sqft from Realtor API results."""
        rent_per_sqft_values = []
        
        for prop in properties:
            list_price = prop.get("list_price")
            description = prop.get("description", {})
            sqft = description.get("sqft") if description else None
            
            if list_price and sqft and sqft > 0:
                rent_per_sqft = list_price / sqft
                rent_per_sqft_values.append(rent_per_sqft)
        
        if rent_per_sqft_values:
            avg_rent_per_sqft = statistics.median(rent_per_sqft_values)
            return avg_rent_per_sqft
        
        return None
    
    @classmethod
    def _calculate_total_income(cls, result: RentalIncomeCalculationResult) -> float:
        """Calculate total monthly rental income."""
        total = 0.0
        
        # Add known rents
        for unit in result.units_with_rent:
            if unit.rent:
                total += unit.rent
                logger.info(f"Known rent - Unit {unit.unit}: ${unit.rent:.2f}")
        
        # Add estimated rents
        for unit_id, estimated_rent in result.estimated_rents.items():
            total += estimated_rent
            logger.info(f"Estimated rent - Unit ID {unit_id}: ${estimated_rent:.2f}")
        
        return total
    
    @classmethod
    async def get_rental_income_breakdown(
        cls, 
        prop_id: str
    ) -> Dict[str, Any]:
        """
        Get detailed breakdown of rental income calculation.
        
        Args:
            prop_id: Property ID
            
        Returns:
            Detailed breakdown dictionary
        """
        result = await cls.calculate_rental_income(prop_id)
        
        breakdown = {
            "property_id": prop_id,
            "total_monthly_income": result.total_rental_income,
            "total_annual_income": result.total_rental_income * 12,
            "units_with_known_rent": len(result.units_with_rent),
            "units_with_estimated_rent": len(result.estimated_rents),
            "average_rent_per_sqft": result.rent_per_sqft,
            "sources_used": result.sources_used,
            "unit_details": [],
            "market_data_count": len(result.market_data_used)
        }
        
        # Add unit details
        for unit in result.units_with_rent:
            breakdown["unit_details"].append({
                "unit_id": str(unit.id),
                "unit_name": unit.unit,
                "rent": unit.rent,
                "sqft": unit.sqft,
                "status": "known_rent"
            })
        
        for unit in result.units_missing_rent:
            unit_id = str(unit.id)
            estimated_rent = result.estimated_rents.get(unit_id)
            breakdown["unit_details"].append({
                "unit_id": unit_id,
                "unit_name": unit.unit,
                "rent": estimated_rent,
                "sqft": unit.sqft,
                "status": "estimated_rent" if estimated_rent else "no_estimate"
            })
        
        return breakdown 