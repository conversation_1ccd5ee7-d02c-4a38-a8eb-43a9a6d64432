"""
Expense Persistence Service - Save calculated expense data to database for DataFrame consumption.

This service bridges the gap between expense calculations (which return FinancialResponse objects)
and the DataFrame system (which reads from database fields). It ensures that calculated expenses
are properly saved to the database fields that the financial DataFrame expects.

Key Functions:
- Maps FinancialResponse objects to database field names
- Saves individual expense components to their respective database fields
- Handles both individual year data and projection data
- Maintains data integrity and audit trails

Sources and References:
- IREM Chart of Accounts for field naming standards
- GAAP accounting principles for expense categorization
- Real estate financial reporting standards (NAREIT)
"""
from typing import Dict, List, Optional, Any

from app.utils.logger import get_logger
from app.models.ai import FinancialResponse
from app.services.financials_service import PropertFinancialService

logger = get_logger(__name__)

class ExpensePersistenceService:
    """Service for saving calculated expense data to database fields."""
    
    # Mapping from expense calculation descriptions to database field names
    # Source: IREM Chart of Accounts and real estate financial reporting standards
    EXPENSE_FIELD_MAPPING = {
        # Property tax variations
        'property_tax': 'property_tax',
        'property tax': 'property_tax',
        'real estate tax': 'property_tax',
        'tax': 'property_tax',
        
        # Insurance variations  
        'insurance': 'insurance',
        'property insurance': 'insurance',
        'liability insurance': 'insurance',
        
        # Professional fees - individual components
        'management_fees': 'management_fees',
        'property management': 'management_fees', 
        'management': 'management_fees',
        
        'leasing_fees': 'leasing_fees',
        'leasing': 'leasing_fees',
        'tenant placement': 'leasing_fees',
        
        'legal_fees': 'legal_fees', 
        'legal': 'legal_fees',
        'legal services': 'legal_fees',
        
        'accounting_fees': 'accounting_fees',
        'accounting': 'accounting_fees',
        'bookkeeping': 'accounting_fees',
        
        'engineering_fees': 'engineering_fees',
        'engineering': 'engineering_fees',
        'inspection': 'engineering_fees',
        
        'marketing_fees': 'marketing_fees',
        'marketing': 'marketing_fees',
        'advertising': 'marketing_fees',
        
        'consulting_fees': 'consulting_fees',
        'consulting': 'consulting_fees',
        'advisory': 'consulting_fees',
        
        # Repairs and maintenance
        'repairs': 'repairs',
        'repair': 'repairs',
        'maintenance': 'maintenance',
        'repairs and maintenance': 'repairs',
        
        # Utilities
        'utilities': 'utilities',
        'utility': 'utilities',
        'water': 'utilities',
        'sewer': 'utilities',
        'electric': 'utilities',
        'gas': 'utilities',
        
        # Other expenses
        'services': 'services',
        'landscaping': 'services',
        'cleaning': 'services',
        'security': 'services',
        
        'reserves': 'reserves',
        'capital reserves': 'reserves',
        'replacement reserves': 'reserves',
    }
    
    @classmethod
    def identify_expense_field(cls, financial_response: FinancialResponse) -> Optional[str]:
        """
        Identify the database field name for a FinancialResponse object.
        
        Uses description text analysis and source information to map to proper database fields.
        
        Args:
            financial_response: The FinancialResponse object to analyze
            
        Returns:
            Database field name or None if not identifiable
        """
        try:
            # Check description for keywords
            description = financial_response.description.lower() if financial_response.description else ""
            
            # Direct keyword matching
            for keyword, field_name in cls.EXPENSE_FIELD_MAPPING.items():
                if keyword in description:
                    return field_name
            
            # Check sources for more specific identification
            if financial_response.sources:
                for source in financial_response.sources:
                    source_name = source.source.lower() if source.source else ""
                    
                    # Property tax sources
                    if any(tax_keyword in source_name for tax_keyword in ['tax', 'property_tax']):
                        return 'property_tax'
                    
                    # Insurance sources  
                    if any(ins_keyword in source_name for ins_keyword in ['insurance', 'insurance_rate']):
                        return 'insurance'
                    
                    # Professional fees AI analysis
                    if 'ai_market_analysis' in source_name:
                        # Analyze description for professional fee type
                        if 'management' in description:
                            return 'management_fees'
                        elif any(lease_word in description for lease_word in ['leasing', 'tenant placement', 'placement']):
                            return 'leasing_fees'
                        elif 'legal' in description:
                            return 'legal_fees'
                        elif any(acc_word in description for acc_word in ['accounting', 'bookkeeping', 'financial records']):
                            return 'accounting_fees'
                        elif any(eng_word in description for eng_word in ['engineering', 'inspection', 'assessment']):
                            return 'engineering_fees'
                        elif any(mkt_word in description for mkt_word in ['marketing', 'advertising', 'promotion']):
                            return 'marketing_fees'
                        elif any(con_word in description for con_word in ['consulting', 'advisory', 'investment advice']):
                            return 'consulting_fees'
                    
                    # Repairs analysis
                    if 'condition_analysis' in source_name or 'repair' in source_name:
                        return 'repairs'
            
            logger.warning(f"Could not identify field for expense: {description}")
            return None
            
        except Exception as e:
            logger.error(f"Error identifying expense field: {str(e)}")
            return None
    
    @classmethod
    async def save_expense_calculations(
        cls, 
        prop_id: str, 
        expense_responses: List[FinancialResponse],
        save_projections: bool = True
    ) -> Dict[str, Any]:
        """
        Save calculated expense data to database fields.
        
        Maps FinancialResponse objects to appropriate database fields and saves both
        year 1 data and projections (if available).
        
        Source: IREM financial reporting standards for expense categorization
        
        Args:
            prop_id: Property ID
            expense_responses: List of FinancialResponse objects from calculations
            save_projections: Whether to save projection data (years 2-5)
            
        Returns:
            Summary of saved data
        """
        try:
            saved_fields = {}
            projection_data = {}

            # Collect field names for batch attribution updates
            year_1_fields = []
            projection_fields_by_year = {2: [], 3: [], 4: [], 5: []}

            logger.info(f"Saving {len(expense_responses)} expense calculations for property {prop_id}")

            for response in expense_responses:
                # Identify the database field for this expense
                field_name = cls.identify_expense_field(response)
                
                if not field_name:
                    logger.warning(f"Skipping expense - could not identify field: {response.description}")
                    continue
                
                # Save Year 1 data
                year = response.year if hasattr(response, 'year') and response.year is not None else 1
                money_value = response.money_value if response.money_value is not None else 0.0
                
                # Save to database
                await PropertFinancialService.set_single_property_financial(
                    prop_id=prop_id,
                    year=year,
                    field_name=field_name,
                    value=money_value
                )

                # Collect field name for batch attribution update
                if year == 1:
                    year_1_fields.append(field_name)
                elif year in projection_fields_by_year:
                    projection_fields_by_year[year].append(field_name)
                
                saved_fields[field_name] = {
                    'year_1': money_value,
                    'description': response.description,
                    'sources': [s.source for s in response.sources] if response.sources else []
                }
                
                logger.info(f"Saved {field_name}: ${money_value:,.2f} for year {year}")
                
                # Save projection data if available and requested
                if save_projections and hasattr(response, 'additional_data') and response.additional_data:
                    for proj_year, proj_response in response.additional_data.items():
                        if proj_response and hasattr(proj_response, 'money_value'):
                            proj_year_num = int(proj_year.replace('year_', ''))
                            proj_value = proj_response.money_value if proj_response.money_value is not None else 0.0
                            
                            # Save projection year
                            await PropertFinancialService.set_single_property_financial(
                                prop_id=prop_id,
                                year=proj_year_num,
                                field_name=field_name,
                                value=proj_value
                            )

                            # Collect field name for batch attribution update
                            if proj_year_num in projection_fields_by_year:
                                projection_fields_by_year[proj_year_num].append(field_name)
                            
                            if field_name not in projection_data:
                                projection_data[field_name] = {}
                            projection_data[field_name][proj_year] = proj_value
                            
                            logger.info(f"Saved {field_name} projection: ${proj_value:,.2f} for year {proj_year_num}")
            
            # Calculate and save professional_fees total (sum of individual professional fee components)
            # Source: IREM standards for professional fees aggregation
            professional_fee_components = [
                'management_fees', 'leasing_fees', 'legal_fees', 'accounting_fees',
                'engineering_fees', 'marketing_fees', 'consulting_fees'
            ]
            
            for year in range(1, 6):
                prof_fee_total = 0.0
                for component in professional_fee_components:
                    if component in saved_fields:
                        if year == 1:
                            prof_fee_total += saved_fields[component]['year_1']
                        elif component in projection_data and f'year_{year}' in projection_data[component]:
                            prof_fee_total += projection_data[component][f'year_{year}']
                
                if prof_fee_total > 0:
                    await PropertFinancialService.set_single_property_financial(
                        prop_id=prop_id,
                        year=year,
                        field_name='professional_fees',
                        value=prof_fee_total
                    )

                    # Collect professional_fees for batch attribution update
                    if year == 1:
                        year_1_fields.append('professional_fees')
                    elif year in projection_fields_by_year:
                        projection_fields_by_year[year].append('professional_fees')
                    
                    logger.info(f"Saved professional_fees total: ${prof_fee_total:,.2f} for year {year}")

            # Batch update attribution metadata to avoid blocking the event loop
            if year_1_fields:
                await PropertFinancialService.batch_set_metadata_attribution(
                    prop_id=prop_id,
                    year=1,
                    field_names=year_1_fields
                )
                logger.info(f"Batch updated attribution for {len(year_1_fields)} year 1 fields")

            # Batch update attribution for projection years
            for year, field_names in projection_fields_by_year.items():
                if field_names:
                    await PropertFinancialService.batch_set_metadata_attribution(
                        prop_id=prop_id,
                        year=year,
                        field_names=field_names
                    )
                    logger.info(f"Batch updated attribution for {len(field_names)} fields in year {year}")

            # Clear cache to ensure fresh data is loaded
            from app.repository.cache import redis_cache
            cache_key = f"property_financials_df:{prop_id}"
            await redis_cache.delete(cache_key)
            logger.info("Cleared financial DataFrame cache for fresh data")
            
            return {
                'success': True,
                'saved_fields': saved_fields,
                'projection_data': projection_data,
                'total_fields_saved': len(saved_fields),
                'projections_saved': len(projection_data) if save_projections else 0
            }
            
        except Exception as e:
            logger.error(f"Error saving expense calculations: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'saved_fields': {},
                'projection_data': {}
            }
    
    @classmethod
    async def recalculate_and_save_all_expenses(
        cls, 
        prop_id: str, 
        portfolio_id: str,
        save_projections: bool = True
    ) -> Dict[str, Any]:
        """
        Recalculate all expenses and save to database.
        
        This is a comprehensive function that:
        1. Runs all expense calculations
        2. Maps results to database fields  
        3. Saves individual components
        4. Calculates aggregated totals
        5. Clears cache for fresh data
        
        Args:
            prop_id: Property ID
            portfolio_id: Portfolio ID  
            save_projections: Whether to save projection data
            
        Returns:
            Summary of all calculations and saves
        """
        try:
            logger.info(f"Recalculating and saving all expenses for property {prop_id}")
            
            # Import here to avoid circular imports
            from app.agents.financials import FinancialServiceAgent, FinancialTaskInput
            from app.services.property_service import PropertyService
            
            # Initialize financial service
            financial_service = FinancialServiceAgent()
            
            # Create task input for expense calculation
            task_input = FinancialTaskInput(
                task_type='overall_expense',
                portfolio_id=portfolio_id,
                prop_id=prop_id,
                year=1,
                db=PropertyService(),
                return_projections=save_projections
            )
            
            # Run expense calculations
            result = await financial_service.agent.run(
                "Calculate all property expenses for database storage",
                deps=task_input
            )
            
            if not result.output or not result.output.responses:
                logger.error("No expense calculations returned from agent")
                return {'success': False, 'error': 'No calculations returned'}
            
            # Save all calculated expenses
            save_result = await cls.save_expense_calculations(
                prop_id=prop_id,
                expense_responses=result.output.responses,
                save_projections=save_projections
            )
            
            logger.info(f"Expense recalculation and save completed: {save_result.get('total_fields_saved', 0)} fields saved")
            
            return save_result
            
        except Exception as e:
            logger.error(f"Error in recalculate_and_save_all_expenses: {str(e)}")
            return {'success': False, 'error': str(e)} 