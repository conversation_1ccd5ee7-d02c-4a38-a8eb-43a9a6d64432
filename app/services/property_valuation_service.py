"""
Property Valuation Service - Estimates property values using cap rates and financial data.

This service analyzes local cap rates and uses effective gross income to estimate
property values for financial calculations.
"""

import re
from typing import Dict, List, Optional, Any
from dataclasses import dataclass

from tavily import TavilyClient

from app.utils.logger import get_logger
from app.utils.env_cache import get_env

logger = get_logger(__name__)

@dataclass
class PropertyValuationContext:
    """Context for property valuation calculations."""
    prop_id: str
    portfolio_id: str
    zipcode: Optional[str] = None
    city: Optional[str] = None
    state: Optional[str] = None
    effective_gross_income: Optional[float] = None
    rental_income: Optional[float] = None

class PropertyValuationService:
    """Service for property valuation calculations."""
    
    def __init__(self):
        """Initialize the property valuation service."""
        self.tavily_client = TavilyClient(api_key=get_env('TAVILY_API_KEY'))
    
    async def get_cap_rates(self, zipcode: Optional[str], city: Optional[str], state: Optional[str]) -> Dict[str, Any]:
        """Get cap rates for a location using Tavily search."""
        try:
            # Create search query
            location = ""
            if zipcode:
                location = f"ZIP code {zipcode}"
            elif city and state:
                location = f"{city}, {state}"
            else:
                logger.warning("No location information provided for cap rate search")
                return {"cap_rate": 0.06, "source": "default"}  # Default 6% cap rate
            
            query = f"cap rate capitalization rate {location} real estate investment property 2024"
            
            logger.info(f"Searching for cap rates: {query}")
            
            # Search using Tavily
            response = self.tavily_client.search(
                query=query,
                search_depth="advanced",
                max_results=5
            )
            
            if not response or 'results' not in response:
                logger.warning("No cap rate data found")
                return {"cap_rate": 0.06, "source": "default"}
            
            # Extract cap rate information
            cap_rate_data = self._extract_cap_rates(response['results'], location)
            logger.info(f"Found cap rates: {cap_rate_data}")
            
            return cap_rate_data
            
        except Exception as e:
            logger.error(f"Error searching for cap rates: {str(e)}")
            return {"cap_rate": 0.06, "source": "default"}
    
    def _extract_cap_rates(self, search_results: List[Dict], location: str) -> Dict[str, Any]:
        """Extract cap rate information from search results."""
        try:
            cap_rates = []
            
            # Look for cap rate information in search results
            for result in search_results:
                content = result.get('content', '').lower()
                
                # Common patterns for cap rates
                patterns = [
                    r'cap\s+rate[:\s]*(\d+\.?\d*)%',
                    r'capitalization\s+rate[:\s]*(\d+\.?\d*)%',
                    r'(\d+\.?\d*)%\s+cap\s+rate',
                    r'(\d+\.?\d*)\s*percent\s+cap',
                    r'cap[:\s]*(\d+\.?\d*)\s*percent'
                ]
                
                for pattern in patterns:
                    matches = re.findall(pattern, content)
                    for match in matches:
                        try:
                            rate = float(match) / 100  # Convert percentage to decimal
                            
                            # Sanity check - typical cap rates are between 3% and 15%
                            if 0.03 <= rate <= 0.15:
                                cap_rates.append(rate)
                        except ValueError:
                            continue
            
            if cap_rates:
                # Use average of found cap rates
                avg_cap_rate = sum(cap_rates) / len(cap_rates)
                return {
                    "cap_rate": avg_cap_rate,
                    "source": "tavily_search",
                    "description": f"Cap rate for {location}",
                    "rates_found": len(cap_rates),
                    "content_snippet": search_results[0].get('content', '')[:200] if search_results else ""
                }
            
            # If no specific rate found, return regional average
            logger.warning(f"Could not extract specific cap rate from search results for {location}")
            return {"cap_rate": 0.06, "source": "estimated_average"}
            
        except Exception as e:
            logger.error(f"Error extracting cap rates: {str(e)}")
            return {"cap_rate": 0.06, "source": "default"}
    
    def calculate_property_value(
        self, 
        effective_gross_income: float, 
        cap_rate: float
    ) -> Dict[str, Any]:
        """Calculate property value using effective gross income and cap rate."""
        try:
            # Property Value = NOI / Cap Rate
            # Using effective gross income as proxy for NOI (simplified calculation)
            estimated_value = effective_gross_income / cap_rate if cap_rate > 0 else 0
            
            logger.info(f"Property valuation: ${effective_gross_income:.2f} / {cap_rate*100:.2f}% = ${estimated_value:.2f}")
            
            return {
                "estimated_value": estimated_value,
                "effective_gross_income": effective_gross_income,
                "cap_rate": cap_rate,
                "valuation_method": "income_approach"
            }
            
        except Exception as e:
            logger.error(f"Error calculating property value: {str(e)}")
            return {"estimated_value": 0, "effective_gross_income": effective_gross_income, "cap_rate": cap_rate}
    
    async def estimate_property_value(self, context: PropertyValuationContext) -> Dict[str, Any]:
        """Estimate property value using cap rates and income data."""
        try:
            # Get cap rates for the location
            cap_rate_data = await self.get_cap_rates(context.zipcode, context.city, context.state)
            cap_rate = cap_rate_data.get('cap_rate', 0.06)
            
            # Use effective gross income or rental income as fallback
            income_to_use = context.effective_gross_income or context.rental_income or 0
            
            if income_to_use <= 0:
                logger.warning("No income data available for property valuation")
                return {
                    "estimated_value": 0,
                    "cap_rate": cap_rate,
                    "income_used": 0,
                    "error": "No income data available"
                }
            
            # Calculate property value
            valuation = self.calculate_property_value(income_to_use, cap_rate)
            
            # Add source information
            valuation.update({
                "cap_rate_source": cap_rate_data.get('source', 'unknown'),
                "location_analyzed": f"{context.city}, {context.state}" if context.city and context.state else context.zipcode,
                "income_used": income_to_use
            })
            
            return valuation
            
        except Exception as e:
            logger.error(f"Error estimating property value: {str(e)}")
            return {"estimated_value": 0, "error": str(e)} 