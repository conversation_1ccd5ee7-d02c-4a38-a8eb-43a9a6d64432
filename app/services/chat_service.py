"""
Chat service module for managing chat-related operations.
"""
from uuid import uuid4
from datetime import datetime
from typing import List, Optional, Dict, Any

from app.utils.logger import get_logger
from app.models.message import Message
from app.repository.db import get_db_client

logger = get_logger(__name__)

class ChatService:
    """
    Service for handling chat-related operations.
    Uses the standardized database access pattern.
    """
    
    async def create_chat(self, portfolio_id: str, user_id: str) -> Dict[str, Any]:
        """
        Create a new chat.
        
        Args:
            portfolio_id: Portfolio ID for this chat
            user_id: User ID of the creator
            
        Returns:
            Dict: The newly created chat
        """
        chat_id = str(uuid4())
        now = datetime.utcnow().isoformat()
        
        try:
            # Get the client
            client = await get_db_client()
            
            # Create chat in Supabase
            chat_data = {
                "id": chat_id,
                "name": "Portfolio Chat",
                "portfolio_id": portfolio_id,
                "created_by": user_id,
                "created_at": now,
                "is_deleted": False
            }
            
            # Insert into chat table
            client.table("chat").insert(chat_data).execute()
            
            # Determine user role based on access type
            user_role = await self._determine_user_role(portfolio_id, user_id)
            
            # Add creator as chat member with appropriate role
            member_data = {
                "id": str(uuid4()),
                "user_id": user_id,
                "chat_id": chat_id,
                "role": user_role,
                "added_at": now
            }
            
            # Insert into chat_members table
            client.table("chat_members").insert(member_data).execute()
            
            logger.info(f"Created new chat with ID: {chat_id}, user role: {user_role}")
            return chat_data
            
        except Exception as e:
            logger.error(f"Error creating chat: {str(e)}")
            raise
    
    async def _determine_user_role(self, portfolio_id: str, user_id: str) -> str:
        """
        Determine the appropriate role for a user in a chat based on their access type.
        
        Args:
            portfolio_id: The ID of the portfolio
            user_id: The ID of the user
            
        Returns:
            str: 'owner' for workspace members, 'guest' for dataroom guests
        """
        try:
            client = await get_db_client()
            
            # First check if user has workspace access
            portfolio_response = client.table("portfolios").select("workspace_id").eq("id", portfolio_id).execute()
            if portfolio_response.data:
                portfolio_data = portfolio_response.data[0]
                workspace_id = portfolio_data.get("workspace_id")
                
                # Check workspace membership if workspace_id exists
                if workspace_id:
                    workspace_member_response = client.table("workspace_members")\
                        .select("*")\
                        .eq("workspace_id", workspace_id)\
                        .eq("user_id", user_id)\
                        .execute()
                    
                    if workspace_member_response.data and len(workspace_member_response.data) > 0:
                        logger.info(f"User {user_id} is workspace member for portfolio {portfolio_id}, role: owner")
                        return "owner"
            
            # Check if user is a dataroom guest
            guest_response = client.table("dataroom_guests").select("*")\
                .eq("portfolio_id", portfolio_id)\
                .eq("receiver_id", user_id)\
                .eq("has_access", True)\
                .execute()
            
            if guest_response.data and len(guest_response.data) > 0:
                logger.info(f"User {user_id} is dataroom guest for portfolio {portfolio_id}, role: guest")
                return "guest"
            
            # Default to owner if no specific access type found (shouldn't happen if access check passed)
            logger.warning(f"Could not determine access type for user {user_id} and portfolio {portfolio_id}, defaulting to owner")
            return "owner"
            
        except Exception as e:
            logger.error(f"Error determining user role: {str(e)}")
            return "owner"
    
    async def check_portfolio_access(self, portfolio_id: str, user_id: str) -> bool:
        """
        Check if a user has access to a portfolio through workspace membership or dataroom guest access.
        
        Args:
            portfolio_id: The ID of the portfolio
            user_id: The ID of the user
            
        Returns:
            bool: True if user has access, False otherwise
        """
        try:
            client = await get_db_client()
            
            # First, get portfolio to find workspace_id
            try:
                portfolio_response = client.table("portfolios").select("workspace_id").eq("id", portfolio_id).execute()
                if portfolio_response.data:
                    portfolio_data = portfolio_response.data[0]
                    workspace_id = portfolio_data.get("workspace_id")
                    
                    # Check workspace membership if workspace_id exists and is not null
                    if workspace_id:
                        workspace_member_response = client.table("workspace_members")\
                            .select("*")\
                            .eq("workspace_id", workspace_id)\
                            .eq("user_id", user_id)\
                            .execute()
                        
                        if workspace_member_response.data and len(workspace_member_response.data) > 0:
                            logger.info(f"User {user_id} has workspace access to portfolio {portfolio_id}")
                            return True
                    else:
                        logger.info(f"Portfolio {portfolio_id} has no workspace_id, checking other access methods")
                else:
                    logger.warning(f"Portfolio {portfolio_id} not found")
                    return False
            except Exception as e:
                logger.warning(f"Error checking portfolio/workspace: {str(e)}")
            
            # Check dataroom guest access
            guest_response = client.table("dataroom_guests").select("*")\
                .eq("portfolio_id", portfolio_id)\
                .eq("receiver_id", user_id)\
                .eq("has_access", True)\
                .execute()
            
            if guest_response.data and len(guest_response.data) > 0:
                logger.info(f"User {user_id} has dataroom guest access to portfolio {portfolio_id}")
                return True
            
            logger.warning(f"User {user_id} has no access to portfolio {portfolio_id}")
            return False
            
        except Exception as e:
            logger.error(f"Error checking portfolio access: {str(e)}")
            return False
    
    async def get_user_portfolio_chat(self, portfolio_id: str, user_id: str) -> Optional[Dict[str, Any]]:
        """
        Get a user's specific chat for a portfolio.
        
        Args:
            portfolio_id: The ID of the portfolio
            user_id: The ID of the user
            
        Returns:
            Optional[Dict]: The chat if found, None otherwise
        """
        try:
            client = await get_db_client()
            
            # Query chats where this user is a member and portfolio matches
            member_response = client.table("chat_members")\
                .select("chat_id")\
                .eq("user_id", user_id)\
                .execute()
            
            if not member_response.data:
                return None
                
            # Get all chat IDs this user is a member of
            user_chat_ids = [item["chat_id"] for item in member_response.data]
            
            # Find the chat for this portfolio among the user's chats
            for chat_id in user_chat_ids:
                chat_response = client.table("chat")\
                    .select("*")\
                    .eq("id", chat_id)\
                    .eq("portfolio_id", portfolio_id)\
                    .eq("is_deleted", False)\
                    .execute()
                
                if chat_response.data:
                    return chat_response.data[0]
            
            return None
            
        except Exception as e:
            logger.error(f"Error getting user portfolio chat: {str(e)}")
            return None
    
    async def get_or_create_user_portfolio_chat(self, portfolio_id: str, user_id: str) -> Optional[Dict[str, Any]]:
        """
        Get a user's chat for a portfolio, creating one if it doesn't exist (with access checks).
        
        Args:
            portfolio_id: The ID of the portfolio
            user_id: The ID of the user
            
        Returns:
            Optional[Dict]: The chat if found/created, None if access denied
        """
        try:
            # First check if user has access to this portfolio
            has_access = await self.check_portfolio_access(portfolio_id, user_id)
            if not has_access:
                logger.warning(f"User {user_id} denied access to portfolio {portfolio_id}")
                return None
            
            # Try to get existing chat
            existing_chat = await self.get_user_portfolio_chat(portfolio_id, user_id)
            if existing_chat:
                return existing_chat
            
            # Create new chat if none exists
            new_chat = await self.create_chat(portfolio_id, user_id)
            logger.info(f"Created new chat {new_chat['id']} for user {user_id} and portfolio {portfolio_id}")
            return new_chat
            
        except Exception as e:
            logger.error(f"Error getting or creating user portfolio chat: {str(e)}")
            return None
    
    async def get_chats_by_portfolio_and_user(self, portfolio_id: str, user_id: str) -> List[Dict[str, Any]]:
        """
        Get all active chats for a portfolio that belong to a specific user.
        
        Args:
            portfolio_id: Portfolio ID to find chats for
            user_id: User ID to filter chats by
            
        Returns:
            List[Dict]: List of chat data for this user, empty list if none found
        """
        try:
            client = await get_db_client()
            
            # First get all chat IDs the user is a member of
            member_response = client.table("chat_members")\
                .select("chat_id")\
                .eq("user_id", user_id)\
                .execute()
            
            if not member_response.data:
                return []
                
            # Get all chat IDs this user is a member of
            user_chat_ids = [item["chat_id"] for item in member_response.data]
            
            # Find chats for this portfolio among the user's chats
            user_portfolio_chats = []
            for chat_id in user_chat_ids:
                chat_response = client.table("chat")\
                    .select("*")\
                    .eq("id", chat_id)\
                    .eq("portfolio_id", portfolio_id)\
                    .eq("is_deleted", False)\
                    .execute()
                
                if chat_response.data:
                    user_portfolio_chats.extend(chat_response.data)
            
            # Sort manually in descending order by created_at
            if user_portfolio_chats:
                user_portfolio_chats.sort(key=lambda x: x.get("created_at", ""), reverse=True)
            
            return user_portfolio_chats
            
        except Exception as e:
            logger.error(f"Error getting chats for portfolio {portfolio_id} and user {user_id}: {str(e)}")
            return []
    
    async def get_chat(self, chat_id: str) -> Optional[Dict[str, Any]]:
        """
        Retrieve a chat by ID.
        
        Args:
            chat_id: The ID of the chat to retrieve
            
        Returns:
            Optional[Dict]: The chat if found, None otherwise
        """
        try:
            # Get the client
            client = await get_db_client()
            
            # Query chat table
            chat_response = client.table("chat").select("*").eq("id", chat_id).execute()
            
            if not chat_response.data:
                logger.warning(f"Chat not found: {chat_id}")
                return None
                
            chat_data = chat_response.data[0]
            
            # Get messages for this chat
            messages_response = client.table("chat_messages").select("*").eq("chat_id", chat_id).eq("is_deleted", False).order("created_at").execute()
            
            # Get chat members
            members_response = client.table("chat_members").select("*").eq("chat_id", chat_id).execute()
            
            # Combine data
            chat_data["messages"] = messages_response.data
            chat_data["members"] = members_response.data
            
            return chat_data
            
        except Exception as e:
            logger.error(f"Error retrieving chat {chat_id}: {str(e)}")
            return None
    
    async def save_message(self, chat_id: str, message: Message) -> bool:
        """
        Save a message to chat_messages table.
        
        Args:
            chat_id: The ID of the chat
            message: The message to add
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Get the client
            client = await get_db_client()
            
            # CHANGED: Use image_urls directly from message content
            attachments = None
            if message.content.image_urls:
                attachments = {
                    "image_urls": message.content.image_urls
                }
                
            # Prepare metadata if present
            metadata = message.content.metadata if message.content.metadata else None
            
            # Store in chat_messages table
            message_data = {
                "id": str(uuid4()),
                "chat_id": str(message.chat_id),
                "user_id": str(message.user_id),
                "message": message.content.text,
                "role": message.role.value,
                "created_at": message.timestamp.isoformat(),
                "updated_at": message.timestamp.isoformat(),
                "is_deleted": False,
                "attachments": attachments,
                "metadata": metadata
            }
            
            client.table("chat_messages").insert(message_data).execute()
            
            # Update the chat's updated_at timestamp
            client.table("chat").update({"updated_at": message.timestamp.isoformat()}).eq("id", chat_id).execute()
            
            return True
            
        except Exception as e:
            logger.error(f"Error storing message for chat {chat_id}: {str(e)}")
            return False
    
    async def list_chats(self, user_id: str, limit: int = 100, offset: int = 0) -> List[Dict[str, Any]]:
        """
        List all chats for a user with pagination.
        
        Args:
            user_id: User ID to fetch chats for
            limit: Maximum number of chats to return
            offset: Offset for pagination
            
        Returns:
            List[Dict]: List of chats
        """
        try:
            # Get the client
            client = await get_db_client()
            
            # First get all chat IDs the user is a member of
            member_response = client.table("chat_members").select("chat_id").eq("user_id", user_id).execute()
            
            if not member_response.data:
                return []
                
            # Extract chat IDs
            chat_ids = [item["chat_id"] for item in member_response.data]
            
            # Get full chat data for these IDs
            chats = []
            for chat_id in chat_ids:
                # Get chat data
                chat_response = client.table("chat").select("*").eq("id", chat_id).eq("is_deleted", False).execute()
                
                if chat_response.data:
                    chat_data = chat_response.data[0]
                    
                    # Add to results
                    chats.append(chat_data)
            
            # Sort by created_at descending
            chats.sort(key=lambda x: x.get("created_at", ""), reverse=True)
            
            # Apply pagination
            return chats[offset:offset + limit]
            
        except Exception as e:
            logger.error(f"Error listing chats for user {user_id}: {str(e)}")
            return []
    
    async def delete_chat(self, chat_id: str) -> bool:
        """
        Soft delete a chat by ID (mark as deleted).
        
        Args:
            chat_id: The ID of the chat to delete
            
        Returns:
            bool: True if the chat was deleted, False otherwise
        """
        try:
            # Get the client
            client = await get_db_client()
            
            # Check if chat exists
            chat_response = client.table("chat").select("*").eq("id", chat_id).execute()
            if not chat_response.data:
                logger.warning(f"Failed to delete chat: {chat_id} (not found)")
                return False
            
            # Soft delete by setting is_deleted = true
            client.table("chat").update({"is_deleted": True, "updated_at": datetime.utcnow().isoformat()}).eq("id", chat_id).execute()
            
            logger.info(f"Deleted chat: {chat_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error deleting chat {chat_id}: {str(e)}")
            return False
    
    async def add_chat_member(self, chat_id: str, user_id: str, role: str = "member") -> bool:
        """
        Add a member to a chat.
        
        Args:
            chat_id: Chat ID to add member to
            user_id: User ID to add as member
            role: Member role (default: "member")
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Get the client
            client = await get_db_client()
            
            # Check if member already exists
            existing_response = client.table("chat_members").select("*").eq("chat_id", chat_id).eq("user_id", user_id).execute()
            if existing_response.data:
                logger.info(f"User {user_id} is already a member of chat {chat_id}")
                return True
            
            # Add the member
            member_data = {
                "id": str(uuid4()),
                "chat_id": chat_id,
                "user_id": user_id,
                "role": role,
                "added_at": datetime.utcnow().isoformat()
            }
            
            client.table("chat_members").insert(member_data).execute()
            logger.info(f"Added user {user_id} to chat {chat_id} with role {role}")
            return True
            
        except Exception as e:
            logger.error(f"Error adding member to chat {chat_id}: {str(e)}")
            return False
    
    async def remove_chat_member(self, chat_id: str, user_id: str) -> bool:
        """
        Remove a member from a chat.
        
        Args:
            chat_id: Chat ID to remove member from
            user_id: User ID to remove
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Get the client
            client = await get_db_client()
            
            # Delete the member record
            client.table("chat_members").delete().eq("chat_id", chat_id).eq("user_id", user_id).execute()
            logger.info(f"Removed user {user_id} from chat {chat_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error removing member from chat {chat_id}: {str(e)}")
            return False
    
    async def check_chat_access(self, chat_id: str, user_id: str) -> bool:
        """
        Check if a user has access to a specific chat.
        
        Args:
            chat_id: Chat ID to check access for
            user_id: User ID to check
            
        Returns:
            bool: True if user has access, False otherwise
        """
        try:
            # Get the client
            client = await get_db_client()
            
            # First check if user is a member of this chat
            member_response = client.table("chat_members")\
                .select("*")\
                .eq("chat_id", chat_id)\
                .eq("user_id", user_id)\
                .execute()
                
            if member_response.data:
                logger.info(f"User {user_id} has access to chat {chat_id} (member)")
                return True
            
            # If not a direct member, check if the user has portfolio access
            # First get the chat to find its portfolio_id
            chat_response = client.table("chat")\
                .select("portfolio_id")\
                .eq("id", chat_id)\
                .eq("is_deleted", False)\
                .execute()
                
            if not chat_response.data:
                logger.warning(f"Chat {chat_id} not found or deleted")
                return False
            
            portfolio_id = chat_response.data[0]["portfolio_id"]
            
            # Check portfolio access
            has_portfolio_access = await self.check_portfolio_access(portfolio_id, user_id)
            if has_portfolio_access:
                logger.info(f"User {user_id} has access to chat {chat_id} (portfolio access)")
                return True
                
            logger.warning(f"User {user_id} does not have access to chat {chat_id}")
            return False
            
        except Exception as e:
            logger.error(f"Error checking chat access for user {user_id} and chat {chat_id}: {str(e)}")
            return False
    
    async def get_chat_with_access_check(self, chat_id: str, user_id: str) -> Optional[Dict[str, Any]]:
        """
        Retrieve a chat by ID after verifying user has access.
        
        Args:
            chat_id: The ID of the chat to retrieve
            user_id: The ID of the user requesting access
            
        Returns:
            Optional[Dict]: The chat if found and accessible, None otherwise
        """
        try:
            # Check access first
            has_access = await self.check_chat_access(chat_id, user_id)
            if not has_access:
                logger.warning(f"User {user_id} denied access to chat {chat_id}")
                return None
            
            # If access is granted, return the chat
            return await self.get_chat(chat_id)
            
        except Exception as e:
            logger.error(f"Error getting chat {chat_id} with access check for user {user_id}: {str(e)}")
            return None
    
    async def clear_chat_messages(self, chat_id: str) -> bool:
        """
        Mark all messages in a chat as deleted (is_deleted=True).
        This effectively clears the chat history without removing the chat itself.
        
        Args:
            chat_id: Chat ID to clear messages for
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Get the client
            client = await get_db_client()
            
            # First check if the chat exists and is not deleted
            chat_response = client.table("chat").select("*").eq("id", chat_id).eq("is_deleted", False).execute()
            if not chat_response.data:
                logger.warning(f"Cannot clear messages: chat {chat_id} not found or already deleted")
                return False
            
            # Mark all messages in this chat as deleted
            now = datetime.utcnow().isoformat()
            client.table("chat_messages").update({"is_deleted": True, "updated_at": now}).eq("chat_id", chat_id).execute()
            
            # Update the chat's updated_at timestamp
            client.table("chat").update({"updated_at": now}).eq("id", chat_id).execute()
            
            logger.info(f"Cleared all messages in chat: {chat_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error clearing messages for chat {chat_id}: {str(e)}")
            return False

    async def update_chat_name(self, chat_id: str, new_name: str) -> bool:
        """
        Update the name of a chat.
        
        Args:
            chat_id: Chat ID to update
            new_name: New name for the chat
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            client = await get_db_client()
            
            # Update chat name
            now = datetime.utcnow().isoformat()
            client.table("chat").update({
                "name": new_name,
                "updated_at": now
            }).eq("id", chat_id).execute()
            
            logger.info(f"Updated chat {chat_id} name to: {new_name}")
            return True
            
        except Exception as e:
            logger.error(f"Error updating chat name for {chat_id}: {str(e)}")
            return False

    async def get_user_message_count(self, chat_id: str) -> int:
        """
        Get the count of user messages in a chat.
        
        Args:
            chat_id: Chat ID to count messages for
            
        Returns:
            int: Number of user messages in the chat
        """
        try:
            client = await get_db_client()
            
            # Count user messages that aren't deleted
            response = client.table("chat_messages")\
                .select("id", count="exact")\
                .eq("chat_id", chat_id)\
                .eq("role", "user")\
                .eq("is_deleted", False)\
                .execute()
            
            return response.count or 0
            
        except Exception as e:
            logger.error(f"Error counting user messages for chat {chat_id}: {str(e)}")
            return 0

    async def get_recent_messages_for_naming(self, chat_id: str, limit: int = 10) -> List[Dict[str, Any]]:
        """
        Get recent messages from a chat for name generation purposes.
        
        Args:
            chat_id: Chat ID to get messages from
            limit: Maximum number of messages to return
            
        Returns:
            List[Dict]: List of recent messages with role and message content
        """
        try:
            client = await get_db_client()
            
            # Get recent messages
            response = client.table("chat_messages")\
                .select("role", "message", "created_at")\
                .eq("chat_id", chat_id)\
                .eq("is_deleted", False)\
                .order("created_at", desc=False)\
                .limit(limit)\
                .execute()
            
            return response.data or []
            
        except Exception as e:
            logger.error(f"Error getting recent messages for chat {chat_id}: {str(e)}")
            return []

# Create a singleton instance
chat_service = ChatService() 