"""
Web search service for retrieving data from external sources using internet search.
"""
import time
import random
from typing import List, Any
from pydantic import BaseModel, Field
from dotenv import load_dotenv

# For web search functionality
from pydantic_ai import Agent, RunContext

# Try to import duckduckgo, but handle gracefully if not installed
try:
    from pydantic_ai.common_tools.duckduckgo import duckduckgo_search_tool
    DUCKDUCKGO_AVAILABLE = True
except ImportError:
    DUCKDUCKGO_AVAILABLE = False

from app.utils.logger import get_logger

# Load environment variables
load_dotenv()

logger = get_logger(__name__)

class SearchResult(BaseModel):
    """Model to represent search results."""
    title: str = Field(..., description="Title of the search result")
    url: str = Field(..., description="URL of the search result")
    snippet: str = Field(..., description="Snippet/description of the search result")
    source: str = Field(default="web_search", description="Source of the result")

class WebSearchService:
    """Service for performing web searches to gather data from the internet."""
    
    def __init__(self):
        """Initialize the web search service."""
        # Check if DuckDuckGo search is available
        self.search_agent = None
        
        # Track searches to avoid rate limiting
        self.last_search_time = 0
        self.search_count = 0
        self.max_searches_per_hour = 10  # Limit total searches
        self.min_search_interval = 10  # Seconds between searches
        
        if DUCKDUCKGO_AVAILABLE:
            # Initialize the DuckDuckGo search agent
            self.search_agent = Agent(
                model="openai:gpt-4o-mini",
                tools=[duckduckgo_search_tool()],
                system_prompt="""
                    You are a research assistant specialized in finding accurate information.
                    When searching:
                    1. Focus on reliable and authoritative sources
                    2. Extract specific data when available
                    3. Note the recency and relevance of the information
                    4. Provide concise and accurate summaries
                    
                    Keep searches focused on the specific query.
                    """
            ) # type: ignore
            logger.info("DuckDuckGo search tool initialized successfully")
        else:
            # Initialize agent without search capabilities
            self.search_agent = Agent(
                model="openai:gpt-4o-mini",
                tools=[],  # No tools available
                system_prompt="""
                You are a research assistant specialized in analyzing information.
                
                IMPORTANT: Web search capabilities are currently unavailable.
                Please notify the user that you are unable to perform web searches.
                """
            )
            logger.warning("DuckDuckGo search tool not available. Install with: pip install 'pydantic-ai-slim[duckduckgo]'")
    
    async def search(self, ctx: RunContext[Any], query: str) -> List[SearchResult]:
        """
        Search for information on the internet based on the provided query.
        
        Args:
            ctx: RunContext containing task input parameters
            query: Search query string
            
        Returns:
            List of search results
        """
        # Check if search agent is available
        if not self.search_agent:
            logger.warning("Search functionality not available - missing dependency: duckduckgo-search")
            return [SearchResult(
                title="Search Unavailable",
                url="",
                snippet="Web search functionality is not available. Install required dependencies: pip install 'pydantic-ai-slim[duckduckgo]'",
                source="error"
            )]
            
        # Check rate limits
        current_time = time.time()
        hour_ago = current_time - 3600
        
        # Reset counter if more than an hour has passed
        if self.last_search_time < hour_ago:
            self.search_count = 0
            
        # Check if we've exceeded our hourly limit
        if self.search_count >= self.max_searches_per_hour:
            logger.warning(f"Search rate limit reached: {self.max_searches_per_hour} searches per hour")
            return [SearchResult(
                title="Search Rate Limited",
                url="",
                snippet=f"Web search rate limit reached ({self.max_searches_per_hour} searches per hour).",
                source="rate_limited"
            )]
            
        # Check if we need to wait between searches
        time_since_last_search = current_time - self.last_search_time
        if time_since_last_search < self.min_search_interval:
            wait_time = self.min_search_interval - time_since_last_search
            logger.info(f"Rate limiting: waiting {wait_time:.2f} seconds between searches")
            time.sleep(wait_time)
            
        try:
            # Add jitter to avoid predictable patterns
            jitter = random.uniform(0.5, 2.0)
            time.sleep(jitter)
            
            # Use PydanticAI agent with DuckDuckGo tool to search
            search_result = await self.search_agent.run(
                f"Search for information about: {query}. Provide detailed and accurate results."
            )
            
            # Update search tracking
            self.last_search_time = time.time()
            self.search_count += 1
            
            # Extract structured results from the search output
            parsed_results = self._parse_search_response(search_result.output)
            logger.info(f"Found {len(parsed_results)} search results for query: {query}")
            
            return parsed_results
            
        except Exception as e:
            logger.error(f"Error searching for data: {str(e)}")
            
            # Return a fallback result with explanation
            return [SearchResult(
                title="Search Error",
                url="",
                snippet=f"Unable to search for information. Error: {str(e)}",
                source="error"
            )]
    
    def _parse_search_response(self, search_output: str) -> List[SearchResult]:
        """
        Parse the search output into structured results.
        
        Args:
            search_output: Raw output from the search agent
            
        Returns:
            List of structured search results
        """
        results = []
        
        # Split output by lines or sources
        lines = search_output.split("\n")
        current_result = None
        
        for line in lines:
            line = line.strip()
            
            # Look for URL patterns to identify sources
            if "http" in line or "www." in line:
                # Extract URL using basic pattern recognition
                url_start = line.find("http")
                if url_start == -1:
                    url_start = line.find("www.")
                
                url_end = line.find(" ", url_start)
                if url_end == -1:
                    url = line[url_start:].strip("., )")
                else:
                    url = line[url_start:url_end].strip("., )")
                
                # If URL found, create a new result
                if current_result and current_result.title:
                    results.append(current_result)
                
                # Extract full title from the line
                title = line.replace(url, "").strip(".: -")
                if not title:
                    title = "Search Result"  # Default title if none found
                
                current_result = SearchResult(
                    title=title,
                    url=url,
                    snippet="",
                    source="web_search"
                )
            
            # If we have a current result, add the line to its snippet
            elif current_result is not None:
                if current_result.snippet:
                    current_result.snippet += " " + line
                else:
                    current_result.snippet = line
            
            # If line contains relevant information but we don't have a current result
            elif len(line) > 10:  # Arbitrary threshold to filter out short lines
                current_result = SearchResult(
                    title=line[:100],  # Use more of the line as title
                    url="",  # No URL available for this result
                    snippet=line,  # Use the full line as the snippet
                    source="web_search"
                )
        
        # Add the last result if not added yet
        if current_result and current_result.title:
            results.append(current_result)
            
        # If no structured results were found, create a single result from the entire output
        if not results and search_output.strip():
            results.append(SearchResult(
                title="Search Results Summary",
                url="",
                snippet=search_output,  # Include full search output as snippet
                source="web_search"
            ))
            
        return results

# Initialize a singleton instance
web_search_service = WebSearchService()