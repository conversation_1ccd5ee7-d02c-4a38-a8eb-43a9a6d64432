import os
import re
import json
import uuid
import nomic
import cohere
import PyPDF2
import asyncio
import requests
import traceback
import instructor
import turbopuffer as tpuf

from nomic import embed
from openai import OpenAI
from typing import Dict, Any, List, Literal
from datetime import datetime
from dotenv import load_dotenv

from app.utils.logger import get_logger
from app.repository.db import get_db_client
from app.models.prompts import AgentPrompts
import app.utils.html_to_pdf as html_to_pdf
from app.repository.cache import redis_cache
from app.models.ai_responses import DocumentAIMetadata, SlidesResponse

# LangChain imports
from langchain_text_splitters import CharacterTextSplitter
from langchain_community.document_loaders import PyPDFLoader


load_dotenv()

logger = get_logger(__name__)

# API Keys
COHERE_API_KEY = os.getenv("COHERE_API_KEY")
if not COHERE_API_KEY:
    raise RuntimeError("COHERE_API_KEY not set in environment variables")

NOMIC_API_KEY = os.getenv("NOMIC_API_KEY")
if not NOMIC_API_KEY:
    raise RuntimeError("NOMIC_API_KEY not set in environment variables")

# Initialize Cohere
cohere_client = None
if COHERE_API_KEY:
    cohere_client = cohere.Client(COHERE_API_KEY)

# Initialize Nomic if key is available
if NOMIC_API_KEY:
    nomic.cli.login(token=NOMIC_API_KEY)

# OpenAI API key
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
if not OPENAI_API_KEY:
    raise RuntimeError("OPENAI_API_KEY not set in environment variables")


class TurboPufferClient:
    """TurboPuffer vector DB client using Namespace and ns.write API."""
    def __init__(self):
        self.api_key = os.getenv("TURBOPUFFER_API_KEY")
        self.region = os.getenv("TURBOPUFFER_REGION")
        if not self.api_key:
            raise RuntimeError("TURBOPUFFER_API_KEY not set in environment variables")
        tpuf.api_key = self.api_key
        tpuf.api_base_url = self.region

    def store_vectors(self, namespace: str, ids: list, vectors: list, attributes: dict):
        ns = tpuf.Namespace(namespace)
        upsert_columns = {
            "id": ids,
            "vector": vectors,
        }
        for key, value in attributes.items():
            upsert_columns[key] = value
        ns.write(
            upsert_columns=upsert_columns,
            distance_metric='cosine_distance',
            schema={
                "text": {
                    "type": "string",
                    "full_text_search": True,
                }
            }
        )
        return True

class DocumentService:
    def __init__(self):
        self.cohere_client = cohere_client
        self.tpuf_client = TurboPufferClient()
        self.prompts = AgentPrompts()
        # Set up OpenAI client with Instructor
        self.openai_client = instructor.patch(OpenAI(api_key=OPENAI_API_KEY))
    
    def generate_task_token(self) -> str:
        """Generate a short unique task token for tracking upload progress."""
        import random
        import string
        # Generate 8-character alphanumeric token (uppercase letters + digits)
        chars = string.ascii_uppercase + string.digits
        return ''.join(random.choices(chars, k=8))
    
    def _get_upload_status_key(self, portfolio_id: str, token: str) -> str:
        """Generate Redis key for upload status tracking."""
        return f"upload_status:{portfolio_id}:{token}"
    
    async def initialize_upload_task(self, portfolio_id: str, token: str, document_urls: List[str]) -> Dict[str, Any]:
        """
        Initialize a new upload task in Redis with all documents set to 'processing' status.
        
        Args:
            portfolio_id: Portfolio ID
            token: Unique task token
            document_urls: List of Supabase URLs to process
            
        Returns:
            Initial task status data
        """
        # Get database client to query document IDs
        client = await get_db_client()
        
        # Extract document names from URLs and get actual document IDs
        documents = []
        for url in document_urls:
            # Extract filename from URL
            filename = url.split("/")[-1]
            # Remove timestamp suffix if present (from previous processing)
            if "_" in filename and filename.count("_") >= 2:
                # Try to extract original name by removing timestamp
                parts = filename.split("_")
                if len(parts) >= 3 and parts[-1].replace(".pdf", "").isdigit():
                    # Looks like a timestamped file, reconstruct original name
                    original_name = "_".join(parts[:-1]) + ".pdf"
                    filename = original_name
            
            # Query database to get the actual document ID
            document_id = None
            try:
                docs = client.table("documents").select("id,filename").eq("supabase_url", url).execute()
                if docs and docs.data:
                    document_id = str(docs.data[0]["id"])
                    # Use the actual filename from database if available
                    db_filename = docs.data[0].get("filename")
                    if db_filename:
                        filename = db_filename
                    logger.info(f"Found document_id {document_id} for URL {url}")
                else:
                    logger.error(f"No document found in database for URL {url} - skipping this document")
                    continue  # Skip this document and continue with the next one
            except Exception as e:
                logger.error(f"Error querying document_id for URL {url}: {str(e)} - skipping this document")
                continue  # Skip this document and continue with the next one
            
            # Only add document if we found a valid document_id
            if document_id:
                doc_status = {
                    "id": document_id,  # Use actual document_id from database
                    "name": filename,
                    "supabase_url": url,
                    "status": "processing",
                    "message": "Initializing document processing",
                    "datetime": datetime.now().isoformat()
                }
                documents.append(doc_status)
        
        # Check if we have any valid documents after filtering
        if not documents:
            logger.error("No valid documents found in database for any of the provided URLs")
            raise ValueError("No valid documents found in database. All documents must exist in the database before processing.")
        
        logger.info(f"Proceeding with {len(documents)} valid documents out of {len(document_urls)} provided URLs")
        
        # Initialize task data
        task_data = {
            "is_ready": False,
            "percentage": 0,
            "documents": documents,
            "total_documents": len(documents),
            "completed_documents": 0,
            "failed_documents": 0
        }
        
        # Store in Redis with 1 hour TTL
        cache_key = self._get_upload_status_key(portfolio_id, token)
        await redis_cache.set(cache_key, task_data, ttl=3600)
        
        logger.info(f"Initialized upload task {token} for portfolio {portfolio_id} with {len(documents)} documents")
        return task_data
    
    async def update_document_status(self, portfolio_id: str, token: str, document_id: str, 
                                   status: str, message: str = None) -> None:
        """
        Update the status of a specific document in the upload task.
        
        Args:
            portfolio_id: Portfolio ID
            token: Task token
            document_id: Document ID within the task
            status: New status ('processing', 'complete', 'failed')
            message: Optional status message
        """
        max_retries = 3
        retry_delay = 0.1  # 100ms
        
        for attempt in range(max_retries):
            try:
                cache_key = self._get_upload_status_key(portfolio_id, token)
                task_data = await redis_cache.get(cache_key)
                
                if not task_data:
                    logger.warning(f"Task data not found for token {token} (attempt {attempt + 1})")
                    if attempt < max_retries - 1:
                        await asyncio.sleep(retry_delay)
                        continue
                    return
                
                # Find and update the document
                document_updated = False
                for doc in task_data["documents"]:
                    if doc["id"] == document_id:
                        doc["status"] = status
                        doc["datetime"] = datetime.now().isoformat()
                        if message:
                            doc["message"] = message
                        document_updated = True
                        break
                
                if not document_updated:
                    logger.warning(f"Document {document_id} not found in task {token}")
                    return
                
                # Recalculate statistics
                completed = sum(1 for doc in task_data["documents"] if doc["status"] == "complete")
                failed = sum(1 for doc in task_data["documents"] if doc["status"] == "failed") 
                total = len(task_data["documents"])
                
                task_data["completed_documents"] = completed
                task_data["failed_documents"] = failed
                task_data["percentage"] = int((completed + failed) / total * 100) if total > 0 else 0
                task_data["is_ready"] = (completed + failed) == total
                
                # Update Redis
                await redis_cache.set(cache_key, task_data, ttl=3600)
                
                logger.info(f"Updated document {document_id} status to {status} in task {token}. Progress: {completed + failed}/{total} ({task_data['percentage']}%) - Ready: {task_data['is_ready']}")
                break  # Success, exit retry loop
                
            except Exception as e:
                logger.error(f"Error updating document status (attempt {attempt + 1}): {str(e)}")
                if attempt < max_retries - 1:
                    await asyncio.sleep(retry_delay)
                else:
                    logger.error(f"Failed to update document status after {max_retries} attempts")
    
    async def get_upload_status(self, portfolio_id: str, token: str) -> Dict[str, Any]:
        """
        Get the current status of an upload task.
        
        Args:
            portfolio_id: Portfolio ID
            token: Task token
            
        Returns:
            Current task status data or None if not found
        """
        try:
            cache_key = self._get_upload_status_key(portfolio_id, token)
            task_data = await redis_cache.get(cache_key)
            
            if task_data:
                # Log current status for debugging
                logger.debug(f"Retrieved task status for {token}: {task_data.get('percentage', 0)}% complete, is_ready={task_data.get('is_ready', False)}")
            else:
                logger.warning(f"No task data found for token {token} in cache key {cache_key}")
                
            return task_data
        except Exception as e:
            logger.error(f"Error getting upload status: {str(e)}")
            return None
    
    async def merge_upload_tasks(self, portfolio_id: str, existing_token: str, new_document_urls: List[str]) -> str:
        """
        Merge new documents into an existing upload task or create a new one.
        
        Args:
            portfolio_id: Portfolio ID
            existing_token: Existing task token (if any)
            new_document_urls: New document URLs to add
            
        Returns:
            Task token (existing or new)
        """
        try:
            # Check if existing task exists and is not complete
            existing_task = await self.get_upload_status(portfolio_id, existing_token) if existing_token else None
            
            if existing_task and not existing_task.get("is_ready", True):
                # Existing task is still processing, merge new documents
                logger.info(f"Merging {len(new_document_urls)} new documents into existing task {existing_token}")
                
                # Get database client to query document IDs for new documents
                client = await get_db_client()
                
                # Add new documents to existing task
                for url in new_document_urls:
                    filename = url.split("/")[-1]
                    if "_" in filename and filename.count("_") >= 2:
                        parts = filename.split("_")
                        if len(parts) >= 3 and parts[-1].replace(".pdf", "").isdigit():
                            original_name = "_".join(parts[:-1]) + ".pdf"
                            filename = original_name
                    
                    # Query database to get the actual document ID
                    document_id = None
                    try:
                        docs = client.table("documents").select("id,filename").eq("supabase_url", url).execute()
                        if docs and docs.data:
                            document_id = str(docs.data[0]["id"])
                            # Use the actual filename from database if available
                            db_filename = docs.data[0].get("filename")
                            if db_filename:
                                filename = db_filename
                            logger.info(f"Found document_id {document_id} for merge with URL {url}")
                        else:
                            logger.error(f"No document found in database for URL {url} - skipping this document")
                            continue  # Skip this document and continue with the next one
                    except Exception as e:
                        logger.error(f"Error querying document_id for URL {url}: {str(e)} - skipping this document")
                        continue  # Skip this document and continue with the next one
                    
                    # Only add document if we found a valid document_id
                    if document_id:
                        new_doc = {
                            "id": document_id,  # Use actual document_id from database
                            "name": filename,
                            "supabase_url": url,
                            "status": "processing",
                            "message": "Initializing document processing",
                            "datetime": datetime.now().isoformat()
                        }
                        existing_task["documents"].append(new_doc)
                
                # Check if we added any valid documents
                added_count = len(existing_task["documents"]) - len([doc for doc in existing_task["documents"] if doc["supabase_url"] not in new_document_urls])
                if added_count == 0:
                    logger.warning("No valid new documents found in database for any of the provided URLs during merge")
                    # If no valid documents were added, we still return the existing token but log the issue
                else:
                    logger.info(f"Added {added_count} valid documents to existing task")
                
                # Update totals
                existing_task["total_documents"] = len(existing_task["documents"])
                completed = existing_task["completed_documents"]
                failed = existing_task["failed_documents"]
                total = existing_task["total_documents"]
                existing_task["percentage"] = int((completed + failed) / total * 100) if total > 0 else 0
                existing_task["is_ready"] = (completed + failed) == total
                
                # Update Redis
                cache_key = self._get_upload_status_key(portfolio_id, existing_token)
                await redis_cache.set(cache_key, existing_task, ttl=3600)
                
                return existing_token
            else:
                # Create new task
                new_token = self.generate_task_token()
                await self.initialize_upload_task(portfolio_id, new_token, new_document_urls)
                return new_token
                
        except Exception as e:
            logger.error(f"Error merging upload tasks: {str(e)}")
            # Fallback to creating new task
            new_token = self.generate_task_token()
            await self.initialize_upload_task(portfolio_id, new_token, new_document_urls)
            return new_token
    
    async def process_document_with_status_updates(self, document_info: Dict[str, Any], 
                                                 portfolio_id: str, user_id: str, token: str) -> Dict[str, Any]:
        """
        Process a single document and update its status in the task.
        
        Args:
            document_info: Dict with document info (id, supabase_url, name)
            portfolio_id: Portfolio ID
            user_id: User ID
            token: Task token
            
        Returns:
            Processing result
        """
        document_id = document_info["id"]
        supabase_url = document_info["supabase_url"]
        
        try:
            # Update status to processing
            await self.update_document_status(portfolio_id, token, document_id, "processing", "Processing document")
            
            # Get document_id from database if available
            db_document_id = None
            try:
                client = await get_db_client()
                docs = client.table("documents").select("id").eq("supabase_url", supabase_url).execute()
                if docs and docs.data:
                    db_document_id = str(docs.data[0]["id"])
            except Exception as e:
                logger.warning(f"Could not find document_id for URL {supabase_url}: {str(e)}")
            
            # Process the document
            result = await self.process_document_for_rag(
                supabase_url=supabase_url,
                portfolio_id=portfolio_id,
                user_id=user_id,
                document_id=db_document_id
            )
            
            # Update status to complete
            await self.update_document_status(
                portfolio_id, token, document_id, "complete", 
                f"Successfully processed: {result.get('title', 'Document')}"
            )
            
            return {
                "success": True,
                "document_id": document_id,
                "result": result
            }
            
        except Exception as e:
            error_msg = f"Failed to process document: {str(e)}"
            logger.error(f"Error processing document {document_id}: {error_msg}")
            
            # Update status to failed
            await self.update_document_status(portfolio_id, token, document_id, "failed", error_msg)
            
            return {
                "success": False,
                "document_id": document_id,
                "error": error_msg
            }
    
    async def process_documents_batch(self, document_urls: List[str], portfolio_id: str, user_id: str, 
                                    existing_token: str = None) -> Dict[str, Any]:
        """
        Process multiple documents in parallel with status tracking.
        
        Args:
            document_urls: List of Supabase URLs to process
            portfolio_id: Portfolio ID
            user_id: User ID
            existing_token: Existing task token to merge with (optional)
            
        Returns:
            Dict with task token and initial status
        """
        if not document_urls:
            raise ValueError("No document URLs provided")
        
        # Merge with existing task or create new one
        token = await self.merge_upload_tasks(portfolio_id, existing_token, document_urls)
        
        # Get the current task data to find document info
        task_data = await self.get_upload_status(portfolio_id, token)
        if not task_data:
            raise RuntimeError("Failed to initialize upload task")
        
        # Find the documents that need processing (those with 'processing' status)
        documents_to_process = [
            doc for doc in task_data["documents"] 
            if doc["status"] == "processing" and doc["supabase_url"] in document_urls
        ]
        
        logger.info(f"Starting parallel processing of {len(documents_to_process)} documents")
        
        # Process documents in parallel with controlled concurrency
        async def process_single_document(doc_info):
            return await self.process_document_with_status_updates(doc_info, portfolio_id, user_id, token)
        
        # Use asyncio.gather with limited concurrency (3 documents at a time)
        semaphore = asyncio.Semaphore(3)
        
        async def process_with_semaphore(doc_info):
            async with semaphore:
                return await process_single_document(doc_info)
        
        # Start background processing
        asyncio.create_task(self._process_documents_background(documents_to_process, process_with_semaphore, portfolio_id, token))
        
        # Return immediately with task token
        return {
            "success": True,
            "task_token": token,
            "total_documents": len(documents_to_process),
            "message": f"Started processing {len(documents_to_process)} documents"
        }
    
    async def _process_documents_background(self, documents_to_process: List[Dict], process_func, portfolio_id: str, token: str):
        """Background task to process documents and invalidate cache when complete."""
        try:
            # Process all documents
            results = await asyncio.gather(*[process_func(doc) for doc in documents_to_process], return_exceptions=True)
            
            # Log results
            successful = sum(1 for r in results if isinstance(r, dict) and r.get("success"))
            failed = len(results) - successful
            
            logger.info(f"Batch processing complete: {successful} successful, {failed} failed")
            
            # Wait a brief moment to ensure all individual status updates have completed
            await asyncio.sleep(0.1)
            
            # CRITICAL: Verify and update final task status based on actual processing results
            try:
                cache_key = self._get_upload_status_key(portfolio_id, token)
                task_data = await redis_cache.get(cache_key)
                
                if task_data:
                    # Count actual document statuses from cache
                    completed_docs = sum(1 for doc in task_data["documents"] if doc["status"] == "complete")
                    failed_docs = sum(1 for doc in task_data["documents"] if doc["status"] == "failed")
                    total_docs = len(task_data["documents"])
                    
                    # Only update if the counts don't match what we processed
                    expected_completed = successful
                    expected_failed = failed
                    
                    # If the cache doesn't reflect the actual results, there was a race condition
                    if (completed_docs != expected_completed) or (failed_docs != expected_failed):
                        logger.warning(f"Cache inconsistency detected! Cache: {completed_docs} completed, {failed_docs} failed. Expected: {expected_completed} completed, {expected_failed} failed")
                        
                        # Update individual document statuses based on processing results
                        for i, (doc_info, result) in enumerate(zip(documents_to_process, results)):
                            if isinstance(result, dict):
                                # Find the document in task_data and update its status
                                for doc in task_data["documents"]:
                                    if doc["id"] == doc_info["id"]:
                                        if result.get("success"):
                                            doc["status"] = "complete"
                                            doc["message"] = f"Successfully processed: {result.get('result', {}).get('title', 'Document')}"
                                        else:
                                            doc["status"] = "failed"
                                            doc["message"] = result.get("error", "Processing failed")
                                        doc["datetime"] = datetime.now().isoformat()
                                        break
                        
                        # Recalculate after fixing individual statuses
                        completed_docs = sum(1 for doc in task_data["documents"] if doc["status"] == "complete")
                        failed_docs = sum(1 for doc in task_data["documents"] if doc["status"] == "failed")
                    
                    # Update task-level status based on actual counts (don't force values)
                    task_data["completed_documents"] = completed_docs
                    task_data["failed_documents"] = failed_docs
                    task_data["percentage"] = int((completed_docs + failed_docs) / total_docs * 100) if total_docs > 0 else 0
                    task_data["is_ready"] = (completed_docs + failed_docs) == total_docs
                    
                    # Update the cache with corrected state
                    await redis_cache.set(cache_key, task_data, ttl=3600)
                    logger.info(f"Final task status verified: {completed_docs} completed, {failed_docs} failed, {task_data['percentage']}% complete, is_ready={task_data['is_ready']}")
                else:
                    logger.warning(f"Task data not found for final update: {cache_key}")
                    
            except Exception as status_error:
                logger.error(f"Error updating final task status: {str(status_error)}")
            
            # Invalidate portfolio documents cache (both owner and guest)
            await self.invalidate_portfolio_documents_cache(portfolio_id)
            logger.info("Invalidated portfolio documents cache after batch processing")
            
        except Exception as e:
            logger.error(f"Error in background document processing: {str(e)}")

    async def generate_embeddings(self, texts: List[str], task_type: Literal["search_document", "search_query"]) -> List[List[float]]:
        """
        Generate embeddings for the given texts using the specified provider.
        
        Args:
            texts: List of text strings to embed
            task_type: Type of embedding task (document or query)
            provider: Embedding provider to use ("cohere" or "nomic")
        
        Returns:
            List of embedding vectors
        """
        provider = "nomic"
        
        if provider.lower() == "cohere":
            logger.info(f"Generating {len(texts)} embeddings with Cohere ({task_type})")
            
            # Use cohere client
            try:
                embed_response = self.cohere_client.embed(
                    texts=texts,
                    model="embed-v4.0",
                    input_type=task_type
                )
                return embed_response.embeddings
            except Exception as e:
                logger.error(f"Cohere embedding failed: {str(e)}")
                raise RuntimeError(f"Failed to generate embeddings with Cohere: {str(e)}")
                
        elif provider.lower() == "nomic":
            logger.info(f"Generating {len(texts)} embeddings with Nomic ({task_type})")
            
            # Use nomic
            try:                
                embed_response = embed.text(
                    texts=texts,
                    model="nomic-embed-text-v1.5",
                    task_type=task_type
                )
                return embed_response["embeddings"]
            except Exception as e:
                logger.error(f"Nomic embedding failed: {str(e)}")
                raise RuntimeError(f"Failed to generate embeddings with Nomic: {str(e)}")
        else:
            raise ValueError(f"Unsupported embedding provider: {provider}")
    
    async def fetch_document(self, supabase_url: str) -> str:
        """
        Fetch a document from its URL and save to a temporary file.
        
        Args:
            supabase_url: Direct URL to the document in Supabase storage
        
        Returns:
            Local path to the downloaded file
        """
        # Generate a unique ID for the temporary file
        temp_id = str(uuid.uuid4())
            
        # Download file directly from URL
        response = requests.get(supabase_url)
        response.raise_for_status()  # Raise exception for HTTP errors
        
        # Save to temporary file
        local_path = f"/tmp/{temp_id}.pdf"
        with open(local_path, "wb") as f:
            f.write(response.content)
            
        logger.info(f"Downloaded document to {local_path}")
        return local_path
    
    async def extract_text_from_pdf(self, pdf_path: str) -> str:
        """
        Extract all text from a PDF file.
        
        Args:
            pdf_path: Path to the PDF file
        
        Returns:
            Extracted text content
        """
        text_content = ""
        try:
            with open(pdf_path, "rb") as file:
                pdf_reader = PyPDF2.PdfReader(file)
                for page in pdf_reader.pages:
                    text_content += page.extract_text() + "\n"
        except Exception as e:
            logger.error(f"Error extracting text from PDF: {str(e)}")
            text_content = "Error extracting text from document."
        
        # Limit text to first ~10000 characters for GPT 4.1-mini context
        if len(text_content) > 10000:
            text_content = text_content[:10000] + "...[content truncated]"
            
        return text_content
    
    async def generate_document_metadata(self, pdf_path: str) -> DocumentAIMetadata:
        """
        Generate metadata for a document using OpenAI with Instructor.
        
        Args:
            pdf_path: Path to the PDF file
        
        Returns:
            DocumentAIMetadata with title and summary
        """
        # Extract text from PDF
        text_content = await self.extract_text_from_pdf(pdf_path)
        
        # Get the document metadata prompt
        prompt = self.prompts.document_metadata_prompt()
        
        try:
            # Generate metadata using OpenAI with Instructor
            metadata = self.openai_client.chat.completions.create(
                model="gpt-4o-mini",  # Using GPT 4.1-mini as requested
                response_model=DocumentAIMetadata,
                messages=[
                    {"role": "system", "content": prompt},
                    {"role": "user", "content": f"Document text:\n\n{text_content}"}
                ]
            )
            
            logger.info(f"Generated document metadata: {metadata.title}")
            return metadata
        except Exception as e:
            logger.error(f"Error generating document metadata: {str(e)}")
            # Return fallback metadata
            return DocumentAIMetadata(
                title="Untitled Document",
                summary="No summary available due to processing error.",
                document_type="other"
            )
    
    async def rename_supabase_file(self, supabase_url: str, new_filename: str) -> str:
        """
        Rename a file in Supabase storage while preserving the directory structure.
        
        Args:
            supabase_url: Original Supabase URL
            new_filename: New filename (without extension)
        
        Returns:
            New Supabase URL
        """
        try:
            # Get a database client using the standard function
            client = await get_db_client()
            
            # Parse the current URL to extract bucket and path
            # Example URL format: https://<project-id>.supabase.co/storage/v1/object/public/documents/<portfolio_id>/<property_id>/<filename>.pdf
            url_parts = supabase_url.split("/storage/v1/object/public/")
            if len(url_parts) != 2:
                logger.error(f"Invalid Supabase URL format: {supabase_url}")
                return supabase_url
            
            base_url = url_parts[0]
            bucket_path = url_parts[1].split("/", 1)
            if len(bucket_path) != 2:
                logger.error(f"Invalid bucket path in URL: {url_parts[1]}")
                return supabase_url
            
            bucket = bucket_path[0]
            old_path = bucket_path[1]
            
            # Parse path to preserve directory structure (portfolio_id/property_id)
            path_parts = old_path.rsplit('/', 1)
            if len(path_parts) != 2:
                logger.error(f"Unable to parse directory structure from path: {old_path}")
                return supabase_url
                
            directory = path_parts[0]  # This contains portfolio_id/property_id
            filename = path_parts[1]  # This is the actual filename
            
            # Get the file extension
            file_extension = os.path.splitext(filename)[1]  # e.g., .pdf
            
            # Generate the new path with sanitized filename
            # Replace spaces with underscores and remove special characters
            sanitized_filename = new_filename.replace(" ", "_").replace("/", "_").replace("\\", "_")
            sanitized_filename = ''.join(c for c in sanitized_filename if c.isalnum() or c in '_-.')
            new_path = f"{directory}/{sanitized_filename}{file_extension}"
            
            # Download the file
            local_path = await self.fetch_document(supabase_url)
            
            # Upload with new name
            with open(local_path, "rb") as f:
                client.storage.from_(bucket).upload(
                    new_path, 
                    f,
                    file_options={"content-type": "application/pdf"}
                )
            
            # Remove the old file
            client.storage.from_(bucket).remove([old_path])
            
            # Clean up local file
            os.remove(local_path)
            
            # Generate and return the new URL
            new_url = f"{base_url}/storage/v1/object/public/{bucket}/{new_path}"
            logger.info(f"Renamed file from {old_path} to {new_path}")
            
            return new_url
        except Exception as e:
            logger.error(f"Error renaming file in Supabase: {str(e)}")
            # Return original URL if rename fails
            return supabase_url

    async def process_document_for_rag(self, supabase_url: str, portfolio_id: str, user_id: str, document_id: str = None) -> Dict[str, Any]:
        """
        Process a document for RAG: download, parse, embed, and store in vector DB.
        
        Args:
            supabase_url: URL to the document in Supabase storage
            portfolio_id: Portfolio ID for namespace organization
            user_id: User who owns the document
            document_id: Document ID for tracking in vector storage
        
        Returns:
            Processing result details
        """
        
        # Generate a unique vector document ID for this document
        vector_doc_id = str(uuid.uuid4())
            
        # Download document
        pdf_path = await self.fetch_document(supabase_url)
        
        # Generate AI metadata
        metadata = await self.generate_document_metadata(pdf_path)
        
        # Rename file in Supabase
        new_url = await self.rename_supabase_file(supabase_url, metadata.title)
        
        # Extract just the filename from the URL path
        filename = new_url.split("/")[-1]
        
        # Process document with LangChain
        try:
            loader = PyPDFLoader(pdf_path)
            pages = loader.load()
            text_splitter = CharacterTextSplitter(chunk_size=500, chunk_overlap=100)
            chunks = text_splitter.split_documents(pages)
            texts = [chunk.page_content for chunk in chunks]
            chunk_count = len(texts)
            logger.info(f"Number of chunks: {chunk_count}")
        except Exception as e:
            logger.error(f"Error splitting document: {str(e)}")
            raise RuntimeError(f"Document splitting failed: {str(e)}")
        
        # Generate embeddings using our helper function
        try:
            embeddings = await self.generate_embeddings(
                texts=texts,
                task_type="search_document"
            )
            embedding_count = len(embeddings)
            logger.info(f"Embeddings shape: {embedding_count}")
        except Exception as e:
            logger.error(f"Error embedding document: {str(e)}")
            raise RuntimeError(f"Document embedding failed: {str(e)}")
            
        # Store vectors in TurboPuffer
        ids = [f"{vector_doc_id}_{i}" for i in range(embedding_count)]
        attributes = {
            "vector_doc_id": [vector_doc_id] * embedding_count,
            "document_id": [document_id] * embedding_count if document_id else [None] * embedding_count,
            "user_id": [user_id] * embedding_count,
            "chunk_idx": list(range(embedding_count)),
            "text": texts,
        }
        stored = self.tpuf_client.store_vectors(namespace=portfolio_id, ids=ids, vectors=embeddings, attributes=attributes)
        
        # Update document metadata in Supabase database
        try:
            # Get a database client using the standard function
            client = await get_db_client()
            
            document_metadata = {
                "title": metadata.title,
                "summary": metadata.summary,
                "document_type": metadata.document_type
            }
            
            # Find the document by URL and update metadata
            docs = client.table("documents").select("*").eq("supabase_url", supabase_url).execute()
            if docs and len(docs.data) > 0:
                # Update existing document
                doc_id = docs.data[0]["id"]
                client.table("documents").update({
                    "supabase_url": new_url,
                    "filename": filename,  # Update the filename field
                    "processed": True,
                    "metadata": document_metadata
                }).eq("id", doc_id).execute()
            else:
                # If document doesn't exist in DB yet (unlikely but possible)
                logger.warning(f"Document with URL {supabase_url} not found in database")
        except Exception as e:
            logger.error(f"Error updating document metadata in database: {str(e)}")
        
        # Clean up temporary file
        try:
            os.remove(pdf_path)
        except Exception as e:
            logger.warning(f"Could not remove temporary file: {str(e)}")
            
        return {
            "vector_doc_id": vector_doc_id,
            "portfolio_id": portfolio_id,
            "user_id": user_id,
            "title": metadata.title,
            "summary": metadata.summary,
            "document_type": metadata.document_type,
            "supabase_url": new_url,
            "filename": filename, # Add filename to return value
            "num_chunks": chunk_count,
            "embeddings_shape": embedding_count,
            "stored": stored,
        }

    async def retrieve_relevant_chunks(self, query: str, portfolio_id: str, top_k: int = 5) -> list:
        """
        Embed the query and retrieve relevant chunks from TurboPuffer using the portfolio's namespace.
        Filters out chunks from deleted documents.
        
        Args:
            query: The search query
            portfolio_id: Portfolio ID to use as the namespace
            top_k: Maximum number of results to return
            
        Returns:
            List of dicts with id, attributes, and distance (excluding deleted documents)
        """
        # Embed the query using our helper function
        try:
            query_embeddings = await self.generate_embeddings(
                texts=[query],
                task_type="search_query"
            )
            query_vector = query_embeddings[0]
        except Exception as e:
            logger.error(f"Error embedding query: {str(e)}")
            raise RuntimeError(f"Query embedding failed: {str(e)}")
            
        # Query TurboPuffer using portfolio_id as namespace
        try:
            ns = tpuf.Namespace(portfolio_id)
            results = ns.query(
                rank_by=["vector", "ANN", query_vector],
                top_k=top_k * 2,
                include_attributes=["text", "document_id"]
            )
            
            logger.info(f"Retrieved {len(results.rows)} initial results from TurboPuffer for query: '{query[:50]}...'")
            
            # Extract document IDs from results
            document_ids = []
            legacy_chunks = 0
            for row in results.rows:
                doc_id = row.attributes.get("document_id")
                if doc_id and doc_id not in document_ids:
                    document_ids.append(doc_id)
                elif doc_id is None:
                    legacy_chunks += 1
            
            logger.info(f"Found {len(document_ids)} unique document IDs and {legacy_chunks} legacy chunks without document_id")
            
            # Query database to check which documents are not deleted
            valid_document_ids = set()
            if document_ids:
                try:
                    client = await get_db_client()
                    docs = client.table("documents").select("id").in_("id", document_ids).eq("is_deleted", False).execute()
                    if docs and docs.data:
                        valid_document_ids = {str(doc["id"]) for doc in docs.data}
                    logger.info(f"Found {len(valid_document_ids)} valid (non-deleted) documents out of {len(document_ids)} total")
                except Exception as e:
                    logger.error(f"Error checking document deletion status: {str(e)}")
                    # If we can't check, return all results as fallback
                    valid_document_ids = set(document_ids)
                    logger.warning("Failed to check deletion status, including all documents as fallback")
            
            # Filter results to only include chunks from non-deleted documents
            filtered_results = []
            filtered_count = 0
            for row in results.rows:
                doc_id = row.attributes.get("document_id")
                # Include if document_id is None (legacy data) or if document is not deleted
                if doc_id is None or doc_id in valid_document_ids:
                    filtered_results.append({
                        "id": row.id, 
                        "attributes": row.attributes, 
                        "distance": row.dist
                    })
                else:
                    filtered_count += 1
                    
                # Stop when we have enough results
                if len(filtered_results) >= top_k:
                    break
            
            logger.info(f"Filtered out {filtered_count} chunks from deleted documents. Returning {len(filtered_results)} results.")
            return filtered_results
            
        except Exception as e:
            logger.error(f"Error querying TurboPuffer namespace '{portfolio_id}': {str(e)}")
            return []

    async def invalidate_portfolio_documents_cache(self, portfolio_id: str) -> None:
        """
        Invalidate both owner and guest cache entries for a portfolio's documents.
        
        Args:
            portfolio_id: Portfolio ID to invalidate cache for
        """
        try:
            # Invalidate both owner and guest cache keys
            owner_cache_key = f"portfolio_documents:{portfolio_id}:owner"
            guest_cache_key = f"portfolio_documents:{portfolio_id}:guest"
            
            await redis_cache.delete(owner_cache_key)
            await redis_cache.delete(guest_cache_key)
            
            logger.info(f"Invalidated both owner and guest cache for portfolio {portfolio_id}")
            
        except Exception as e:
            logger.warning(f"Error invalidating portfolio documents cache: {str(e)}")

    async def check_portfolio_ownership(self, portfolio_id: str, user_id: str) -> bool:
        """
        Check if a user is the owner of a portfolio.
        
        Args:
            portfolio_id: Portfolio ID to check
            user_id: User ID to check ownership for
            
        Returns:
            bool: True if user owns the portfolio, False otherwise
        """
        try:
            # Get a database client
            client = await get_db_client()
            
            # Get the portfolio's workspace_id
            portfolio = client.table("portfolios").select("workspace_id").eq("id", portfolio_id).eq("is_deleted", False).execute()
            
            if not portfolio or not portfolio.data:
                logger.warning(f"Portfolio {portfolio_id} not found or is deleted")
                return False
                
            workspace_id = portfolio.data[0]["workspace_id"]
            
            # Check if user is the workspace owner
            workspace = client.table("workspaces").select("owner_id").eq("id", workspace_id).eq("is_deleted", False).execute()
            
            if not workspace or not workspace.data:
                logger.warning(f"Workspace {workspace_id} not found or is deleted")
                return False
                
            workspace_owner_id = str(workspace.data[0]["owner_id"])
            is_owner = workspace_owner_id == user_id
            
            logger.info(f"User {user_id} ownership check for portfolio {portfolio_id}: {is_owner}")
            return is_owner
            
        except Exception as e:
            logger.error(f"Error checking portfolio ownership: {str(e)}")
            # Default to False for security
            return False

    async def get_portfolio_documents(self, portfolio_id: str, user_id: str = None) -> Dict[str, Any]:
        """
        Get all documents for a portfolio, organized in a hierarchical structure.
        
        Args:
            portfolio_id: Portfolio ID to fetch documents for
            user_id: User ID to check ownership for document visibility
            
        Returns:
            Dict with portfolio details and documents organized in folders by property and document type.
            Hidden documents (is_hidden=True) are only included if the user is the portfolio owner.
        """
        try:
            # Get a database client
            client = await get_db_client()
            
            # Check ownership if user_id is provided
            is_owner = False
            if user_id:
                is_owner = await self.check_portfolio_ownership(portfolio_id, user_id)
                logger.info(f"User {user_id} is {'owner' if is_owner else 'guest'} of portfolio {portfolio_id}")
            
            # Get portfolio information
            portfolio_name = "Portfolio"
            try:
                portfolio = client.table("portfolios").select("name").eq("id", portfolio_id).execute()
                if portfolio and portfolio.data:
                    portfolio_name = portfolio.data[0].get("name", "Portfolio")
            except Exception as e:
                logger.warning(f"Error fetching portfolio name: {str(e)}")
                # Continue with default portfolio name
            
            # 1. Fetch all valid properties in this portfolio first
            properties_list = []
            valid_property_ids = set()
            try:
                # Get properties that aren't deleted
                properties = client.table("prop").select("*").eq("portfolio_id", portfolio_id).eq("is_deleted", False).execute()
                
                if properties and properties.data:
                    for prop in properties.data:
                        prop_id = str(prop["id"])
                        valid_property_ids.add(prop_id)
                        
                        # Fetch address for property
                        address_id = prop.get("address_id")
                        property_name = f"Property {prop_id[-6:]}"  # Default name using ID suffix
                        property_address = property_name
                        
                        if address_id:
                            address = client.table("prop_addresses").select("*").eq("id", address_id).execute()
                            if address and address.data:
                                address_data = address.data[0]
                                # Create property display name from address
                                address_parts = []
                                if address_data.get("address"):
                                    address_parts.append(address_data.get("address").title())
                                if address_data.get("city"):
                                    address_parts.append(address_data.get("city").title())
                                if address_data.get("state"):
                                    address_parts.append(address_data.get("state").upper())
                                    
                                if address_parts:
                                    property_name = ", ".join(address_parts)
                                    property_address = property_name
                        
                        # Add property to list
                        properties_list.append({
                            "id": prop_id,
                            "name": property_name,
                            "address": property_address,
                            "property_obj": prop
                        })
                
                logger.info(f"Found {len(valid_property_ids)} valid properties in portfolio {portfolio_id}")
            except Exception as e:
                logger.warning(f"Error fetching properties: {str(e)}")
                valid_property_ids = set()
            
            # 2. Fetch all documents for this portfolio
            all_documents = []
            
            try:
                # Build document query - only fetch non-deleted documents
                document_query = client.table("documents").select("*").eq("portfolio_id", portfolio_id).eq("is_deleted", False)
                
                # If user is not owner, also filter out hidden documents
                if not is_owner:
                    document_query = document_query.eq("is_hidden", False)
                    logger.info("Filtering hidden documents for non-owner user")
                
                documents = document_query.execute()
                
                if documents and documents.data:
                    for doc in documents.data:
                        # Safely handle metadata which might be None
                        metadata = doc.get("metadata") or {}
                        summary = "No summary available"
                        if isinstance(metadata, dict):
                            summary = metadata.get("summary", "") or "No summary available"
                        
                        # Create standardized document entry
                        doc_entry = {
                            "id": doc["id"],
                            "type": "file",
                            "name": doc.get("filename", "Untitled Document"),
                            "document_type": doc.get("type") or "other",  # Default to "other" if no type
                            "sub_type": doc.get("sub_type") if doc.get("sub_type") else None,
                            "unit_number": doc.get("unit_number") if doc.get("unit_number") else None,
                            "is_unit": doc.get("is_unit", False) or False,
                            "is_hidden": doc.get("is_hidden", False),
                            "summary": summary,
                            "url": doc.get("supabase_url"),
                            "uploaded_at": doc.get("uploaded_at"),
                            "property_id": str(doc.get("property_id")) if doc.get("property_id") else None
                        }
                        
                        # Add to all documents list
                        all_documents.append(doc_entry)
                
                logger.info(f"Found {len(all_documents)} documents in portfolio {portfolio_id} (owner: {is_owner})")
            except Exception as e:
                logger.error(f"Error fetching documents: {str(e)}")
                raise RuntimeError(f"Failed to fetch documents: {str(e)}")
            
            # 3. Get the latest slides for this portfolio
            slides = []
            try:
                slides_data = client.table("slides_data").select("*").eq("portfolio_id", portfolio_id).order("created_at", desc=True).limit(1).execute()
                if slides_data and slides_data.data:
                    for slide in slides_data.data:
                        slides.append({
                            "id": slide["id"],
                            "type": "file",
                            "document_type": "offering_memorandum",
                            "name": slide.get("pdf_name", "Portfolio Slides"),
                            "url": slide["pdf_supabase_url"],
                            "created_at": slide["created_at"]
                        })
            except Exception as e:
                logger.warning(f"Error fetching slides: {str(e)}")
                
            # 4. Create the new folder structure
            
            # Map UI document types to backend storage types
            document_type_map = {
                "Operating & Financials": "operating_financials",
                "Purchase Agreements": "purchase_agreements", 
                "Third Party Reports": "third_party_reports",
                "Title": "title",
                "Insurance": "insurance"
            }
            
            # Root level folders array
            folders = []
            
            # Add Offering Memorandum folder
            offering_memorandum_folder = {
                "id": f"offering_memorandum_{portfolio_id}",
                "type": "folder",
                "name": "Offering Memorandum",
                "document_type": "offering_memorandum",
                "children": []
            }
            
            # Add slides to Offering Memorandum folder
            if slides:
                offering_memorandum_folder["children"] = slides
                folders.append(offering_memorandum_folder)
            
            # Add property folders
            for prop in properties_list:
                property_folder = {
                    "id": prop["id"],
                    "type": "folder",
                    "name": prop["name"],
                    "address": prop["address"],
                    "document_type": "property",
                    "children": []
                }
                
                # Create the standard folder structure for each property
                operating_financials_folder = {
                    "id": f"operating_financials_{prop['id']}",
                    "type": "folder",
                    "name": "Operating and Financials",
                    "document_type": "operating_financials",
                    "children": [
                        {
                            "id": f"financials_{prop['id']}",
                            "type": "folder",
                            "name": "Financials",
                            "document_type": "financials",
                            "children": []
                        },
                        {
                            "id": f"leases_{prop['id']}",
                            "type": "folder",
                            "name": "Leases",
                            "document_type": "leases",
                            "children": [
                                {
                                    "id": f"unit_leases_{prop['id']}",
                                    "type": "folder",
                                    "name": "Unit Leases",
                                    "document_type": "unit_leases",
                                    "children": [] # Will contain folders per unit
                                },
                                {
                                    "id": f"rent_roll_{prop['id']}",
                                    "type": "folder",
                                    "name": "Rent Roll",
                                    "document_type": "rent_roll",
                                    "children": []
                                },
                                {
                                    "id": f"other_leases_{prop['id']}",
                                    "type": "folder",
                                    "name": "Other",
                                    "document_type": "other_leases",
                                    "children": []
                                }
                            ]
                        },
                        {
                            "id": f"expenses_{prop['id']}",
                            "type": "folder",
                            "name": "Expenses",
                            "document_type": "expenses",
                            "children": [
                                {
                                    "id": f"contracts_{prop['id']}_expenses",
                                    "type": "folder",
                                    "name": "Contracts",
                                    "document_type": "contracts_expenses",
                                    "children": []
                                },
                                {
                                    "id": f"service_bills_{prop['id']}",
                                    "type": "folder",
                                    "name": "Service Bills",
                                    "document_type": "service_bills",
                                    "children": []
                                },
                                {
                                    "id": f"utility_bills_{prop['id']}",
                                    "type": "folder",
                                    "name": "Utility Bills",
                                    "document_type": "utility_bills",
                                    "children": []
                                },
                                {
                                    "id": f"other_expenses_{prop['id']}",
                                    "type": "folder",
                                    "name": "Other",
                                    "document_type": "other_expenses",
                                    "children": []
                                }
                            ]
                        },
                        {
                            "id": f"taxes_{prop['id']}",
                            "type": "folder",
                            "name": "Taxes",
                            "document_type": "taxes",
                            "children": [
                                {
                                    "id": f"general_{prop['id']}_taxes",
                                    "type": "folder",
                                    "name": "General",
                                    "document_type": "general_taxes",
                                    "children": []
                                },
                                {
                                    "id": f"notices_{prop['id']}",
                                    "type": "folder",
                                    "name": "Notices",
                                    "document_type": "notices",
                                    "children": []
                                },
                                {
                                    "id": f"tax_bills_{prop['id']}",
                                    "type": "folder",
                                    "name": "Tax Bills",
                                    "document_type": "tax_bills",
                                    "children": []
                                },
                                {
                                    "id": f"other_taxes_{prop['id']}",
                                    "type": "folder",
                                    "name": "Other",
                                    "document_type": "other_taxes",
                                    "children": []
                                }
                            ]
                        },
                        {
                            "id": f"insurance_of_{prop['id']}",
                            "type": "folder",
                            "name": "Insurance",
                            "document_type": "insurance_of",
                            "children": [
                                {
                                    "id": f"insurance_bills_{prop['id']}",
                                    "type": "folder",
                                    "name": "Insurance Bills",
                                    "document_type": "insurance_bills",
                                    "children": []
                                },
                                {
                                    "id": f"insurance_claims_{prop['id']}",
                                    "type": "folder",
                                    "name": "Insurance Claims",
                                    "document_type": "insurance_claims",
                                    "children": []
                                },
                                {
                                    "id": f"insurance_policies_{prop['id']}",
                                    "type": "folder",
                                    "name": "Insurance Policies",
                                    "document_type": "insurance_policies",
                                    "children": []
                                },
                                {
                                    "id": f"other_insurance_of_{prop['id']}",
                                    "type": "folder",
                                    "name": "Other",
                                    "document_type": "other_insurance_of",
                                    "children": []
                                }
                            ]
                        },
                        {
                            "id": f"other_of_{prop['id']}",
                            "type": "folder",
                            "name": "Other",
                            "document_type": "other_of",
                            "children": []
                        }
                    ]
                }
                
                purchase_agreements_folder = {
                    "id": f"purchase_agreements_{prop['id']}",
                    "type": "folder",
                    "name": "Purchase Agreements",
                    "document_type": "purchase_agreements",
                    "children": []
                }
                
                third_party_reports_folder = {
                    "id": f"third_party_reports_{prop['id']}",
                    "type": "folder",
                    "name": "Third Party Reports",
                    "document_type": "third_party_reports",
                    "children": [
                        {
                            "id": f"inspections_{prop['id']}",
                            "type": "folder",
                            "name": "Inspections",
                            "document_type": "inspections",
                            "children": []
                        },
                        {
                            "id": f"maintenance_{prop['id']}",
                            "type": "folder",
                            "name": "Maintenance",
                            "document_type": "maintenance",
                            "children": []
                        },
                        {
                            "id": f"environmental_{prop['id']}",
                            "type": "folder",
                            "name": "Environmental",
                            "document_type": "environmental",
                            "children": []
                        },
                        {
                            "id": f"other_tpr_{prop['id']}",
                            "type": "folder",
                            "name": "Other",
                            "document_type": "other_tpr",
                            "children": []
                        }
                    ]
                }
                
                title_folder = {
                    "id": f"title_{prop['id']}",
                    "type": "folder",
                    "name": "Title",
                    "document_type": "title",
                    "children": [
                        {
                            "id": f"deed_{prop['id']}",
                            "type": "folder",
                            "name": "Deed",
                            "document_type": "deed",
                            "children": []
                        },
                        {
                            "id": f"survey_{prop['id']}",
                            "type": "folder",
                            "name": "Survey",
                            "document_type": "survey",
                            "children": []
                        },
                        {
                            "id": f"other_title_{prop['id']}",
                            "type": "folder",
                            "name": "Other",
                            "document_type": "other_title",
                            "children": []
                        }
                    ]
                }
                
                insurance_folder = {
                    "id": f"insurance_{prop['id']}",
                    "type": "folder",
                    "name": "Insurance",
                    "document_type": "insurance",
                    "children": [
                        {
                            "id": f"contracts_{prop['id']}_insurance",
                            "type": "folder",
                            "name": "Contracts",
                            "document_type": "contracts_insurance",
                            "children": []
                        },
                        {
                            "id": f"claims_{prop['id']}",
                            "type": "folder",
                            "name": "Claims",
                            "document_type": "claims",
                            "children": []
                        },
                        {
                            "id": f"other_insurance_{prop['id']}",
                            "type": "folder",
                            "name": "Other",
                            "document_type": "other_insurance",
                            "children": []
                        }
                    ]
                }
                
                # Add the standard folders to the property folder
                property_folder["children"] = [
                    operating_financials_folder,
                    purchase_agreements_folder,
                    third_party_reports_folder,
                    title_folder,
                    insurance_folder
                ]
                
                # Now we'll populate these folders with documents
                property_documents = [doc for doc in all_documents if doc.get("property_id") == prop["id"]]
                
                # Place documents in appropriate folders based on type and subtype
                for doc in property_documents:
                    doc_type = doc.get("document_type")
                    sub_type = doc.get("sub_type")
                    is_unit = doc.get("is_unit", False)
                    unit_number = doc.get("unit_number")
                    
                    # Parse the combined sub_type format: "MainType - MiniType"
                    main_sub_type = None
                    mini_sub_type = None
                    
                    if sub_type and " - " in sub_type:
                        parts = sub_type.split(" - ", 1)
                        main_sub_type = parts[0].strip()
                        mini_sub_type = parts[1].strip() if len(parts) > 1 else None
                    else:
                        main_sub_type = sub_type
                    
                    # Map from backend type to UI type if needed
                    ui_doc_type = next((k for k, v in document_type_map.items() if v == doc_type), doc_type)
                    
                    target_folder = None
                    
                    # Special handling for unit leases based on parsed sub_type
                    if (ui_doc_type == "Operating & Financials" and 
                        ((main_sub_type == "Leases" and mini_sub_type == "Unit Leases") or 
                         (is_unit and main_sub_type == "Leases"))):
                        
                        # Find the Unit Leases folder
                        unit_leases_folder = None
                        for folder in property_folder["children"]:
                            if folder["document_type"] == "operating_financials":
                                for subfolder in folder["children"]:
                                    if subfolder["document_type"] == "leases":
                                        for mini_folder in subfolder["children"]:
                                            if mini_folder["document_type"] == "unit_leases":
                                                unit_leases_folder = mini_folder
                                                break
                                        break
                                break
                        
                        if unit_leases_folder:
                            # Use unit number if available, otherwise create a generic unit folder
                            unit_id = unit_number if unit_number else "general"
                            
                            # Check if we already have a folder for this unit
                            unit_folder = None
                            for folder in unit_leases_folder["children"]:
                                if folder["document_type"] == f"unit_{unit_id}":
                                    unit_folder = folder
                                    break
                            
                            # If not, create a new folder for this unit
                            if not unit_folder:
                                unit_folder = {
                                    "id": f"unit_{unit_id}_{prop['id']}",
                                    "type": "folder",
                                    "name": f"Unit {unit_id}" if unit_id != "general" else "General Units",
                                    "document_type": f"unit_{unit_id}",
                                    "children": []
                                }
                                unit_leases_folder["children"].append(unit_folder)
                            
                            # Use this unit folder as target
                            target_folder = unit_folder
                    
                    # Handle rent roll documents based on parsed sub_type
                    elif (ui_doc_type == "Operating & Financials" and 
                          ((main_sub_type == "Leases" and mini_sub_type == "Rent Roll") or 
                           (not is_unit and main_sub_type == "Leases" and mini_sub_type == "Rent Roll"))):
                        
                        # Find the Rent Roll folder
                        for folder in property_folder["children"]:
                            if folder["document_type"] == "operating_financials":
                                for subfolder in folder["children"]:
                                    if subfolder["document_type"] == "leases":
                                        for rent_roll_folder in subfolder["children"]:
                                            if rent_roll_folder["document_type"] == "rent_roll":
                                                target_folder = rent_roll_folder
                                                break
                                        break
                                break
                    
                    # Handle Operating & Financials with parsed sub_type
                    elif ui_doc_type == "Operating & Financials" or doc_type == "operating_financials":
                        op_fin_folder = None
                        for folder in property_folder["children"]:
                            if folder["document_type"] == "operating_financials":
                                op_fin_folder = folder
                                break
                        
                        if op_fin_folder:
                            # Handle main sub_type (Financials, Leases, Expenses, Taxes, Insurance)
                            if main_sub_type in ["Financials", "Leases", "Expenses", "Taxes", "Insurance"]:
                                sub_folder = None
                                for sf in op_fin_folder["children"]:
                                    if sf["name"].lower() == main_sub_type.lower():
                                        sub_folder = sf
                                        break
                                
                                if sub_folder:
                                    # If we have a mini_sub_type, find the appropriate mini-folder
                                    if mini_sub_type:
                                        # For Leases mini-types
                                        if main_sub_type == "Leases" and mini_sub_type in ["Other"]:
                                            for mini_folder in sub_folder["children"]:
                                                if mini_folder["name"] == mini_sub_type:
                                                    target_folder = mini_folder
                                                    break
                                        # For Expenses mini-types
                                        elif main_sub_type == "Expenses" and mini_sub_type in ["Contracts", "Service Bills", "Utility Bills", "Other"]:
                                            for mini_folder in sub_folder["children"]:
                                                if mini_folder["name"] == mini_sub_type:
                                                    target_folder = mini_folder
                                                    break
                                        # For Taxes mini-types
                                        elif main_sub_type == "Taxes" and mini_sub_type in ["General", "Notices", "Tax Bills", "Other"]:
                                            for mini_folder in sub_folder["children"]:
                                                if mini_folder["name"] == mini_sub_type:
                                                    target_folder = mini_folder
                                                    break
                                        # For Insurance mini-types
                                        elif main_sub_type == "Insurance" and mini_sub_type in ["Insurance Bills", "Insurance Claims", "Insurance Policies", "Other"]:
                                            for mini_folder in sub_folder["children"]:
                                                if mini_folder["name"] == mini_sub_type:
                                                    target_folder = mini_folder
                                                    break
                                        # Default case: use the sub-folder itself
                                        else:
                                            target_folder = sub_folder
                                    else:
                                        # No mini-type, use the sub-folder
                                        target_folder = sub_folder
                            else:
                                # If no recognized main_sub_type, use the Other folder
                                for sf in op_fin_folder["children"]:
                                    if sf["document_type"] == "other_of":
                                        target_folder = sf
                                        break
                            
                            # If still no target_folder, default to op_fin_folder
                            if not target_folder:
                                target_folder = op_fin_folder
                    
                    # Handle Title documents with parsed sub_type
                    elif ui_doc_type == "Title" or doc_type == "title":
                        title_folder = None
                        for folder in property_folder["children"]:
                            if folder["document_type"] == "title":
                                title_folder = folder
                                break
                        
                        if title_folder:
                            # Find appropriate subfolder based on subtype
                            if main_sub_type in ["Deed", "Survey", "Other"]:
                                for sf in title_folder["children"]:
                                    if sf["name"].lower() == main_sub_type.lower():
                                        target_folder = sf
                                        break
                            else:
                                # Default to "Other" subfolder
                                for sf in title_folder["children"]:
                                    if sf["document_type"] == "other_title":
                                        target_folder = sf
                                        break
                            
                            # If still no target_folder, default to title_folder
                            if not target_folder:
                                target_folder = title_folder
                    
                    # Handle Third Party Reports with parsed sub_type
                    elif ui_doc_type == "Third Party Reports" or doc_type == "third_party_reports":
                        tpr_folder = None
                        for folder in property_folder["children"]:
                            if folder["document_type"] == "third_party_reports":
                                tpr_folder = folder
                                break
                        
                        if tpr_folder:
                            # Find appropriate subfolder based on subtype
                            if main_sub_type in ["Inspections", "Maintenance", "Environmental", "Other"]:
                                for sf in tpr_folder["children"]:
                                    if sf["name"].lower() == main_sub_type.lower():
                                        target_folder = sf
                                        break
                            else:
                                # Default to "Other" subfolder
                                for sf in tpr_folder["children"]:
                                    if sf["document_type"] == "other_tpr":
                                        target_folder = sf
                                        break
                            
                            # If still no target_folder, default to tpr_folder
                            if not target_folder:
                                target_folder = tpr_folder
                    
                    # Handle Insurance documents with parsed sub_type
                    elif ui_doc_type == "Insurance" or doc_type == "insurance":
                        insurance_folder = None
                        for folder in property_folder["children"]:
                            if folder["document_type"] == "insurance":
                                insurance_folder = folder
                                break
                        
                        if insurance_folder:
                            # Find appropriate subfolder based on subtype
                            if main_sub_type in ["Contracts", "Claims", "Other"]:
                                for sf in insurance_folder["children"]:
                                    if sf["name"].lower() == main_sub_type.lower():
                                        target_folder = sf
                                        break
                            else:
                                # Default to "Other" subfolder
                                for sf in insurance_folder["children"]:
                                    if sf["document_type"] == "other_insurance":
                                        target_folder = sf
                                        break
                            
                            # If still no target_folder, default to insurance_folder
                            if not target_folder:
                                target_folder = insurance_folder
                    
                    # Handle Purchase Agreements (no subtypes)
                    elif ui_doc_type == "Purchase Agreements" or doc_type == "purchase_agreements":
                        for folder in property_folder["children"]:
                            if folder["document_type"] == "purchase_agreements":
                                target_folder = folder
                                break
                    
                    # If we found a target folder, add the document to it
                    if target_folder:
                        target_folder["children"].append(doc)
                    else:
                        # Last resort - add to the property root
                        property_folder["children"].append(doc)
                
                # Define recursive function to filter out empty folders
                def filter_empty_folders(folder):
                    if folder["type"] == "file":
                        return True
                    
                    # First recursively process all children
                    if "children" in folder:
                        # Filter out empty children
                        filtered_children = [child for child in folder["children"] if filter_empty_folders(child)]
                        folder["children"] = filtered_children
                        
                        # Return True if this folder has files (directly or in subdirectories)
                        return len(folder["children"]) > 0
                    
                    return False
                
                # Filter the property folder structure to remove empty folders
                if filter_empty_folders(property_folder):
                    folders.append(property_folder)
            
            # 5. Create the final data structure
            data_room = {
                "success": True,
                "type": "root",
                "portfolio_id": portfolio_id,
                "portfolio_name": portfolio_name,
                "children": sorted(folders, key=lambda x: "0" if x["name"] == "Offering Memorandum" else x["name"])
            }
            
            return data_room
            
        except Exception as e:
            logger.error(f"Error getting portfolio documents: {str(e)}")
            logger.error(traceback.format_exc())
            raise RuntimeError(f"Failed to get portfolio documents: {str(e)}")

    async def generate_portfolio_slides(self, portfolio_id: str) -> Dict[str, Any]:
        """
        Generate HTML for PDF slides based on all documents in a portfolio.
        
        Args:
            portfolio_id: Portfolio ID to fetch documents from
        
        Returns:
            Dict with HTML for slides and processing metadata
        """
        try:
            # Get a database client using the standard function
            client = await get_db_client()
            
            # Portfolio information can be minimal if table doesn't exist
            portfolio_name = "Real Estate Portfolio"
            try:
                # Fetch portfolio information
                portfolio = client.table("portfolios").select("*").eq("id", portfolio_id).execute()
                if portfolio and portfolio.data:
                    portfolio_data = portfolio.data[0]
                    portfolio_name = portfolio_data.get("name", portfolio_name)
            except Exception as e:
                logger.warning(f"Error fetching portfolio information: {str(e)}")
                logger.warning("Continuing with default portfolio name")
            
            # Fetch all properties in the portfolio
            try:
                properties = client.table("prop").select("*").eq("portfolio_id", portfolio_id).execute()
                
                if not properties or not properties.data:
                    logger.warning(f"No properties found for portfolio {portfolio_id}")
                    return {
                        "html": f'<div class="slide"><h1>{portfolio_name}</h1><div class="slide-content"><p>No property data available for this portfolio.</p></div></div>',
                        "document_count": 0,
                        "property_count": 0
                    }
                
                property_ids = [prop["id"] for prop in properties.data]
                property_count = len(property_ids)
            except Exception as e:
                logger.warning(f"Error fetching properties: {str(e)}")
                # Check if we can still proceed with documents directly
                property_ids = []
                property_count = 0
            
            # Initialize property data structure with all property info
            property_data_map = {}
            
            if property_count > 0:
                for prop in properties.data:
                    property_id = prop["id"]
                    property_data_map[property_id] = {
                        "property_info": prop,
                        "documents": [],
                        "market_data": None,
                        "financials": None,
                        "address": None
                    }
                    
                    # Fetch address for each property
                    try:
                        address = client.table("prop_addresses").select("*").eq("id", prop["address_id"]).execute()
                        if address and address.data:
                            property_data_map[property_id]["address"] = address.data[0]
                    except Exception as e:
                        logger.warning(f"Error fetching address for property {property_id}: {str(e)}")
                    
                    # Fetch market data for each property
                    try:
                        market_data = client.table("prop_market_data").select("*").eq("prop_id", property_id).execute()
                        if market_data and market_data.data:
                            property_data_map[property_id]["market_data"] = market_data.data[0]
                    except Exception as e:
                        logger.warning(f"Error fetching market data for property {property_id}: {str(e)}")
                    
                    # Fetch financial data for each property
                    try:
                        financials = client.table("prop_financials").select("*").eq("prop_id", property_id).execute()
                        if financials and financials.data:
                            property_data_map[property_id]["financials"] = financials.data[0]
                    except Exception as e:
                        logger.warning(f"Error fetching financials for property {property_id}: {str(e)}")
            
            # Fetch all documents for all properties in the portfolio
            # Exclude hidden documents from slide generation as slides are typically meant to be shared
            document_count = 0
            try:
                portfolio_docs = client.table("documents").select("*").eq("portfolio_id", portfolio_id).eq("is_hidden", False).execute()
                if portfolio_docs and portfolio_docs.data:
                    document_count = len(portfolio_docs.data)
                    # Create a special "portfolio" container if no properties exist
                    if not property_data_map:
                        property_data_map["portfolio"] = {
                            "property_info": {"name": portfolio_name},
                            "documents": portfolio_docs.data,
                            "address": None,
                            "market_data": None,
                            "financials": None
                        }
                    else:
                        # Distribute documents to their properties if possible
                        for doc in portfolio_docs.data:
                            prop_id = doc.get("property_id")
                            if prop_id and prop_id in property_data_map:
                                property_data_map[prop_id]["documents"].append(doc)
                            else:
                                # If no property match, add to first property as fallback
                                first_prop_id = next(iter(property_data_map.keys()))
                                property_data_map[first_prop_id]["documents"].append(doc)
            except Exception as e:
                logger.warning(f"Error fetching documents for portfolio {portfolio_id}: {str(e)}")
            
            if document_count == 0 and property_count == 0:
                logger.warning(f"No documents or properties found for portfolio {portfolio_id}")
                return {
                    "html": f'<div class="slide"><h1>{portfolio_name}</h1><div class="slide-content"><p>No documents or property data available for this portfolio.</p></div></div>',
                    "document_count": 0,
                    "property_count": 0
                }
            
            # Prepare document contents for processing
            all_document_contents = []
            
            # Process documents to extract text
            for property_id, prop_data in property_data_map.items():
                property_name = prop_data["property_info"].get("name", "Unnamed Property")
                property_address = "Unknown Address"
                
                if prop_data["address"]:
                    address_parts = []
                    addr = prop_data["address"]
                    if addr.get("street"):
                        address_parts.append(addr.get("street"))
                    if addr.get("city"):
                        address_parts.append(addr.get("city"))
                    if addr.get("state"):
                        address_parts.append(addr.get("state"))
                    if addr.get("zip"):
                        address_parts.append(addr.get("zip"))
                    property_address = ", ".join(address_parts)
                
                for doc in prop_data["documents"]:
                    try:
                        # Download document
                        pdf_path = await self.fetch_document(doc["supabase_url"])
                        
                        # Extract text
                        text_content = await self.extract_text_from_pdf(pdf_path)
                        
                        # Add to document contents with context
                        all_document_contents.append({
                            "title": doc.get("filename", "Untitled Document"),
                            "type": doc.get("type", "other"),
                            "property_id": property_id,
                            "property_name": property_name,
                            "property_address": property_address,
                            "text": text_content[:3000],  # Limit text to avoid context issues
                            "metadata": doc.get("metadata", {})
                        })
                        
                        # Clean up
                        try:
                            os.remove(pdf_path)
                        except Exception as e:
                            logger.warning(f"Could not remove temporary file: {str(e)}")
                    except Exception as e:
                        logger.error(f"Error processing document {doc.get('id')}: {str(e)}")
                        # Add error info to document contents
                        all_document_contents.append({
                            "title": doc.get("filename", "Untitled Document"),
                            "type": "error",
                            "property_id": property_id,
                            "property_name": property_name,
                            "property_address": property_address,
                            "error": str(e)
                        })
            
            # Prepare prompt context
            context_data = {
                "portfolio_id": portfolio_id,
                "portfolio_name": portfolio_name,
                "property_count": property_count,
                "document_count": document_count,
                "properties": [
                    {
                        "id": prop_id,
                        "info": data["property_info"],
                        "address": data["address"],
                        "market_data": data["market_data"],
                        "financials": data["financials"],
                        "document_count": len(data["documents"]),
                        "document_types": list(set(doc.get("type", "other") for doc in data["documents"]))
                    }
                    for prop_id, data in property_data_map.items()
                ],
                "documents": all_document_contents
            }
            
            # Get prompt for slide generation
            prompt = self.prompts.slides_html_prompt()
            
            # Custom instructions for HTML formatting
            additional_instructions = """
            CRITICAL FORMATTING REQUIREMENTS:
            1. You MUST use valid HTML5 for the entire content
            2. Each slide MUST be in its own separate <div class="slide"> element
            3. Each slide MUST start with a <h1> heading for the title
            4. After the heading, content must be in <div class="slide-content"> and </div> tags
            5. NEVER output raw text outside of proper HTML elements or slide divs
            6. ALWAYS use proper HTML tags (<ul>, <li>, <p>, <strong>, etc.)
            7. Do NOT use Markdown syntax at all (*, #, -, etc.)
            8. Each major section (Overview, Properties, Documents, etc.) MUST be on its own slide
            9. NEVER put the word "html" at the top of your response or outside slide divs
            
            SLIDE STRUCTURE:
            <div class="slide">
              <h1>Title Goes Here</h1>
              <div class="slide-content">
                <!-- Content goes here, using proper HTML -->
              </div>
            </div>
            
            PROPERTY DETAILS FORMATTING (CRITICAL):
            For property detail slides, ALWAYS use this exact structure:
            <div class="slide">
              <h1>Property Details: [Property Name]</h1>
              <div class="slide-content property-details">
                <!-- Basic property info in two columns - KEEP VERY BRIEF -->
                <div class="columns">
                  <div class="column">
                    <ul>
                      <li><strong>Location:</strong> Short Address</li>
                      <li><strong>City:</strong> City</li>
                      <li><strong>State:</strong> State</li>
                    </ul>
                  </div>
                  <div class="column">
                    <ul>
                      <li><strong>Property Type:</strong> Type</li>
                      <li><strong>Purchase Price:</strong> Price</li>
                      <li><strong>Size:</strong> Size</li>
                    </ul>
                  </div>
                </div>
                
                <!-- Summary (2-3 sentences MAXIMUM) -->
                <h2>Summary</h2>
                <p>Very brief property summary here</p>
                
                <!-- ALWAYS use property-pros-cons for Pros/Cons -->
                <div class="property-pros-cons">
                  <div class="property-pros">
                    <h3>Pros</h3>
                    <ul>
                      <li>Short pro point (5-7 words max!)</li>
                      <li>Another pro (very short!)</li>
                      <li>Third pro (keep brief!)</li>
                    </ul>
                  </div>
                  <div class="property-cons">
                    <h3>Cons</h3>
                    <ul>
                      <li>Short con point (5-7 words max!)</li>
                      <li>Another con (very short!)</li>
                      <li>Third con (keep brief!)</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
            
            The above format with proper div separation is CRITICAL for page breaks to work correctly.
            NEVER use long sentences in bullet points - keep them to 5-8 words MAXIMUM.
            """
            
            # Generate HTML using OpenAI
            response = self.openai_client.chat.completions.create(
                model="gpt-4o",
                messages=[
                    {"role": "system", "content": prompt},
                    {"role": "user", "content": f"Generate PDF-compatible HTML slides for this portfolio data, following the format guidelines exactly:\n\n{json.dumps(context_data, default=str)}"},
                    {"role": "user", "content": additional_instructions}
                ],
                temperature=0.2  # Lower temperature for more structured, consistent output
            )
            
            html = response.choices[0].message.content
            
            # Post-process to ensure proper formatting
            # Remove any raw text or code blocks
            html = re.sub(r'```html|```', '', html)
            
            # Strip any raw text at the beginning (e.g. "html")
            html = re.sub(r'^(.*?)(?=<div class="slide">)', '', html, flags=re.DOTALL)
            
            # Fix nested slide divs
            html = re.sub(r'<div class="slide">\s*<div class="slide">', '<div class="slide">', html)
            
            # Ensure all content is wrapped in slide divs
            if not html.strip().startswith('<div class="slide">'):
                html = f'<div class="slide">\n<h1>{portfolio_name}</h1>\n<div class="slide-content">\n{html}\n</div>\n</div>'
            
            # Ensure each slide has proper div structure for its content
            slide_parts = re.split(r'(<div class="slide">|</div>\s*<div class="slide">)', html)
            for i, part in enumerate(slide_parts):
                if part.startswith('<div class="slide">') and i+1 < len(slide_parts):
                    content = slide_parts[i+1]
                    if '<h1>' in content and '<div class="slide-content">' not in content:
                        # Add slide-content div after h1 tag
                        modified_content = re.sub(r'(<h1>.*?</h1>)(?!\s*<div class="slide-content">)',
                                               r'\1\n<div class="slide-content">', content)
                        # Close div at end if needed
                        if not modified_content.strip().endswith('</div>'):
                            modified_content += '\n</div>'
                        slide_parts[i+1] = modified_content
            
            # Rejoin parts
            html = ''.join(slide_parts)
            
            # Ensure each slide has an explicit page break
            html = html.replace('</div>\n<div class="slide">', 
                               '</div>\n<div style="page-break-after: always;"></div>\n<div class="slide">')
            
            logger.info(f"Generated HTML with {len(html)} characters")
            
            return {
                "html": html,
                "document_count": document_count,
                "property_count": property_count
            }
        except Exception as e:
            logger.error(f"Error generating slides HTML: {str(e)}")
            logger.error(traceback.format_exc())
            return {
                "html": f'<div class="slide"><h1>Error</h1><div class="slide-content"><p>Failed to generate slides: {str(e)}</p></div></div>',
                "document_count": 0,
                "property_count": 0
            }

    async def upload_pdf_to_supabase(self, pdf_path: str, portfolio_id: str) -> Dict[str, Any]:
        """
        Upload a PDF file to Supabase storage in the 'slides' bucket.
        
        Args:
            pdf_path: Local path to the PDF file
            portfolio_id: Portfolio ID for organizing files
            
        Returns:
            Dict with Supabase storage information
        """
        max_retries = 3
        retry_count = 0
        
        # Validate inputs
        if not pdf_path or not os.path.exists(pdf_path):
            raise FileNotFoundError(f"PDF file not found at path: {pdf_path}")
            
        if not portfolio_id:
            raise ValueError("Portfolio ID is required")
            
        # Validate file size and type
        if not pdf_path.lower().endswith('.pdf'):
            raise ValueError(f"File must be a PDF: {pdf_path}")
            
        file_size = os.path.getsize(pdf_path) / (1024 * 1024)  # Size in MB
        if file_size > 10:  # 10MB limit
            raise ValueError(f"PDF file size exceeds limit (10MB): {file_size:.2f}MB")
            
        try:
            # Get a database client using the standard function
            client = await get_db_client()
            
            # Extract filename from path
            filename = os.path.basename(pdf_path)
            
            # Create a unique filename to avoid conflicts
            timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
            unique_filename = f"{os.path.splitext(filename)[0]}_{timestamp}.pdf"
            
            # Create a storage path with portfolio ID for organization
            storage_path = f"{portfolio_id}/{unique_filename}"
            
            logger.info(f"Uploading PDF ({file_size:.2f}MB) to Supabase: {storage_path}")
            
            # Implement retry logic for transient errors
            while retry_count < max_retries:
                try:
                    # Upload the file to 'slides' bucket
                    with open(pdf_path, "rb") as f:
                        client.storage.from_("slides").upload(
                            storage_path, 
                            f,
                            file_options={"content-type": "application/pdf"}
                        )
                    break  # Success, exit retry loop
                except Exception as upload_error:
                    retry_count += 1
                    if retry_count >= max_retries:
                        raise upload_error
                    logger.warning(f"Upload attempt {retry_count} failed: {str(upload_error)}. Retrying...")
                    await asyncio.sleep(1)  # Wait before retry
            
            # Get the public URL
            public_url = client.storage.from_("slides").get_public_url(storage_path)
            
            logger.info(f"Successfully uploaded PDF to Supabase: {public_url}")
            return {
                "supabase_path": storage_path,
                "supabase_url": public_url,
                "filename": unique_filename
            }
            
        except Exception as e:
            logger.error(f"Error uploading PDF to Supabase: {str(e)}")
            logger.error(traceback.format_exc())
            raise RuntimeError(f"Failed to upload PDF to Supabase: {str(e)}")
    
    async def store_slides_metadata(self, portfolio_id: str, pdf_data: Dict[str, Any], html: str) -> int:
        """
        Store slides metadata in the slides_data table.
        
        Args:
            portfolio_id: Portfolio ID
            pdf_data: PDF upload data from Supabase
            html: Raw HTML content
            
        Returns:
            ID of the inserted record
        """
        try:
            # Get a database client using the standard function
            client = await get_db_client()
            
            # Prepare record data
            slides_data = {
                "portfolio_id": portfolio_id,
                "pdf_supabase_url": pdf_data["supabase_url"],
                "pdf_supabase_path": pdf_data["supabase_path"],
                "pdf_name": pdf_data["filename"],
                "pdf_url": pdf_data["supabase_url"],
                "raw_html": html,
                # created_at is added automatically by Supabase
            }
            
            # Insert record
            result = client.table("slides_data").insert(slides_data).execute()
            
            if result and result.data:
                # Return ID of the inserted record
                return result.data[0]["id"]
            else:
                raise RuntimeError("Failed to get ID of inserted record")
                
        except Exception as e:
            logger.error(f"Error storing slides metadata: {str(e)}")
            logger.error(traceback.format_exc())
            raise RuntimeError(f"Failed to store slides metadata: {str(e)}")
    
    async def convert_html_to_pdf(self, html_content: str, title: str = None, portfolio_id: str = None) -> str:
        """
        Convert HTML content to PDF using WeasyPrint.
        
        Args:
            html_content: The HTML content to convert
            title: Optional title for the PDF document
            portfolio_id: Optional portfolio ID for additional context
            
        Returns:
            Path to the generated PDF file
        """
        try:
            # Log the start of conversion process
            logger.info(f"Starting HTML to PDF conversion for: {title or 'Untitled Document'}")
            
            # Validate HTML content
            if not html_content or not html_content.strip():
                raise ValueError("Empty HTML content provided")
            
            # Append a final slide if not already present
            if "final-slide" not in html_content:
                final_slide = """
                <div class="final-slide">
                <div class="final-title">Relm Portfolios</div>
                <div class="final-subtitle">AI-Powered Real Estate Portfolio Analysis</div>
                """
                
                if portfolio_id:
                    # Attempt to get portfolio name
                    try:
                        # Get a database client using the standard function
                        client = await get_db_client()
                        portfolio = client.table("portfolios").select("name").eq("id", portfolio_id).execute()
                        if portfolio and portfolio.data:
                            portfolio_name = portfolio.data[0].get("name", "Portfolio")
                            final_slide += f'<div class="final-portfolio">Portfolio: {portfolio_name}</div>\n'
                    except Exception as e:
                        logger.warning(f"Error fetching portfolio name: {str(e)}")
                
                final_slide += """
                <div class="final-website">relm-intel.vercel.app</div>
                </div>
                """
                
                html_content += final_slide
            
            # Use our custom WeasyPrint-based converter
            pdf_file = html_to_pdf.convert_html_to_pdf(
                html_content=html_content,
                title=title,
                include_cover=True,
                subtitle="Portfolio Analysis"
            )
            
            # Verify the file was created successfully
            if not os.path.exists(pdf_file):
                raise FileNotFoundError(f"PDF file was not created at expected path: {pdf_file}")
                
            # Get file size for logging
            file_size = os.path.getsize(pdf_file) / 1024  # Size in KB
            
            logger.info(f"Successfully converted HTML to PDF: {pdf_file} ({file_size:.2f} KB)")
            return pdf_file
            
        except Exception as e:
            logger.error(f"Error converting HTML to PDF: {str(e)}")
            logger.error(traceback.format_exc())
            raise RuntimeError(f"Failed to convert HTML to PDF: {str(e)}")

    async def generate_and_store_pdf_slides(self, portfolio_id: str) -> SlidesResponse:
        """
        Generate HTML slides, convert to PDF, and store in Supabase.
        
        Args:
            portfolio_id: Portfolio ID to generate slides for
            
        Returns:
            SlidesResponse with PDF URL and metadata
        """
        pdf_path = None
        
        try:
            # First generate the HTML content
            logger.info(f"Generating slides for portfolio {portfolio_id}")
            result = await self.generate_portfolio_slides(portfolio_id)
            
            # Extract portfolio name for title
            portfolio_name = "Portfolio Slides"
            try:
                # Get a database client using the standard function
                client = await get_db_client()
                
                # Fetch portfolio information
                portfolio = client.table("portfolios").select("*").eq("id", portfolio_id).execute()
                if portfolio and portfolio.data:
                    portfolio_data = portfolio.data[0]
                    portfolio_name = portfolio_data.get("name", portfolio_name)
                    logger.info(f"Using portfolio name for slides: {portfolio_name}")
            except Exception as e:
                logger.warning(f"Error fetching portfolio name: {str(e)}")
            
            # Convert HTML to PDF, passing portfolio and user info
            pdf_path = await self.convert_html_to_pdf(
                html_content=result["html"], 
                title=f"{portfolio_name} Slides", 
                portfolio_id=portfolio_id
            )
            
            # Upload PDF to Supabase
            pdf_data = await self.upload_pdf_to_supabase(pdf_path, portfolio_id)
            
            # Store metadata in slides_data table
            slides_data_id = await self.store_slides_metadata(portfolio_id, pdf_data, result["html"])
            
            # Return response (will clean up in finally block)
            return SlidesResponse(
                success=True,
                pdf_url=pdf_data["supabase_url"],
                pdf_name=pdf_data["filename"],
                document_count=result["document_count"],
                property_count=result["property_count"],
                slides_data_id=slides_data_id,
                portfolio_id=portfolio_id
            )
            
        except Exception as e:
            logger.error(f"Error generating and storing PDF slides: {str(e)}")
            logger.error(traceback.format_exc())
            raise RuntimeError(f"Failed to generate and store PDF slides: {str(e)}")
            
        finally:
            # Clean up temporary PDF file
            if pdf_path and os.path.exists(pdf_path):
                try:
                    os.remove(pdf_path)
                    logger.info(f"Cleaned up temporary PDF file: {pdf_path}")
                    
                    # Try to remove the parent directory if it's empty
                    parent_dir = os.path.dirname(pdf_path)
                    if os.path.exists(parent_dir) and not os.listdir(parent_dir):
                        os.rmdir(parent_dir)
                        logger.info(f"Removed empty temporary directory: {parent_dir}")
                except Exception as cleanup_error:
                    logger.warning(f"Error cleaning up temporary files: {str(cleanup_error)}")
                    # Continue execution - cleanup failure shouldn't affect the overall result

    async def toggle_document_hidden(self, portfolio_id: str, document_id: str) -> Dict[str, Any]:
        """
        Toggle the is_hidden field for a document.
        
        Args:
            portfolio_id: Portfolio ID of the document (for validation)
            document_id: Document ID to toggle visibility for
            
        Returns:
            Dict with success status, new is_hidden value, and message
        """
        try:
            # Get a database client
            client = await get_db_client()
            
            # Verify document exists and belongs to the specified portfolio
            document = client.table("documents").select("id,portfolio_id,is_hidden").eq("id", document_id).execute()
            
            if not document or not document.data:
                logger.warning(f"Document {document_id} not found")
                return {
                    "success": False,
                    "error": f"Document {document_id} not found"
                }
                
            doc_data = document.data[0]
            if str(doc_data.get("portfolio_id")) != portfolio_id:
                logger.warning(f"Document {document_id} does not belong to portfolio {portfolio_id}")
                return {
                    "success": False,
                    "error": f"Document {document_id} does not belong to portfolio {portfolio_id}"
                }
            
            # Get current is_hidden value and toggle it
            current_hidden = doc_data.get("is_hidden", False)
            new_hidden = not current_hidden
            
            # Update the document with toggled is_hidden value
            result = client.table("documents").update({"is_hidden": new_hidden}).eq("id", document_id).execute()
            
            if result and result.data:
                action = "hidden" if new_hidden else "visible"
                logger.info(f"Document {document_id} toggled to {action} successfully")
                
                # Invalidate portfolio documents cache (both owner and guest)
                try:
                    await self.invalidate_portfolio_documents_cache(portfolio_id)
                    logger.info(f"Invalidated portfolio documents cache for portfolio {portfolio_id}")
                except Exception as cache_e:
                    logger.warning(f"Error invalidating cache: {str(cache_e)}")
                
                return {
                    "success": True,
                    "is_hidden": new_hidden,
                    "message": f"Document {document_id} is now {action}"
                }
            else:
                logger.error(f"Failed to toggle visibility for document {document_id}")
                return {
                    "success": False,
                    "error": f"Failed to update document {document_id}"
                }
                
        except Exception as e:
            logger.error(f"Error toggling document visibility {document_id}: {str(e)}")
            logger.error(traceback.format_exc())
            return {
                "success": False,
                "error": f"Error toggling document visibility: {str(e)}"
            }

    async def delete_document(self, portfolio_id: str, document_id: str) -> bool:
        """
        Soft delete a document by marking it as deleted in the database.
        
        Args:
            portfolio_id: Portfolio ID of the document (for validation)
            document_id: Document ID to delete
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Get a database client
            client = await get_db_client()
            
            # Verify document exists and belongs to the specified portfolio
            document = client.table("documents").select("id,portfolio_id").eq("id", document_id).execute()
            
            if not document or not document.data:
                logger.warning(f"Document {document_id} not found")
                return False
                
            doc_data = document.data[0]
            if str(doc_data.get("portfolio_id")) != portfolio_id:
                logger.warning(f"Document {document_id} does not belong to portfolio {portfolio_id}")
                return False
            
            # Mark document as deleted (soft delete)
            result = client.table("documents").update({"is_deleted": True}).eq("id", document_id).execute()
            
            if result and result.data:
                logger.info(f"Document {document_id} soft deleted successfully")
                
                # Invalidate portfolio documents cache (both owner and guest)
                try:
                    await self.invalidate_portfolio_documents_cache(portfolio_id)
                    logger.info(f"Invalidated portfolio documents cache for portfolio {portfolio_id}")
                except Exception as cache_e:
                    logger.warning(f"Error invalidating cache: {str(cache_e)}")
                
                return True
            else:
                logger.error(f"Failed to soft delete document {document_id}")
                return False
                
        except Exception as e:
            logger.error(f"Error soft deleting document {document_id}: {str(e)}")
            logger.error(traceback.format_exc())
            return False

document_service = DocumentService()