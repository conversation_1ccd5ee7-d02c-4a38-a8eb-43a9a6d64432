"""
Property Tax Service - Calculates property tax expenses based on historical data and local tax rates.

This service analyzes historical tax data from the database and uses local tax rates
to project property tax expenses.
"""
import re
from typing import Dict, List, Optional, Any
from dataclasses import dataclass

from tavily import TavilyClient

from app.utils.logger import get_logger
from app.utils.env_cache import get_env
from app.models.ai import FinancialResponse, Source

logger = get_logger(__name__)

@dataclass
class PropertyTaxContext:
    """Context for property tax calculations."""
    prop_id: str
    portfolio_id: str
    zipcode: Optional[str] = None
    city: Optional[str] = None
    state: Optional[str] = None
    estimated_property_value: Optional[float] = None
    effective_gross_income: Optional[float] = None

class PropertyTaxService:
    """Service for property tax calculations."""
    
    def __init__(self):
        """Initialize the property tax service."""
        self.tavily_client = TavilyClient(api_key=get_env('TAVILY_API_KEY'))
    
    async def get_property_tax_history(self, prop_id: str) -> List[Dict[str, Any]]:
        """Get property tax history from the database."""
        try:
            # Import here to avoid circular imports
            from app.repository.db import get_db_client
            
            # Get the client using the standard function
            client = await get_db_client()
            
            # Query property tax history directly
            response = client.table("prop_tax_history").select("*").eq("prop_id", prop_id).execute()
            
            if not response.data:
                logger.info(f"No tax history found for property {prop_id}")
                return []
            
            # Sort by year
            tax_history = sorted(response.data, key=lambda x: x.get('assessed_tax_year', 0), reverse=True)
            
            logger.info(f"Found {len(tax_history)} tax history records for property {prop_id}")
            return tax_history
            
        except Exception as e:
            logger.error(f"Error getting property tax history: {str(e)}")
            return []
    
    def analyze_tax_trend(self, tax_history: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze tax history to identify trends."""
        try:
            if len(tax_history) < 2:
                return {"has_trend": False, "annual_change": 0.0, "confidence": "low"}
            
            # Extract tax amounts and years
            tax_points = []
            for record in tax_history:
                year = record.get('assessed_tax_year')
                amount = record.get('tax_bill_amount')
                if year and amount and amount > 0:
                    tax_points.append((year, amount))
            
            if len(tax_points) < 2:
                return {"has_trend": False, "annual_change": 0.0, "confidence": "low"}
            
            # Sort by year
            tax_points.sort()
            
            # Calculate year-over-year changes
            changes = []
            for i in range(1, len(tax_points)):
                prev_year, prev_amount = tax_points[i-1]
                curr_year, curr_amount = tax_points[i]
                
                years_diff = curr_year - prev_year
                if years_diff > 0:
                    annual_change = (curr_amount / prev_amount) ** (1/years_diff) - 1
                    changes.append(annual_change)
            
            if not changes:
                return {"has_trend": False, "annual_change": 0.0, "confidence": "low"}
            
            # Average annual change
            avg_change = sum(changes) / len(changes)
            
            # Confidence based on data points and consistency
            confidence = "high" if len(tax_points) >= 3 else "medium"
            if len(tax_points) >= 5:
                # Check consistency
                change_variance = sum((c - avg_change) ** 2 for c in changes) / len(changes)
                if change_variance < 0.01:  # Low variance = high confidence
                    confidence = "high"
            
            logger.info(f"Tax trend analysis: {avg_change*100:.2f}% annual change, confidence: {confidence}")
            
            return {
                "has_trend": True,
                "annual_change": avg_change,
                "confidence": confidence,
                "years_analyzed": len(tax_points),
                "latest_amount": tax_points[-1][1] if tax_points else 0
            }
            
        except Exception as e:
            logger.error(f"Error analyzing tax trend: {str(e)}")
            return {"has_trend": False, "annual_change": 0.0, "confidence": "low"}
    
    async def get_local_tax_rates(self, zipcode: Optional[str], city: Optional[str], state: Optional[str]) -> Dict[str, Any]:
        """Get local tax rates using Tavily search."""
        try:
            # Create search query
            location = ""
            if zipcode:
                location = f"ZIP code {zipcode}"
            elif city and state:
                location = f"{city}, {state}"
            else:
                logger.warning("No location information provided for tax rate search")
                return {"tax_rate": 0.012, "source": "default"}  # Default 1.2% tax rate
            
            query = f"property tax rate {location} real estate tax rate 2024 percentage"
            
            logger.info(f"Searching for tax rates: {query}")
            
            # Search using Tavily
            response = self.tavily_client.search(
                query=query,
                search_depth="advanced",
                max_results=5
            )
            
            if not response or 'results' not in response:
                logger.warning("No tax rate data found")
                return {"tax_rate": 0.012, "source": "default"}
            
            # Extract tax rate information
            tax_rate_data = self._extract_tax_rates(response['results'], location)
            logger.info(f"Found tax rates: {tax_rate_data}")
            
            return tax_rate_data
            
        except Exception as e:
            logger.error(f"Error searching for tax rates: {str(e)}")
            return {"tax_rate": 0.012, "source": "default"}
    
    def _extract_tax_rates(self, search_results: List[Dict], location: str) -> Dict[str, Any]:
        """Extract tax rate information from search results."""
        try:
            tax_rates = []
            
            # Look for tax rate information in search results
            for result in search_results:
                content = result.get('content', '').lower()
                
                # Common patterns for tax rates
                patterns = [
                    r'tax\s+rate[:\s]*(\d+\.?\d*)%',
                    r'property\s+tax[:\s]*(\d+\.?\d*)%',
                    r'(\d+\.?\d*)%\s+tax\s+rate',
                    r'(\d+\.?\d*)\s*percent\s+tax',
                    r'tax[:\s]*(\d+\.?\d*)\s*percent',
                    r'mill\s+rate[:\s]*(\d+\.?\d*)',  # Mill rates
                    r'(\d+\.?\d*)\s*mills'
                ]
                
                for pattern in patterns:
                    matches = re.findall(pattern, content)
                    for match in matches:
                        try:
                            rate = float(match)
                            
                            # Handle mill rates (1 mill = 0.1%)
                            if 'mill' in pattern.lower():
                                rate = rate / 1000  # Convert mills to percentage
                            else:
                                rate = rate / 100  # Convert percentage to decimal
                            
                            # Sanity check - typical property tax rates are between 0.5% and 4%
                            if 0.005 <= rate <= 0.04:
                                tax_rates.append(rate)
                        except ValueError:
                            continue
            
            if tax_rates:
                # Use average of found tax rates
                avg_tax_rate = sum(tax_rates) / len(tax_rates)
                return {
                    "tax_rate": avg_tax_rate,
                    "source": "tavily_search",
                    "description": f"Property tax rate for {location}",
                    "rates_found": len(tax_rates),
                    "content_snippet": search_results[0].get('content', '')[:200] if search_results else ""
                }
            
            # If no specific rate found, return regional average
            logger.warning(f"Could not extract specific tax rate from search results for {location}")
            return {"tax_rate": 0.012, "source": "estimated_average"}
            
        except Exception as e:
            logger.error(f"Error extracting tax rates: {str(e)}")
            return {"tax_rate": 0.012, "source": "default"}
    
    def calculate_property_tax(
        self, 
        property_value: float, 
        tax_rate: float
    ) -> Dict[str, Any]:
        """Calculate property tax based on property value and tax rate."""
        try:
            annual_tax = property_value * tax_rate
            
            logger.info(f"Property tax calculation: ${property_value:.2f} * {tax_rate*100:.3f}% = ${annual_tax:.2f}")
            
            return {
                "annual_tax": annual_tax,
                "property_value": property_value,
                "tax_rate": tax_rate,
                "calculation_method": "value_based"
            }
            
        except Exception as e:
            logger.error(f"Error calculating property tax: {str(e)}")
            return {"annual_tax": 0, "property_value": property_value, "tax_rate": tax_rate}
    
    async def calculate_property_tax_projections(self, context: PropertyTaxContext) -> Dict[str, FinancialResponse]:
        """Calculate property tax projections for years 1-10."""
        try:
            projections = {}
            
            # First, try to use historical tax data for projections
            tax_history = await self.get_property_tax_history(context.prop_id)
            trend_analysis = self.analyze_tax_trend(tax_history)
            
            if trend_analysis.get('has_trend') and trend_analysis.get('confidence') in ['medium', 'high']:
                # Use historical trend for projections
                logger.info("Using historical tax trend for projections")
                
                latest_amount = trend_analysis.get('latest_amount', 0)
                annual_change = trend_analysis.get('annual_change', 0)
                
                if latest_amount > 0:
                    for year in range(1, 11):
                        projected_tax = latest_amount * ((1 + annual_change) ** year)
                        
                        projection = FinancialResponse(
                            task_type="property_tax",
                            portfolio_id=context.portfolio_id,
                            prop_id=context.prop_id,
                            year=year,
                            sources=[
                                Source(
                                    source="property_tax_history",
                                    description=f"Historical tax trend analysis with {annual_change*100:.2f}% annual change",
                                    url="database"
                                )
                            ],
                            value=annual_change * 100,  # Annual change as percentage
                            unit="%",
                            money_value=-projected_tax,  # Make negative since this is an expense
                            money_unit="USD",
                            description=f"Projected property tax for year {year} based on historical trend.",
                            additional_data=None  # Using None instead of dict
                        )
                        
                        projections[f"year_{year}"] = projection
                        logger.info(f"Property Tax Year {year}: ${projected_tax:.2f}")
                
                return projections
            
            # Fallback: Use property value and local tax rates
            logger.info("Using property value and local tax rates for projections")
            
            # Get local tax rates
            tax_rate_data = await self.get_local_tax_rates(context.zipcode, context.city, context.state)
            tax_rate = tax_rate_data.get('tax_rate', 0.012)
            
            # Use estimated property value
            property_value = context.estimated_property_value or 0
            
            if property_value <= 0:
                # Estimate value from income if needed
                if context.effective_gross_income:
                    # Use 10x income as rough property value estimate
                    property_value = context.effective_gross_income * 10
                    logger.info(f"Estimated property value from income: ${property_value:.2f}")
                else:
                    logger.warning("No property value or income data for tax calculation")
                    return {}
            
            # Calculate base tax
            tax_calc = self.calculate_property_tax(property_value, tax_rate)
            base_tax = tax_calc.get('annual_tax', 0)
            
            if base_tax > 0:
                # Project with modest annual increase (typically 2-3% for property taxes)
                annual_increase = 0.025  # 2.5% annual increase
                
                for year in range(1, 11):
                    projected_tax = base_tax * ((1 + annual_increase) ** (year - 1))
                    
                    projection = FinancialResponse(
                        task_type="property_tax",
                        portfolio_id=context.portfolio_id,
                        prop_id=context.prop_id,
                        year=year,
                        sources=[
                            Source(
                                source="local_tax_rates",
                                description="Calculated from local tax rates and estimated property value",
                                url=tax_rate_data.get('source', 'tavily_search')
                            )
                        ],
                        value=tax_rate * 100,  # Tax rate as percentage
                        unit="%",
                        money_value=-projected_tax,  # Make negative since this is an expense
                        money_unit="USD",
                        description=f"Projected property tax for year {year} at {tax_rate*100:.3f}% tax rate.",
                        additional_data=None  # Using None instead of dict
                    )
                    
                    projections[f"year_{year}"] = projection
                    logger.info(f"Property Tax Year {year}: ${projected_tax:.2f}")
            
            return projections
            
        except Exception as e:
            logger.error(f"Error calculating property tax projections: {str(e)}")
            return {} 