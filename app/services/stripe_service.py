"""
Stripe service for handling subscriptions and payments.
"""
import uuid
import json
import stripe
from datetime import datetime
from typing import Dict, Any, List

from app.utils.env_cache import get_env
from app.utils.logger import get_logger
from app.repository.db import get_db_client
from app.services.stripe_handlers import EVENT_HANDLERS, IGNORED_EVENTS
from app.models.database import Workspaces, WorkspaceMembers, WorkspaceSubscriptions

logger = get_logger(__name__)

# Configure Stripe with API key from environment
stripe.api_key = get_env("STRIPE_SECRET_KEY")
STRIPE_SUBSCRIPTION_PRICE_ID = get_env("STRIPE_SUBSCRIPTION_PRICE_ID")
FRONTEND_URL = get_env("FRONTEND_URL")

class StripeService:
    """Service for handling Stripe payments and subscriptions."""
    
    @staticmethod
    async def get_or_create_customer(user_id: str, email: str, name: str = None) -> str:
        """
        Get an existing Stripe customer or create a new one if none exists.
        
        Args:
            user_id: User ID in our database
            email: User's email address
            name: User's full name (optional)
            
        Returns:
            str: Stripe customer ID
        """
        # Get Supabase client
        db = await get_db_client()
        
        # Check if user already has a customer ID
        result = db.table("users").select("stripe_customer_id").eq("id", user_id).execute()
        user_data = result.data[0] if result.data else None
        
        if user_data and user_data.get("stripe_customer_id"):
            try:
                customer_id = user_data["stripe_customer_id"]
                customer_stripe = stripe.Customer.retrieve(customer_id)
                if customer_stripe.get("deleted"):
                    logger.warning(f"Customer {customer_id} is deleted, will create new one")
                else:
                    return customer_id
            except stripe.error.InvalidRequestError:
                logger.warning(f"Invalid Stripe customer ID for user {user_id}, will create new one")
            except Exception as e:
                logger.warning(f"Error verifying Stripe customer for user {user_id}: {str(e)}")
        
        # No valid customer ID in our database, search for existing customer in Stripe by email
        try:
            # Use Stripe's search API to find customer by email
            search_query = f"email:'{email}'"
            existing_customers = stripe.Customer.search(query=search_query)
            
            if existing_customers and existing_customers.data:
                # Search results don't include the deleted flag, so we need to verify each customer individually
                for search_result in existing_customers.data:
                    try:
                        # Retrieve the full customer object to check if it's deleted
                        customer = stripe.Customer.retrieve(search_result.id)
                        
                        # Skip if customer is deleted
                        if customer.get("deleted"):
                            logger.warning(f"Found a deleted customer with ID {customer.id} for email {email}, skipping")
                            continue
                        
                        # Use the first non-deleted customer
                        customer_id = customer.id
                        
                        # Update customer metadata with our user_id if not already set
                        if not customer.metadata.get("user_id"):
                            stripe.Customer.modify(
                                customer_id,
                                metadata={"user_id": user_id}
                            )
                        
                        # Save customer ID to our database
                        db.table("users").update({"stripe_customer_id": customer_id}).eq("id", user_id).execute()
                        
                        logger.info(f"Found existing Stripe customer {customer_id} for email {email}, linked to user {user_id}")
                        return customer_id
                    except Exception as e:
                        logger.warning(f"Error verifying customer {search_result.id}: {str(e)}")
                        continue
                
                # If we got here, all found customers were deleted or invalid
                logger.warning(f"No valid customers found for email {email}, creating a new one")
        except Exception as e:
            logger.warning(f"Error searching for customer by email {email}: {str(e)}")
            # Continue to create a new customer if search fails
        
        # Create new customer in Stripe - no valid customer found in our database or in Stripe
        customer_data = {
            "email": email,
            "metadata": {"user_id": user_id}
        }
        
        if name:
            customer_data["name"] = name
            
        try:
            customer = stripe.Customer.create(**customer_data)
            
            # Save customer ID to our database
            db.table("users").update({"stripe_customer_id": customer.id}).eq("id", user_id).execute()
            
            logger.info(f"Created new Stripe customer {customer.id} for user {user_id}")
            return customer.id
        except Exception as e:
            logger.error(f"Failed to create Stripe customer for user {user_id}: {str(e)}")
            raise
    
    @staticmethod
    async def calculate_subscription_quantity(
        user_id: str,
        new_workspace_id: str = None,
        new_states: List[str] = None,
        remove_states: List[str] = None
    ) -> int:
        """
        Calculate the total subscription quantity for a user based on workspace×state pairs.
        
        Args:
            user_id: User ID of the billing admin
            new_workspace_id: Workspace ID for a new subscription (optional)
            new_states: List of state codes to add (optional)
            remove_states: List of state codes to remove (optional)
            
        Returns:
            int: Total number of billable workspace × state pairs
        """
        db = await get_db_client()
        
        # Find workspaces where user is billing admin
        workspaces_result = db.table("workspaces").select("id").eq("billing_admin_id", user_id).execute()
        
        # Count existing active workspace×state combinations
        total_count = 0
        
        if workspaces_result.data:
            workspace_ids = [workspace["id"] for workspace in workspaces_result.data]
            
            # Log the workspaces found
            logger.info(f"Found {len(workspace_ids)} workspaces for user {user_id}: {workspace_ids}")
            
            for workspace_id in workspace_ids:
                # Get all active subscriptions for this workspace
                subscriptions_result = db.table("workspace_subscriptions") \
                    .select("*") \
                    .eq("workspace_id", workspace_id) \
                    .eq("status", "active") \
                    .execute()
                
                # Add all state subscriptions for this workspace
                if subscriptions_result.data:
                    logger.info(f"Found {len(subscriptions_result.data)} active subscriptions for workspace {workspace_id}")
                    total_count += len(subscriptions_result.data)
        
        # Add new states if provided
        if new_workspace_id and new_states:
            # Check if any of these states already exist for this workspace to avoid double counting
            existing_states = []
            if workspaces_result.data and new_workspace_id in [w["id"] for w in workspaces_result.data]:
                existing_states_result = db.table("workspace_subscriptions") \
                    .select("state_code") \
                    .eq("workspace_id", new_workspace_id) \
                    .eq("status", "active") \
                    .execute()
                
                if existing_states_result.data:
                    existing_states = [s["state_code"] for s in existing_states_result.data]
            
            # Count only new states that don't already exist
            new_state_count = sum(1 for state in new_states if state not in existing_states)
            logger.info(f"Adding {new_state_count} new states for workspace {new_workspace_id}")
            total_count += new_state_count
        
        # Subtract states to be removed if provided
        if remove_states:
            # We're just calculating price here, not actually removing anything
            logger.info(f"Would remove {len(remove_states)} states if this was an update operation")
            # Not implementing removal logic here as it's for pricing calculation only
        
        logger.info(f"Total subscription quantity: {max(1, total_count)}")
        return max(1, total_count)  # Ensure at least 1
    
    @staticmethod
    async def create_checkout_session(user_id: str, workspace_id: str, states: List[str]) -> Dict[str, Any]:
        """
        Create a Stripe Checkout Session for a new workspace subscription.
        
        Args:
            user_id: User ID of the billing admin
            workspace_id: Workspace ID for the subscription
            states: List of state codes (e.g., ['CA']) for the subscription
            
        Returns:
            Dict with session details including the checkout URL
        """
        db = await get_db_client()
        
        # Get user information
        user_result = db.table("users").select("*").eq("id", user_id).execute()
        if not user_result.data:
            raise ValueError(f"User {user_id} not found")
            
        user = user_result.data[0]
        
        # Ensure user has a Stripe customer ID
        customer_id = await StripeService.get_or_create_customer(
            user_id=user_id,
            email=user.get("email", ""),
            name=user.get("full_name")
        )
        
        # Calculate quantity based on workspace×state pairs
        quantity = await StripeService.calculate_subscription_quantity(
            user_id=user_id,
            new_workspace_id=workspace_id,
            new_states=states
        )
        
        logger.info(f"QUANTITY: {quantity} for states: {states}")
        
        # Create checkout session
        try:
            # Stripe SDK is synchronous
            session = stripe.checkout.Session.create(
                customer=customer_id,
                payment_method_types=["card", "cashapp"],
                line_items=[{
                    "price": STRIPE_SUBSCRIPTION_PRICE_ID,
                    "quantity": quantity
                }],
                mode="subscription",
                payment_method_collection="always",
                subscription_data={
                    "metadata": {
                        "workspace_id": workspace_id,
                        "states": ",".join(states)
                    },
                    "trial_period_days": 14
                },
                success_url=f"{FRONTEND_URL}/workspace/{workspace_id}/billing/success",
                cancel_url=f"{FRONTEND_URL}/workspace/{workspace_id}/billing/cancel",
                metadata={
                    "user_id": user_id,
                    "workspace_id": workspace_id,
                    "states": ",".join(states),  # Convert list to comma-separated string for metadata
                    "quantity": str(quantity)
                },
                allow_promotion_codes=True
            )
            
            logger.info(f"Checkout session URL: {session.url}")
            
            return {
                "session_id": session.id,
                "url": session.url,
                "customer_id": customer_id,
                "quantity": quantity,
                "states": states
            }
        except Exception as e:
            logger.error(f"Failed to create checkout session: {str(e)}")
            raise
    
    @staticmethod
    async def handle_webhook_event(payload_str: str, signature: str) -> Dict[str, Any]:
        """
        Handle a Stripe webhook event.
        
        This method processes various Stripe events and updates our database accordingly.
        Event handlers are defined in stripe_handlers.py.
        
        Args:
            payload_str: The raw request body string from Stripe
            signature: The Stripe signature header value
            
        Returns:
            Dict with status and processing information
        """
        # Verify the event with Stripe
        webhook_secret = get_env("STRIPE_WEBHOOK_SECRET")
        event = None
        
        # Attempt to construct and verify the event
        try:
            if webhook_secret:
                try:
                    event = stripe.Webhook.construct_event(
                        payload=payload_str,
                        sig_header=signature,
                        secret=webhook_secret
                    )
                except stripe.error.SignatureVerificationError as e:
                    logger.error(f"Stripe signature verification failed: {str(e)}")
                    return {
                        "success": False,
                        "error": str(e),
                        "status": "rejected"
                    }
            else:
                # If no webhook secret is configured, parse the payload as JSON
                logger.warning("STRIPE_WEBHOOK_SECRET not set, skipping signature verification")
                event_data = json.loads(payload_str)
                event = event_data
        except Exception as e:
            logger.error(f"Error processing webhook: {str(e)}")
            return {
                "success": False,
                "error": f"Invalid payload or signature: {str(e)}"
            }
        
        if not event:
            return {
                "success": False,
                "error": "Failed to parse Stripe event"
            }
        
        # Get the event type
        event_type = event.get("type")
        logger.info(f"Processing Stripe webhook event: {event_type}")
        
        # Get the appropriate handler for this event type
        handler = EVENT_HANDLERS.get(event_type)
        
        try:
            if handler:
                return await handler(event)
            elif event_type in IGNORED_EVENTS:
                logger.info(f"IGNORED EVENT: {event_type}")
                return {
                    "success": True,
                    "status": "ignored",
                    "message": f"Event type '{event_type}' is ignored by design"
                }
            else:
                logger.info(f"UNHANDLED EVENT: {event_type}")
                return {
                    "success": True,
                    "status": "ignored",
                    "message": f"Event type '{event_type}' is not handled by this endpoint"
                }
        except Exception as e:
            # Catch all exceptions from handlers to prevent 500 errors
            logger.error(f"Error handling event {event_type}: {str(e)}")
            return {
                "success": False,
                "error": f"Error processing event {event_type}: {str(e)}",
                "event_type": event_type,
                "status": "error"
            }

    @staticmethod
    async def create_customer_portal_session(user_id: str) -> Dict[str, Any]:
        """
        Create a Stripe Customer Portal session for subscription management.
        
        This allows customers to:
        - Update payment methods
        - View invoice history
        - Manage subscriptions (cancel, upgrade, etc.)
        
        Args:
            user_id: User ID in our database
            return_url: URL to redirect after the customer completes the portal session
            
        Returns:
            Dict with session details including the portal URL
        """
        db = await get_db_client()
        
        # Get user information including email
        user_result = db.table("users").select("*").eq("id", user_id).execute()
        if not user_result.data:
            raise ValueError(f"User {user_id} not found")
        
        user = user_result.data[0]
        
        # Ensure user has a Stripe customer ID using get_or_create_customer
        customer_id = await StripeService.get_or_create_customer(
            user_id=user_id,
            email=user.get("email", ""),
            name=user.get("full_name")
        )
        
        try:
            # Create the portal session
            session = stripe.billing_portal.Session.create(
                customer=customer_id,
                return_url=f"{FRONTEND_URL}/workspace"
            )
            
            logger.info(f"PORTAL URL: {session.url} FOR USER: {user_id}")
            
            return {
                "success": True,
                "url": session.url,
                "session_id": session.id,
                "customer_id": customer_id
            }
        except Exception as e:
            logger.error(f"Failed to create customer portal session: {str(e)}")
            raise

    @staticmethod
    async def get_subscription_details(user_id: str, workspace_id: str = None) -> Dict[str, Any]:
        """
        Get subscription details for a user's workspaces.
        
        This method retrieves subscription information from our database,
        avoiding unnecessary Stripe API calls. Optimized for performance
        with minimal queries and no loops.
        
        Args:
            user_id: User ID to get subscription details for
            workspace_id: Optional workspace ID to filter results
            
        Returns:
            Dict with subscription details
        """
        db = await get_db_client()
        
        # Step 1: Get workspaces - either specific one or all accessible workspaces
        if workspace_id:
            # For specific workspace: simple query
            workspace_query = db.table(Workspaces.Config.table) \
                .select("*") \
                .eq("id", workspace_id) \
                .eq("is_deleted", False) \
                .execute()
            
            workspace_data = workspace_query.data
        else:
            # For all workspaces: get admin + member workspaces
            
            # Admin workspaces
            admin_workspaces_query = db.table(Workspaces.Config.table) \
                .select("*") \
                .eq("billing_admin_id", user_id) \
                .eq("is_deleted", False) \
                .order("updated_at", desc=True) \
                .execute()
            
            # Member workspaces (get IDs first, then details)
            member_query = db.table(WorkspaceMembers.Config.table) \
                .select("workspace_id") \
                .eq("user_id", user_id) \
                .execute()
            
            # Extract member workspace IDs
            member_workspace_ids = [m["workspace_id"] for m in member_query.data] if member_query.data else []
            
            if member_workspace_ids:
                # Use 'in' filter for a single query if member workspaces exist
                member_workspaces_query = db.table(Workspaces.Config.table) \
                    .select("*") \
                    .in_("id", member_workspace_ids) \
                    .eq("is_deleted", False) \
                    .order("updated_at", desc=True) \
                    .execute()
                
                member_workspace_data = member_workspaces_query.data
            else:
                member_workspace_data = []
            
            # Combine admin and member workspaces, avoiding duplicates with a set
            admin_workspace_ids = {w["id"] for w in admin_workspaces_query.data} if admin_workspaces_query.data else set()
            
            workspace_data = admin_workspaces_query.data if admin_workspaces_query.data else []
            
            # Add member workspaces that aren't admin workspaces
            workspace_data.extend([w for w in member_workspace_data if w["id"] not in admin_workspace_ids])
        
        # Handle case where no workspaces are found
        if not workspace_data:
            if workspace_id:
                raise ValueError(f"Workspace {workspace_id} not found or user does not have access")
            return []
        
        # Step 2: Get subscription data for all workspaces at once
        workspace_ids = [w["id"] for w in workspace_data]
        
        subscriptions_query = db.table(WorkspaceSubscriptions.Config.table) \
            .select("*") \
            .in_("workspace_id", workspace_ids) \
            .execute()
        
        subscription_data = subscriptions_query.data if subscriptions_query.data else []
        
        # Group subscriptions by workspace_id for efficient lookup
        subscriptions_by_workspace = {}
        for sub in subscription_data:
            if sub["workspace_id"] not in subscriptions_by_workspace:
                subscriptions_by_workspace[sub["workspace_id"]] = []
            subscriptions_by_workspace[sub["workspace_id"]].append(sub)
        
        # Step 3: Process workspace data with subscriptions
        result_workspaces = []
        
        for workspace in workspace_data:
            # Get subscriptions for this workspace
            workspace_subs = subscriptions_by_workspace.get(workspace["id"], [])
            
            # Filter active subscriptions and extract their state codes
            active_states = [
                sub["state_code"] for sub in workspace_subs 
                if sub.get("status") == "active" and sub.get("state_code")
            ]
            
            # Determine if workspace has active subscription directly from subscription status
            has_active_subscription = any(sub.get("status") == "active" for sub in workspace_subs)
            
            # Get unique subscription IDs
            subscription_ids = list({
                sub["stripe_subscription_id"] for sub in workspace_subs 
                if sub.get("status") == "active" and sub.get("stripe_subscription_id")
            })
            
            # Create workspace details object
            workspace_details = {
                "id": workspace["id"],
                "name": workspace["name"],
                "has_active_subscription": has_active_subscription,
                "is_admin": workspace["billing_admin_id"] == user_id,
                "billing_admin_id": workspace["billing_admin_id"],
                "active_states": active_states,
                "subscription_ids": subscription_ids,
                "created_at": workspace["created_at"]
            }
            
            result_workspaces.append(workspace_details)
        
        # Return the results - always return the list directly
        return result_workspaces

    @staticmethod
    async def preview_subscription_changes(user_id: str, workspace_id: str, added_states: List[str] = None, removed_states: List[str] = None) -> Dict[str, Any]:
        """
        Preview subscription changes using Stripe's upcoming invoice API.
        
        This shows what will be charged immediately (only the proration difference)
        when adding or removing states from a subscription.
        
        Args:
            user_id: User ID of the billing admin
            workspace_id: Workspace ID to modify subscription for
            added_states: List of state codes to add (optional)
            removed_states: List of state codes to remove (optional)
            
        Returns:
            Dict with preview details including updated amount and proration
        """
        db = await get_db_client()
        
        # Validate input - ensure at least one is provided and handle empty lists
        added_states = added_states or []
        removed_states = removed_states or []
        
        if not added_states and not removed_states:
            raise ValueError("Must provide either added_states or removed_states")
            
        # Get user information including email
        user_result = db.table("users").select("*").eq("id", user_id).execute()
        if not user_result.data:
            raise ValueError(f"User {user_id} not found")
            
        user = user_result.data[0]
        
        # Get or create Stripe customer ID
        customer_id = await StripeService.get_or_create_customer(
            user_id=user_id,
            email=user.get("email", ""),
            name=user.get("full_name")
        )
        
        # Get workspace details
        workspace_result = db.table("workspaces").select("*").eq("id", workspace_id).execute()
        if not workspace_result.data:
            raise ValueError(f"Workspace {workspace_id} not found")
            
        workspace = workspace_result.data[0]
        
        # Check if user is billing admin
        if workspace.get("billing_admin_id") != user_id:
            raise ValueError("Only the billing admin can modify subscriptions")
        
        # Get current subscriptions for the workspace
        subscriptions_result = db.table("workspace_subscriptions")\
            .select("*")\
            .eq("workspace_id", workspace_id)\
            .execute()
            
        # Find active states and Stripe subscription ID
        active_states = []
        stripe_subscription_id = None
        
        for sub in subscriptions_result.data:
            if sub.get("status") == "active" and sub.get("state_code"):
                active_states.append(sub["state_code"])
                
            if not stripe_subscription_id and sub.get("stripe_subscription_id"):
                stripe_subscription_id = sub["stripe_subscription_id"]
        
        if not stripe_subscription_id:
            raise ValueError(f"No active subscription found for workspace {workspace_id}")
        
        # Check if any added states are already active
        if added_states:
            already_active = [state for state in added_states if state in active_states]
            if already_active:
                active_states_text = ", ".join(already_active)
                if len(already_active) == len(added_states):
                    # All requested states are already active
                    return {
                        "success": False,
                        "error": f"All requested states ({active_states_text}) are already active in this workspace subscription",
                        "already_active_states": already_active
                    }
                else:
                    # Some states are already active
                    return {
                        "success": False,
                        "error": f"Some requested states ({active_states_text}) are already active in this workspace subscription. Please preview only inactive states.",
                        "already_active_states": already_active,
                        "inactive_states": [state for state in added_states if state not in active_states]
                    }
        
        # Validate that states to be removed are actually active
        if removed_states:
            invalid_removals = [state for state in removed_states if state and state not in active_states]
            if invalid_removals:
                raise ValueError(f"Cannot remove states that are not active in the current subscription: {', '.join(invalid_removals)}")
        
        # Calculate new states list
        new_states = active_states.copy()
        
        if added_states:
            for state in added_states:
                if state and state not in new_states:
                    new_states.append(state)
                    
        if removed_states:
            for state in removed_states:
                if state and state in new_states:
                    new_states.remove(state)
        
        # Calculate new quantity (must be at least 1)
        new_quantity = max(1, len(new_states))
        
        # Get current subscription to find current quantity
        try:
            subscription = stripe.Subscription.retrieve(stripe_subscription_id)
            current_quantity = subscription["items"]["data"][0]["quantity"]
            
            # Preview invoice with only proration, no renewal charge
            invoice = stripe.Invoice.create_preview(
                customer=customer_id,
                subscription=stripe_subscription_id,
                subscription_details={
                    "items": [{
                        "id": subscription["items"]["data"][0]["id"],
                        "quantity": new_quantity
                    }],
                    "proration_behavior": "create_prorations",
                    "billing_cycle_anchor": "unchanged"
                }
            )
            
            # Parse invoice lines to extract meaningful data
            invoice_lines = invoice.get("lines", {}).get("data", [])
            
            # Initialize variables to track the different components
            current_period_refund = 0
            current_period_charge = 0
            
            for line in invoice_lines:
                description = line.get("description", "")
                amount = line.get("amount", 0) / 100  # Convert cents to dollars
                is_proration = False
                
                if line.get("parent") and line.get("parent").get("subscription_item_details"):
                    is_proration = line.get("parent").get("subscription_item_details").get("proration", False)
                
                # Parse line items based on their description and properties
                if "Unused time" in description and is_proration:
                    current_period_refund = abs(amount)  # Make positive for clarity
                elif "Remaining time" in description and is_proration:
                    current_period_charge = amount
            
            # Calculate the proration net effect (new charge minus refund)
            proration_amount = current_period_charge - current_period_refund
            
            # Base price per state
            base_price_per_state = 249  # Use known fixed price of $249 per state
            
            return {
                "success": True,
                "workspace_id": workspace_id,
                "current_states": active_states,
                "new_states": new_states,
                "current_quantity": current_quantity,
                "new_quantity": new_quantity,
                
                # Clear breakdown of costs
                "pricing": {
                    "base_price_per_state": base_price_per_state,
                    "current_monthly_cost": base_price_per_state * current_quantity,
                    "new_monthly_cost": base_price_per_state * new_quantity,
                    
                    # Proration details
                    "proration": {
                        "refund_amount": -current_period_refund,  # Show as negative for clarity
                        "charge_amount": current_period_charge,
                        "net_adjustment": float(f"{proration_amount:.2f}")  # limit to 2 decimals
                    },
                    
                    "immediate_charge": float(f"{proration_amount:.2f}"),  # Only proration, no next period
                    "next_period_charge": base_price_per_state * new_quantity,
                    "billing_note": "Only the proration amount will be charged now. Your regular subscription amount will be charged on your next renewal date."
                }
            }
        except Exception as e:
            logger.error(f"Error previewing subscription changes: {str(e)}")
            raise ValueError(f"Failed to generate preview: {str(e)}")

    @staticmethod
    async def modify_subscription(
        user_id: str, 
        workspace_id: str, 
        added_states: List[str] = None, 
        removed_states: List[str] = None
    ) -> Dict[str, Any]:
        """
        Modify a subscription by adding and/or removing states in a single operation.
        
        This combined function handles both adding new states and removing existing ones
        in a single Stripe API call, ensuring atomicity of the operation.
        
        Args:
            user_id: User ID of the billing admin
            workspace_id: Workspace ID to modify
            added_states: Optional list of state codes to add
            removed_states: Optional list of state codes to remove
            
        Returns:
            Dict with comprehensive update details including added and removed states
        """
        db = await get_db_client()
        
        # Normalize inputs to empty lists if None
        added_states = added_states or []
        removed_states = removed_states or []
        
        # Validate that at least one operation is requested
        if not added_states and not removed_states:
            raise ValueError("Must provide either added_states or removed_states")
            
        # Get workspace details
        workspace_result = db.table("workspaces").select("*").eq("id", workspace_id).execute()
        if not workspace_result.data:
            raise ValueError(f"Workspace {workspace_id} not found")
            
        workspace = workspace_result.data[0]
        
        # Check if user is billing admin
        if workspace.get("billing_admin_id") != user_id:
            raise ValueError("Only the billing admin can modify subscriptions")
            
        # Get current subscriptions for the workspace
        subscriptions_result = db.table("workspace_subscriptions")\
            .select("*")\
            .eq("workspace_id", workspace_id)\
            .execute()
            
        # Find active states and Stripe subscription ID
        active_states = []
        stripe_subscription_id = None
        
        for sub in subscriptions_result.data:
            if sub.get("status") == "active" and sub.get("state_code"):
                active_states.append(sub["state_code"])
                
            if not stripe_subscription_id and sub.get("stripe_subscription_id"):
                stripe_subscription_id = sub["stripe_subscription_id"]
        
        if not stripe_subscription_id:
            raise ValueError(f"No active subscription found for workspace {workspace_id}")
        
        # Validate added states - check if any are already active
        already_active = [state for state in added_states if state in active_states]
        inactive_states = [state for state in added_states if state not in active_states]
        
        if already_active and len(already_active) == len(added_states):
            # All requested states are already active
            return {
                "success": False,
                "error": f"All requested states ({', '.join(already_active)}) are already active in this workspace subscription",
                "already_active_states": already_active
            }
        elif already_active:
            # Some states are already active - filter to only inactive ones
            logger.info(f"States {', '.join(already_active)} already active. Only adding inactive states.")
            added_states = inactive_states
        
        # Validate removed states - check if any are not currently active
        invalid_removals = [state for state in removed_states if state not in active_states]
        valid_removals = [state for state in removed_states if state in active_states]
        
        if invalid_removals and len(invalid_removals) == len(removed_states):
            # All requested removals are invalid
            return {
                "success": False,
                "error": f"Cannot remove states that are not active in the current subscription: {', '.join(invalid_removals)}",
                "invalid_removals": invalid_removals
            }
        elif invalid_removals:
            # Some removals are invalid - filter to only valid ones
            logger.info(f"States {', '.join(invalid_removals)} not active. Only removing active states.")
            removed_states = valid_removals
        
        # If after validation, there's nothing to do, return early
        if not added_states and not removed_states:
            return {
                "success": False,
                "error": "No valid changes to make after validation",
                "already_active_states": already_active,
                "invalid_removals": invalid_removals
            }

        # Get user information for Stripe customer
        user_result = db.table("users").select("*").eq("id", user_id).execute()
        if not user_result.data:
            raise ValueError(f"User {user_id} not found")
            
        user = user_result.data[0]
        
        # Ensure user has a Stripe customer ID
        customer_id = await StripeService.get_or_create_customer(
            user_id=user_id,
            email=user.get("email", ""),
            name=user.get("full_name")
        )
        
        # Calculate new states list by adding new states and removing others
        new_states = active_states.copy()
        
        # Add states that aren't already in the list
        for state in added_states:
            if state not in new_states:
                new_states.append(state)
                
        # Remove states that are in the list
        for state in removed_states:
            if state in new_states:
                new_states.remove(state)
        
        # Calculate new quantity (must be at least 1)
        new_quantity = max(1, len(new_states))
        
        # Update the Stripe subscription
        try:
            # First get the subscription to find customer ID and item ID
            subscription = stripe.Subscription.retrieve(stripe_subscription_id)
            
            # Update the subscription with proration behavior
            subscription = stripe.Subscription.modify(
                stripe_subscription_id,
                items=[{
                    "id": subscription["items"]["data"][0]["id"],
                    "quantity": new_quantity
                }],
                # Only charge prorations, don't advance the billing cycle
                proration_behavior="create_prorations",
                billing_cycle_anchor="unchanged",
                metadata={
                    "states": ",".join(new_states),
                    "quantity": str(new_quantity),
                    "added_states": ",".join(added_states) if added_states else "",
                    "removed_states": ",".join(removed_states) if removed_states else "",
                    "workspace_id": workspace_id
                }
            )
            
            # Variables to track payment status
            payment_success = False
            payment_action_required = False
            update_payment_url = None
            payment_error = None
            finalized_invoice = {"id": None, "status": "no_proration_needed", "amount_due": 0}
            
            # Check if there's an invoice already created for this subscription update
            invoice_list = stripe.Invoice.list(
                customer=customer_id,
                subscription=stripe_subscription_id,
                status="open",
                limit=1
            )
            
            if invoice_list and invoice_list.data:
                # We found an open invoice for this subscription
                finalized_invoice = invoice_list.data[0]
                logger.info(f"Found open invoice {finalized_invoice.id} for subscription update")
                
                # Add metadata to the invoice for tracking purposes
                try:
                    stripe.Invoice.modify(
                        finalized_invoice.id,
                        metadata={
                            "subscription_id": stripe_subscription_id,
                            "workspace_id": workspace_id,
                            "added_states": ",".join(added_states) if added_states else "",
                            "removed_states": ",".join(removed_states) if removed_states else "",
                            "user_id": user_id
                        }
                    )
                    logger.info(f"Added metadata to invoice {finalized_invoice.id}")
                except Exception as e:
                    logger.warning(f"Could not add metadata to invoice: {str(e)}")
                
                # Try to pay the invoice
                if finalized_invoice.get("amount_due", 0) > 0:
                    try:
                        payment_result = stripe.Invoice.pay(finalized_invoice.id)
                        logger.info(f"Payment succeeded for invoice {finalized_invoice.id}: {payment_result.get('status')}")
                        payment_success = True
                    except Exception as pay_error:
                        logger.warning(f"Could not automatically pay invoice: {str(pay_error)}")
                        payment_error = str(pay_error)
                        payment_action_required = True
                        # Get the invoice URL for payment
                        try:
                            invoice = stripe.Invoice.retrieve(finalized_invoice.id)
                            if invoice.get("hosted_invoice_url"):
                                update_payment_url = invoice.get("hosted_invoice_url")
                                logger.info(f"Using hosted invoice URL: {update_payment_url}")
                            else:
                                # Fallback to customer portal
                                logger.info("No hosted invoice URL, creating customer portal link")
                                portal = stripe.billing_portal.Session.create(
                                    customer=customer_id,
                                    return_url=f"{FRONTEND_URL}/workspace/{workspace_id}/billing"
                                )
                                update_payment_url = portal.url
                                logger.info(f"Created customer portal URL: {update_payment_url}")
                        except Exception as url_error:
                            logger.error(f"Failed to get payment URL: {str(url_error)}")
                            # Fallback to billing page
                            update_payment_url = f"{FRONTEND_URL}/workspace/{workspace_id}/billing"
                else:
                    # No amount due on the invoice
                    payment_success = True
            else:
                # No open invoice found, create one
                
                # First check for any pending invoice items
                pending_items = stripe.InvoiceItem.list(
                    customer=customer_id,
                    pending=True
                )
                
                logger.info(f"Found {len(pending_items.data)} pending invoice items for invoice creation")
                
                if pending_items.data:
                    # Create an invoice including all pending invoice items
                    invoice = stripe.Invoice.create(
                        customer=customer_id,
                        auto_advance=True,  # Auto-finalize the invoice
                        collection_method="charge_automatically",  # Attempt to collect payment automatically
                        pending_invoice_items_behavior="include",  # Include all pending items
                        metadata={
                            "subscription_id": stripe_subscription_id,
                            "workspace_id": workspace_id,
                            "added_states": ",".join(added_states) if added_states else "",
                            "removed_states": ",".join(removed_states) if removed_states else "",
                            "user_id": user_id
                        }
                    )
                    
                    # Finalize the invoice to initiate payment collection
                    finalized_invoice = stripe.Invoice.finalize_invoice(
                        invoice.id
                    )
                    
                    logger.info(f"Created and finalized invoice {finalized_invoice.id} for proration amount")
                    
                    # If the invoice amount is greater than 0, attempt to pay it immediately
                    if finalized_invoice.get("amount_due", 0) > 0:
                        try:
                            payment_result = stripe.Invoice.pay(finalized_invoice.id)
                            logger.info(f"Payment succeeded for invoice {finalized_invoice.id}: {payment_result.get('status')}")
                            payment_success = True
                        except Exception as pay_error:
                            logger.warning(f"Could not automatically pay invoice: {str(pay_error)}")
                            payment_error = str(pay_error)
                            payment_action_required = True
                            
                            # Get the invoice URL for payment
                            try:
                                invoice = stripe.Invoice.retrieve(finalized_invoice.id)
                                if invoice.get("hosted_invoice_url"):
                                    update_payment_url = invoice.get("hosted_invoice_url")
                                    logger.info(f"Using hosted invoice URL: {update_payment_url}")
                                else:
                                    # Fallback to customer portal
                                    logger.info("No hosted invoice URL, creating customer portal link")
                                    portal = stripe.billing_portal.Session.create(
                                        customer=customer_id,
                                        return_url=f"{FRONTEND_URL}/workspace/{workspace_id}/billing"
                                    )
                                    update_payment_url = portal.url
                                    logger.info(f"Created customer portal URL: {update_payment_url}")
                            except Exception as url_error:
                                logger.error(f"Failed to get payment URL: {str(url_error)}")
                                # Fallback to billing page
                                update_payment_url = f"{FRONTEND_URL}/workspace/{workspace_id}/billing"
                    else:
                        # No amount due
                        payment_success = True
                else:
                    # No pending items to invoice
                    logger.info("No pending invoice items found after subscription update. No immediate charge needed.")
                    payment_success = True
            
            # Update the database
            # If payment was successful, update the database immediately
            if payment_success:
                # First, set all states to inactive to simplify logic
                db.table("workspace_subscriptions")\
                    .update({"status": "inactive", "updated_at": datetime.now().isoformat()})\
                    .eq("workspace_id", workspace_id)\
                    .execute()
                    
                # Then activate the final set of states
                current_time = datetime.now().isoformat()
                
                # Get all existing state subscriptions for this workspace
                existing_subs = {}
                for sub in subscriptions_result.data:
                    if sub.get("state_code"):
                        existing_subs[sub["state_code"]] = sub
                
                # Process each state in the final state list
                for state_code in new_states:
                    if state_code in existing_subs:
                        # Re-activate existing record
                        db.table("workspace_subscriptions")\
                            .update({
                                "status": "active", 
                                "updated_at": current_time
                            })\
                            .eq("id", existing_subs[state_code]["id"])\
                            .execute()
                    else:
                        # Create new record for this state
                        db.table("workspace_subscriptions")\
                            .insert({
                                "id": str(uuid.uuid4()),
                                "workspace_id": workspace_id,
                                "state_code": state_code,
                                "status": "active",
                                "stripe_subscription_id": stripe_subscription_id,
                                "created_at": current_time,
                                "updated_at": current_time
                            })\
                            .execute()
            # If payment requires action, mark added states as pending_payment
            elif payment_action_required and added_states:
                current_time = datetime.now().isoformat()
                
                # Get all existing state subscriptions for this workspace
                existing_subs = {}
                for sub in subscriptions_result.data:
                    if sub.get("state_code"):
                        existing_subs[sub["state_code"]] = sub
                
                # Mark each added state as pending_payment
                for state_code in added_states:
                    if state_code in existing_subs:
                        # Update existing record to pending_payment
                        db.table("workspace_subscriptions")\
                            .update({
                                "status": "pending_payment", 
                                "updated_at": current_time
                            })\
                            .eq("id", existing_subs[state_code]["id"])\
                            .execute()
                    else:
                        # Create new pending_payment record
                        db.table("workspace_subscriptions")\
                            .insert({
                                "id": str(uuid.uuid4()),
                                "workspace_id": workspace_id,
                                "state_code": state_code,
                                "status": "pending_payment",
                                "stripe_subscription_id": stripe_subscription_id,
                                "created_at": current_time,
                                "updated_at": current_time
                            })\
                            .execute()
                            
                logger.info(f"Marked {len(added_states)} states as pending_payment: {', '.join(added_states)}")
            
            # Get proration amount from the invoice
            proration_amount = finalized_invoice.get("amount_due", 0) / 100
            
            # For the response, include states based on payment status
            active_states_in_response = active_states.copy()
            
            # Add new states if payment succeeded
            if payment_success:
                for state in added_states:
                    if state not in active_states_in_response:
                        active_states_in_response.append(state)
                
                # Remove states if payment succeeded
                for state in removed_states:
                    if state in active_states_in_response:
                        active_states_in_response.remove(state)
            
            return {
                "success": True,
                "workspace_id": workspace_id,
                "subscription_id": stripe_subscription_id,
                
                # State details
                "active_states": active_states_in_response,
                "added_states": added_states,
                "removed_states": removed_states,
                "pending_states": {
                    "add": added_states if not payment_success else [],
                    "remove": removed_states if not payment_success else []
                },
                
                # Subscription details
                "quantity": new_quantity,
                "proration_amount": proration_amount,
                
                # Payment details
                "invoice_id": finalized_invoice.get("id"),
                "invoice_status": finalized_invoice.get("status"),
                "payment_status": "success" if payment_success else "action_required",
                "payment_error": payment_error,
                "payment_action_required": payment_action_required,
                "update_payment_url": update_payment_url,
                
                # Messages
                "message": "Subscription updated successfully" + (
                    " with proration charged immediately" if payment_success else 
                    ". Payment required to activate changes"
                ),
                "billing_note": (
                    "The proration amount has been charged immediately. Your regular subscription amount will be charged on your next renewal date."
                    if payment_success else
                    "Payment is required to apply the subscription changes. Please update your payment method and pay the invoice."
                )
            }
        except Exception as e:
            logger.error(f"Error modifying subscription: {str(e)}")
            raise ValueError(f"Failed to modify subscription: {str(e)}")

# Singleton instance
stripe_service = StripeService()
