"""
AI service integration module.
"""
import instructor

from typing import Dict, Any, List
from openai import OpenAI

from app.utils.logger import get_logger
from app.utils.env_cache import get_env
from app.repository.db import get_db_client
from app.models.ai_responses import PropertyAISummary, PropertyProsCons, PropertyFinancialProspects

# Commenting out missing import to allow startup
# from app.models.query import Query, Location, SearchFilters, TimeWindow

logger = get_logger(__name__)

# Use cached getter
openai_api_key = get_env("OPENAI_API_KEY")
if not openai_api_key:
    logger.info("AI Service initialized without OpenAI API key.")

# Configure OpenAI client with newer API format and patch with instructor
client = instructor.patch(OpenAI(api_key=openai_api_key))

class AIService:
    """
    Service responsible for handling AI-related operations.
    Provides integration with OpenAI API.
    """
    
    def __init__(self, model_name: str = "gpt-4.1-mini"):
        """
        Initialize the AI service.
        
        Args:
            model_name: Name of the OpenAI model to use
        """
        self.model_name = model_name
        self._connection_initialized = False
        self._client = None
        self._canned_responses = [
            "I'm an AI assistant. How can I help you today?",
            "That's an interesting question. Let me think about it.",
            "I don't have enough information to answer that question.",
            "I'd be happy to help you with that!",
            "Could you provide more details about your question?",
        ]
        logger.info(f"Initialized AI service with model: {model_name}")
    
    async def generate_property_summary(self, property_data: Dict[str, Any]) -> PropertyAISummary:
        """
        Generate an AI summary of a property using OpenAI GPT-4o with Instructor.
        
        Args:
            property_data: Property data dictionary
            
        Returns:
            PropertyAISummary: The generated property summary with structured analysis
        """
        try:
            # Log the input
            # logger.info("Generating property summary using Instructor")
            
            # Create the system prompt
            system_prompt = """
            You are a real estate investment analysis AI. Given property data, provide a concise, insightful analysis 
            including:
            
            1. A general summary of the property
            2. Key pros and cons
            3. Points of interest (POI) summary
            4. Financial prospects analysis
            
            Focus on investment potential, market factors, property condition, and location benefits/drawbacks.
            """
            
            # Format the property data as user message content
            user_prompt = f"Please analyze this property data and provide a structured summary:\n\n{property_data}"
            
            # Using instructor to get structured output directly as our Pydantic model
            response = client.chat.completions.create(
                model="gpt-4.1-mini",  # Using GPT-4o as specified
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                response_model=PropertyAISummary,
                max_retries=3,  # Instructor's built-in retry mechanism
                temperature=0.3  # Lower temperature for more factual responses
            )
            
            return response
            
        except Exception as e:
            logger.error(f"Error generating property summary: {str(e)}")
            # Return a fallback summary in case of errors
            return PropertyAISummary(
                summary="Unable to generate property summary due to an error.",
                pros_cons=PropertyProsCons(
                    pros=["Analysis unavailable"], 
                    cons=["Analysis unavailable"]
                ),
                poi_summary="POI analysis unavailable.",
                financial_prospects=PropertyFinancialProspects(
                    cash_flow_potential="Analysis unavailable",
                    appreciation_outlook="Analysis unavailable",
                    investment_grade="N/A",
                    risk_factors=["Analysis unavailable"],
                    opportunity_areas=["Analysis unavailable"]
                ),
                additional_insights=None
            )
    
    # Method to store AI-generated summaries if needed in the future
    async def store_property_summary(self, property_id: str, summary: PropertyAISummary) -> bool:
        """
        Store an AI-generated property summary in the database.
        
        Args:
            property_id: ID of the property
            summary: The summary to store
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            client = await get_db_client()
            
            # Store summary in database - table layout depends on your actual schema
            # This is just an example
            summary_data = {
                "property_id": property_id,
                "summary": summary.summary,
                "pros": summary.pros_cons.pros,
                "cons": summary.pros_cons.cons,
                "poi_summary": summary.poi_summary,
                "created_at": "NOW()"  # Use SQL function for timestamp
            }
            
            # Assuming you have a 'property_summaries' table
            client.table("property_summaries").insert(summary_data).execute()
            
            logger.info(f"Stored AI summary for property {property_id}")
            return True
        except Exception as e:
            logger.error(f"Error storing property summary: {str(e)}")
            return False
    
    async def generate_chat_name(self, messages: List[Dict[str, Any]], max_length: int = 30) -> str:
        """
        Generate a relevant chat name based on conversation messages using OpenAI.
        
        Args:
            messages: List of message dictionaries with 'role' and 'message' keys
            max_length: Maximum length for the generated name
            
        Returns:
            str: Generated chat name, fallback to default if error occurs
        """
        try:
            # Filter to only user messages for name generation
            user_messages = [msg for msg in messages if msg.get("role") == "user"]
            
            if not user_messages:
                return "Portfolio Chat"
            
            # Limit messages to avoid token limits (use latest 5 user messages)
            recent_messages = user_messages[-5:]
            
            # Create conversation context
            conversation_text = "\n".join([
                f"User: {msg.get('message', '')}" 
                for msg in recent_messages
            ])
            
            system_prompt = f"""
            # Real Estate Portfolio Dashboard Chat Name Generator
            - You are a real estate investment analysis AI.
            - Generate a concise chat title based on the conversation below. 
            The title should:
            - Capture the main topic/theme of the conversation
            - Be relevant to the real estate or property discussions
            - Avoid generic terms like "chat" or "conversation"
            - Be {max_length} characters or less
            - Use title case
            
            Return only the title, nothing else.
            """
            
            user_prompt = f"Conversation:\n{conversation_text}\n\nGenerate a title for this conversation:"
            
            response = client.chat.completions.create(
                model=self.model_name,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ]
            )
            
            generated_name = response.choices[0].message.content.strip()
            
            # Clean up the name
            generated_name = generated_name.replace('"', '').replace("'", '')
            
            # Fallback if empty or too short
            if not generated_name or len(generated_name) < 3:
                return "Portfolio Chat"
                
            logger.info(f"Generated chat name: {generated_name}")
            return generated_name
            
        except Exception as e:
            logger.error(f"Error generating chat name: {str(e)}")
            return "Portfolio Chat"

# Create a singleton instance
# Use cached getter for the check
ai_service = AIService()

# Removed empty if __name__ block for now, can be added back if needed
# if __name__ == "__main__":
#     pass # Or add test/example code here 