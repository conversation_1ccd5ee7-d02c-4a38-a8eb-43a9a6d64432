"""
Expense Calculation Service - Main service for calculating all property expenses.

This service orchestrates various expense calculations including property taxes,
insurance, repairs, maintenance, utilities, and professional fees.
"""
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
import re

from tavily import TavilyClient

from app.utils.logger import get_logger
from app.utils.env_cache import get_env
from app.services.property_valuation_service import PropertyValuationService, PropertyValuationContext
from app.services.property_tax_service import PropertyTaxService, PropertyTaxContext
from app.services.insurance_service import InsuranceService, InsuranceContext
from app.services.repairs_analysis_service import RepairsAnalysisService, RepairsAnalysisContext
from app.models.ai import FinancialResponse, Source
from app.services.professional_fees_service import ProfessionalFeesService, ProfessionalFeesContext

logger = get_logger(__name__)

@dataclass
class ExpenseCalculationContext:
    """Context for expense calculations."""
    prop_id: str
    portfolio_id: str
    zipcode: Optional[str] = None
    city: Optional[str] = None
    state: Optional[str] = None
    effective_gross_income: Optional[float] = None
    rental_income: Optional[float] = None

class ExpenseCalculationService:
    """Service for calculating all property expenses."""
    
    def __init__(self):
        """Initialize the expense calculation service."""
        self.valuation_service = PropertyValuationService()
        self.tax_service = PropertyTaxService()
        self.insurance_service = InsuranceService()
        self.repairs_service = RepairsAnalysisService()
        self.professional_fees_service = ProfessionalFeesService()
        self.tavily_client = TavilyClient(api_key=get_env('TAVILY_API_KEY'))
    
    async def calculate_property_value_estimate(self, context: ExpenseCalculationContext) -> Dict[str, Any]:
        """Estimate property value using cap rates and income."""
        try:
            valuation_context = PropertyValuationContext(
                prop_id=context.prop_id,
                portfolio_id=context.portfolio_id,
                zipcode=context.zipcode,
                city=context.city,
                state=context.state,
                effective_gross_income=context.effective_gross_income,
                rental_income=context.rental_income
            )
            
            valuation = await self.valuation_service.estimate_property_value(valuation_context)
            logger.info(f"Property value estimate: ${valuation.get('estimated_value', 0):.2f}")
            
            return valuation
            
        except Exception as e:
            logger.error(f"Error calculating property value estimate: {str(e)}")
            return {"estimated_value": 0, "error": str(e)}
    
    async def calculate_property_tax_expenses(
        self, 
        context: ExpenseCalculationContext, 
        estimated_value: Optional[float] = None
    ) -> Dict[str, FinancialResponse]:
        """Calculate property tax expenses."""
        try:
            tax_context = PropertyTaxContext(
                prop_id=context.prop_id,
                portfolio_id=context.portfolio_id,
                zipcode=context.zipcode,
                city=context.city,
                state=context.state,
                estimated_property_value=estimated_value,
                effective_gross_income=context.effective_gross_income
            )
            
            tax_projections = await self.tax_service.calculate_property_tax_projections(tax_context)
            logger.info(f"Property tax projections calculated: {len(tax_projections)} years")
            
            return tax_projections
            
        except Exception as e:
            logger.error(f"Error calculating property tax expenses: {str(e)}")
            return {}
    
    async def calculate_insurance_expenses(
        self, 
        context: ExpenseCalculationContext, 
        estimated_value: Optional[float] = None
    ) -> Dict[str, FinancialResponse]:
        """Calculate property insurance expenses."""
        try:
            insurance_context = InsuranceContext(
                prop_id=context.prop_id,
                portfolio_id=context.portfolio_id,
                zipcode=context.zipcode,
                city=context.city,
                state=context.state,
                estimated_property_value=estimated_value
            )
            
            insurance_projections = await self.insurance_service.calculate_insurance_projections(insurance_context)
            logger.info(f"Insurance projections calculated: {len(insurance_projections)} years")
            
            return insurance_projections
            
        except Exception as e:
            logger.error(f"Error calculating insurance expenses: {str(e)}")
            return {}
    
    async def calculate_professional_fees(
        self, 
        context: ExpenseCalculationContext, 
        rental_income: float
    ) -> Dict[str, Dict[str, FinancialResponse]]:
        """Calculate professional fees using AI analysis and market research."""
        try:
            # Import here to avoid circular imports
            from app.services.property_service import PropertyService
            
            # Get property type for better analysis
            property_data = await PropertyService.get_property(context.prop_id)
            property_type = getattr(property_data, 'property_type', None) if property_data else None
            
            # Create professional fees context
            fees_context = ProfessionalFeesContext(
                prop_id=context.prop_id,
                portfolio_id=context.portfolio_id,
                zipcode=context.zipcode,
                city=context.city,
                state=context.state,
                rental_income=rental_income,
                property_type=property_type,
                current_year=2024
            )
            
            # Use AI-based professional fees analysis
            professional_fees = await self.professional_fees_service.calculate_professional_fees_projections(fees_context)
            
            logger.info(f"AI-based professional fees calculated: {len(professional_fees)} fee types")
            return professional_fees
            
        except Exception as e:
            logger.error(f"Error calculating AI-based professional fees: {str(e)}")
            return {}
    
    async def calculate_utilities_estimate(
        self, 
        context: ExpenseCalculationContext
    ) -> Dict[str, FinancialResponse]:
        """Calculate utilities expenses based on property size and local rates."""
        try:
            # Import here to avoid circular imports
            from app.services.property_service import PropertyService
            
            # Get property units to calculate total square footage
            units = await PropertyService.get_property_units(context.prop_id)
            
            total_sqft = 0
            for unit in units:
                if unit.sqft:
                    total_sqft += unit.sqft
            
            # If no square footage data, estimate based on rental income or use default
            if total_sqft == 0:
                logger.warning("No square footage data found for utilities calculation, using fallback estimate")
                
                # Estimate square footage from rental income (rough approximation)
                if context.rental_income and context.rental_income > 0:
                    # Assume roughly $1.50-2.00 per sqft monthly rent
                    monthly_rental = context.rental_income / 12
                    estimated_sqft = monthly_rental / 1.75  # Conservative estimate
                    total_sqft = max(800, min(5000, estimated_sqft))  # Reasonable bounds
                    logger.info(f"Estimated {total_sqft:.0f} sqft from rental income of ${context.rental_income:.2f}/year")
                else:
                    # Use a reasonable default for small property
                    total_sqft = 1200
                    logger.info(f"Using default square footage estimate: {total_sqft} sqft")
            
            # Try to get local utility rates using Tavily
            utilities_per_sqft = 2.0  # Default fallback rate
            source_description = f"National average utilities cost at ${utilities_per_sqft:.2f}/sqft annually"
            source_name = "national_average_fallback"
            
            local_utility_data = await self.get_local_utility_rates(
                context.zipcode, 
                context.city, 
                context.state
            )
            
            if local_utility_data and local_utility_data.get('rates_found', 0) > 0:
                utilities_per_sqft = local_utility_data['average_rate']
                source_description = f"Local utilities analysis for {context.city or context.zipcode or context.state} at ${utilities_per_sqft:.2f}/sqft annually"
                source_name = "tavily_local_search"
                logger.info(f"Using local utility rate: ${utilities_per_sqft:.2f}/sqft from {local_utility_data['rates_found']} sources")
            else:
                logger.info(f"Using default utility rate: ${utilities_per_sqft:.2f}/sqft (no local data found)")
            
            base_utilities = total_sqft * utilities_per_sqft
            
            projections = {}
            
            # Calculate projections for years 1-5
            for year in range(1, 6):
                # Utilities typically increase 3-4% annually due to rate increases
                projected_utilities = base_utilities * ((1.035) ** (year - 1))  # 3.5% annual increase
                
                projection = FinancialResponse(
                    task_type="utilities",
                    portfolio_id=context.portfolio_id,
                    prop_id=context.prop_id,
                    year=year,
                    sources=[
                        Source(
                            source=source_name,
                            description=source_description,
                            url=local_utility_data.get('sources', ['internal_calculation'])[0] if local_utility_data.get('sources') else 'internal_calculation'
                        )
                    ],
                    value=utilities_per_sqft,  # Rate per sqft
                    unit="$/sqft",
                    money_value=-projected_utilities,  # Make negative since this is an expense
                    money_unit="USD",
                    description=f"Utilities expenses for Year {year}: water, sewer, trash, common areas (landlord-paid utilities)",
                    additional_data=None
                )
                
                projections[f"year_{year}"] = projection
                logger.info(f"Utilities Year {year}: ${projected_utilities:.2f} (${projected_utilities/12:.2f}/month)")
            
            return projections
            
        except Exception as e:
            logger.error(f"Error calculating utilities estimate: {str(e)}")
            return {}
    
    async def calculate_comprehensive_repairs_analysis(
        self, 
        context: ExpenseCalculationContext, 
        estimated_value: Optional[float] = None
    ) -> Dict[str, FinancialResponse]:
        """Calculate comprehensive repairs and maintenance expenses using property analysis."""
        try:
            # Import here to avoid circular imports
            from app.services.property_service import PropertyService
            
            # Get property details for analysis
            property_data = await PropertyService.get_property(context.prop_id)
            
            total_sqft = 0
            property_age = None
            property_type = None
            
            if property_data:
                # Extract property characteristics
                property_type = getattr(property_data, 'property_type', None)
                
                # Get property units to calculate total square footage
                units = await PropertyService.get_property_units(context.prop_id)
                for unit in units:
                    if hasattr(unit, 'sqft') and unit.sqft:
                        total_sqft += unit.sqft
                
                # Try to determine property age from PropertyDetails
                try:
                    # Query property details directly from the database
                    from app.repository.db import get_db_client
                    
                    client = await get_db_client()
                    details_response = client.table("prop_details").select("year_built").eq("prop_id", context.prop_id).execute()
                    
                    if details_response.data and len(details_response.data) > 0:
                        year_built = details_response.data[0].get('year_built')
                        if year_built:
                            from datetime import datetime
                            current_year = datetime.now().year
                            property_age = current_year - int(year_built)
                            logger.info(f"Property age determined: {property_age} years (built in {year_built})")
                except Exception as e:
                    logger.warning(f"Could not determine property age: {str(e)}")
                    property_age = None
            
            # Create repairs analysis context
            repairs_context = RepairsAnalysisContext(
                prop_id=context.prop_id,
                portfolio_id=context.portfolio_id,
                estimated_property_value=estimated_value,
                property_age=property_age,
                sqft=total_sqft,
                property_type=property_type
            )
            
            # Calculate repairs projections using comprehensive analysis
            repairs_projections = await self.repairs_service.calculate_repairs_projections(repairs_context)
            
            logger.info(f"Comprehensive repairs analysis completed: {len(repairs_projections)} projections")
            return repairs_projections
            
        except Exception as e:
            logger.error(f"Error calculating comprehensive repairs analysis: {str(e)}")
            return {}
    
    async def calculate_all_expenses(self, context: ExpenseCalculationContext, return_projections: bool = False) -> List[FinancialResponse]:
        """Calculate all property expenses and return as a list of FinancialResponse objects."""
        try:
            logger.info(f"Starting comprehensive expense calculation for property {context.prop_id}")
            
            # Step 1: Estimate property value
            valuation = await self.calculate_property_value_estimate(context)
            estimated_value = valuation.get('estimated_value', 0)
            
            # Initialize results list
            expense_responses = []
            
            # Step 2: Calculate property tax expenses
            tax_projections = await self.calculate_property_tax_expenses(context, estimated_value)
            if tax_projections:
                year_1_projection = tax_projections.get('year_1')
                if year_1_projection:
                    if return_projections:
                        # Add years 2-5 to additional_data
                        other_years = {k: v for k, v in tax_projections.items() if k != 'year_1'}
                        year_1_projection.additional_data = other_years if other_years else None
                    else:
                        # No projections requested, clear additional_data
                        year_1_projection.additional_data = None
                    expense_responses.append(year_1_projection)
            
            # Step 3: Calculate insurance expenses
            insurance_projections = await self.calculate_insurance_expenses(context, estimated_value)
            if insurance_projections:
                year_1_projection = insurance_projections.get('year_1')
                if year_1_projection:
                    if return_projections:
                        # Add years 2-5 to additional_data
                        other_years = {k: v for k, v in insurance_projections.items() if k != 'year_1'}
                        year_1_projection.additional_data = other_years if other_years else None
                    else:
                        # No projections requested, clear additional_data
                        year_1_projection.additional_data = None
                    expense_responses.append(year_1_projection)
            
            # Step 4: Calculate utilities expenses
            utilities_projections = await self.calculate_utilities_estimate(context)
            if utilities_projections:
                year_1_projection = utilities_projections.get('year_1')
                if year_1_projection:
                    if return_projections:
                        # Add years 2-5 to additional_data
                        other_years = {k: v for k, v in utilities_projections.items() if k != 'year_1'}
                        year_1_projection.additional_data = other_years if other_years else None
                    else:
                        # No projections requested, clear additional_data
                        year_1_projection.additional_data = None
                    expense_responses.append(year_1_projection)
            
            # Step 5: Calculate professional fees if rental income is available
            if context.rental_income and context.rental_income > 0:
                professional_fees = await self.calculate_professional_fees(context, context.rental_income)
                
                for fee_type, fee_projections in professional_fees.items():
                    if fee_projections:
                        year_1_projection = fee_projections.get('year_1')
                        if year_1_projection:
                            if return_projections:
                                # Add years 2-5 to additional_data
                                other_years = {k: v for k, v in fee_projections.items() if k != 'year_1'}
                                year_1_projection.additional_data = other_years if other_years else None
                            else:
                                # No projections requested, clear additional_data
                                year_1_projection.additional_data = None
                            expense_responses.append(year_1_projection)
            
            # Step 6: Calculate comprehensive repairs analysis
            repairs_projections = await self.calculate_comprehensive_repairs_analysis(context, estimated_value)
            if repairs_projections:
                year_1_projection = repairs_projections.get('year_1')
                if year_1_projection:
                    if return_projections:
                        # Add years 2-5 to additional_data
                        other_years = {k: v for k, v in repairs_projections.items() if k != 'year_1'}
                        year_1_projection.additional_data = other_years if other_years else None
                    else:
                        # No projections requested, clear additional_data
                        year_1_projection.additional_data = None
                    expense_responses.append(year_1_projection)
            
            # Step 7: Calculate maintenance expenses (separate from repairs)
            maintenance_projections = await self.calculate_maintenance_expenses(context, estimated_value)
            if maintenance_projections:
                year_1_projection = maintenance_projections.get('year_1')
                if year_1_projection:
                    if return_projections:
                        other_years = {k: v for k, v in maintenance_projections.items() if k != 'year_1'}
                        year_1_projection.additional_data = other_years if other_years else None
                    else:
                        year_1_projection.additional_data = None
                    expense_responses.append(year_1_projection)
            
            # Step 8: Calculate service expenses (landscaping, cleaning, security)
            services_projections = await self.calculate_services_expenses(context, estimated_value)
            if services_projections:
                year_1_projection = services_projections.get('year_1')
                if year_1_projection:
                    if return_projections:
                        other_years = {k: v for k, v in services_projections.items() if k != 'year_1'}
                        year_1_projection.additional_data = other_years if other_years else None
                    else:
                        year_1_projection.additional_data = None
                    expense_responses.append(year_1_projection)
            
            # Step 9: Calculate reserves for capital expenditures
            reserves_projections = await self.calculate_reserves_expenses(context, estimated_value)
            if reserves_projections:
                year_1_projection = reserves_projections.get('year_1')
                if year_1_projection:
                    if return_projections:
                        other_years = {k: v for k, v in reserves_projections.items() if k != 'year_1'}
                        year_1_projection.additional_data = other_years if other_years else None
                    else:
                        year_1_projection.additional_data = None
                    expense_responses.append(year_1_projection)

            logger.info(f"Expense calculation completed for property {context.prop_id}: {len(expense_responses)} expense items")
            return expense_responses
            
        except Exception as e:
            logger.error(f"Error calculating all expenses: {str(e)}")
            return []
    
    async def get_local_utility_rates(self, zipcode: Optional[str], city: Optional[str], state: Optional[str]) -> Dict[str, Any]:
        """Get local utility rates using Tavily search."""
        try:
            location_parts = []
            if city and state:
                location_parts.append(f"{city}, {state}")
            elif zipcode:
                location_parts.append(f"ZIP code {zipcode}")
            elif state:
                location_parts.append(state)
            
            if not location_parts:
                logger.warning("No location information provided for utility rates search")
                return {}
            
            location_str = " ".join(location_parts)
            
            # Search using Tavily
            response = self.tavily_client.search(
                query=f"average utility costs rental properties landlord paid utilities {location_str} 2024",
                search_depth="advanced",
                max_results=5
            )
            
            if not response.get('results'):
                logger.warning(f"No utility rate results found for {location_str}")
                return {}
            
            # Parse utility rates from search results
            utility_data = {"rates_found": 0, "average_rate": 0, "sources": []}
            rate_pattern = r'\$?(\d+(?:\.\d{2})?)\s*(?:per\s+square\s+foot|per\s+sqft|\/sqft|\/sq\s*ft)'
            annual_pattern = r'\$?(\d+(?:,\d{3})*(?:\.\d{2})?)\s*(?:per\s+year|annually|yearly)'
            monthly_pattern = r'\$?(\d+(?:,\d{3})*(?:\.\d{2})?)\s*(?:per\s+month|monthly)'
            
            found_rates = []
            
            for result in response['results']:
                content = f"{result.get('title', '')} {result.get('content', '')}"
                
                # Look for per-sqft rates
                sqft_matches = re.finditer(rate_pattern, content, re.IGNORECASE)
                for match in sqft_matches:
                    try:
                        rate = float(match.group(1))
                        if 0.5 <= rate <= 10:  # Reasonable range for $/sqft annually
                            found_rates.append(rate)
                            utility_data["sources"].append(result.get('url', ''))
                    except ValueError:
                        continue
                
                # Look for annual amounts and convert to per-sqft (assuming typical property size)
                annual_matches = re.finditer(annual_pattern, content, re.IGNORECASE)
                for match in annual_matches:
                    try:
                        annual_amount = float(match.group(1).replace(',', ''))
                        if 1000 <= annual_amount <= 20000:  # Reasonable range for annual utilities
                            # Convert to per-sqft assuming 1000 sqft average
                            rate_per_sqft = annual_amount / 1000
                            if 0.5 <= rate_per_sqft <= 10:
                                found_rates.append(rate_per_sqft)
                                utility_data["sources"].append(result.get('url', ''))
                    except ValueError:
                        continue
                
                # Look for monthly amounts and convert
                monthly_matches = re.finditer(monthly_pattern, content, re.IGNORECASE)
                for match in monthly_matches:
                    try:
                        monthly_amount = float(match.group(1).replace(',', ''))
                        if 50 <= monthly_amount <= 1000:  # Reasonable range for monthly utilities
                            # Convert to annual per-sqft assuming 1000 sqft average
                            annual_amount = monthly_amount * 12
                            rate_per_sqft = annual_amount / 1000
                            if 0.5 <= rate_per_sqft <= 10:
                                found_rates.append(rate_per_sqft)
                                utility_data["sources"].append(result.get('url', ''))
                    except ValueError:
                        continue
            
            if found_rates:
                utility_data["average_rate"] = sum(found_rates) / len(found_rates)
                utility_data["rates_found"] = len(found_rates)
                utility_data["source"] = "tavily_search"
                utility_data["description"] = f"Utility rate analysis for {location_str}"
            
            logger.info(f"Found utility rate data: {utility_data}")
            return utility_data
            
        except Exception as e:
            logger.error(f"Error getting local utility rates: {str(e)}")
            return {}
    
    async def calculate_maintenance_expenses(self, context: ExpenseCalculationContext, estimated_value: Optional[float] = None) -> Dict[str, FinancialResponse]:
        """Calculate ongoing maintenance expenses (separate from major repairs)."""
        try:
            projections = {}
            
            # Maintenance is typically 0.5-1% of property value annually
            property_value = estimated_value or 0
            if property_value <= 0 and context.rental_income:
                # Estimate value from income (10x multiplier)
                property_value = context.rental_income * 10
            
            if property_value > 0:
                # Conservative maintenance estimate: 0.75% of property value
                annual_maintenance_rate = 0.0075
                base_maintenance = property_value * annual_maintenance_rate
                
                # Project with modest annual increase
                annual_increase = 0.03  # 3% annual increase
                
                for year in range(1, 6):
                    projected_maintenance = base_maintenance * ((1 + annual_increase) ** (year - 1))
                    
                    projection = FinancialResponse(
                        task_type="maintenance",
                        portfolio_id=context.portfolio_id,
                        prop_id=context.prop_id,
                        year=year,
                        sources=[
                            Source(
                                source="maintenance_analysis",
                                description="Ongoing maintenance costs separate from major repairs",
                                url="industry_standards"
                            )
                        ],
                        value=annual_maintenance_rate * 100,  # Rate as percentage
                        unit="%",
                        money_value=-projected_maintenance,
                        money_unit="USD",
                        description=f"Ongoing maintenance expenses for Year {year}",
                        additional_data=None
                    )
                    
                    projections[f"year_{year}"] = projection
                    logger.info(f"Maintenance Year {year}: ${projected_maintenance:.2f}")
            
            return projections
            
        except Exception as e:
            logger.error(f"Error calculating maintenance expenses: {str(e)}")
            return {}
    
    async def calculate_services_expenses(self, context: ExpenseCalculationContext, estimated_value: Optional[float] = None) -> Dict[str, FinancialResponse]:
        """Calculate services expenses (landscaping, cleaning, security, etc.)."""
        try:
            projections = {}
            
            # Services typically vary by property type and size
            property_value = estimated_value or 0
            if property_value <= 0 and context.rental_income:
                property_value = context.rental_income * 10
            
            if property_value > 0:
                # Services estimate: 0.3% of property value annually
                annual_services_rate = 0.003
                base_services = property_value * annual_services_rate
                
                # Project with annual increase
                annual_increase = 0.04  # 4% annual increase (labor costs rise)
                
                for year in range(1, 6):
                    projected_services = base_services * ((1 + annual_increase) ** (year - 1))
                    
                    projection = FinancialResponse(
                        task_type="services",
                        portfolio_id=context.portfolio_id,
                        prop_id=context.prop_id,
                        year=year,
                        sources=[
                            Source(
                                source="services_analysis",
                                description="Landscaping, cleaning, security and other contracted services",
                                url="industry_standards"
                            )
                        ],
                        value=annual_services_rate * 100,  # Rate as percentage
                        unit="%",
                        money_value=-projected_services,
                        money_unit="USD",
                        description=f"Services expenses for Year {year}: landscaping, cleaning, security",
                        additional_data=None
                    )
                    
                    projections[f"year_{year}"] = projection
                    logger.info(f"Services Year {year}: ${projected_services:.2f}")
            
            return projections
            
        except Exception as e:
            logger.error(f"Error calculating services expenses: {str(e)}")
            return {}
    
    async def calculate_reserves_expenses(self, context: ExpenseCalculationContext, estimated_value: Optional[float] = None) -> Dict[str, FinancialResponse]:
        """Calculate capital reserves for future major expenditures."""
        try:
            projections = {}
            
            if context.rental_income and context.rental_income > 0:
                # Conservative reserves estimate: 1.5% of property value

                base_reserves = context.rental_income / 12
                
                # Reserves remain relatively stable but may increase slightly with property age
                annual_increase = 0.02  # 2% annual increase
                
                for year in range(1, 6):
                    projected_reserves = base_reserves * ((1 + annual_increase) ** (year - 1))
                    
                    projection = FinancialResponse(
                        task_type="reserves",
                        portfolio_id=context.portfolio_id,
                        prop_id=context.prop_id,
                        year=year,
                        sources=[
                            Source(
                                source="capital_reserves_analysis",
                                description="Capital reserves for major future expenditures (roof, HVAC, flooring)",
                                url="industry_standards"
                            )
                        ],
                        value=base_reserves/context.rental_income * 100,  # Rate as percentage
                        unit="%",
                        money_value=-projected_reserves,
                        money_unit="USD",
                        description=f"Capital reserves allocation for future capital improvement and replacement costs for Year {year}",
                        additional_data=None
                    )
                    
                    projections[f"year_{year}"] = projection
                    logger.info(f"Reserves Year {year}: ${projected_reserves:.2f}")
            
            return projections
            
        except Exception as e:
            logger.error(f"Error calculating reserves expenses: {str(e)}")
            return {} 